import 'dart:developer';

import 'package:shared_preferences/shared_preferences.dart';

class SharedPrefsHelper {
  static final SharedPrefsHelper _instance = SharedPrefsHelper._internal();
  factory SharedPrefsHelper() => _instance;
  SharedPrefsHelper._internal();

  SharedPreferences? _prefs;

  Future<SharedPreferences> get prefs async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs!;
  }

  // String
  Future<void> setString(String key, String value) async {
    final p = await prefs;
    // log(value);
    log("00000000000  $value");
    await p.setString(key, value);
  }

  Future<String?> getString(String key) async {
    final p = await prefs;
    return p.getString(key);
  }


  // Remove
  Future<void> remove(String key) async {
    final p = await prefs;
    await p.remove(key);
  }

  // Clear all
  Future<void> clear() async {
    final p = await prefs;
    await p.clear();
  }
} 