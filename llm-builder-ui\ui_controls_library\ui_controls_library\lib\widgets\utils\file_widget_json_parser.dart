import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';

/// Utility class for parsing JSON configuration for FileWidget
class FileWidgetJsonParser {
  /// Parse colors from JSON values
  static Color? parseColor(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          return null;
        }
      }

      // Handle named colors
      return _parseNamedColor(colorValue.toLowerCase());
    } else if (colorValue is int) {
      return Color(colorValue);
    }

    return null;
  }

  /// Parse named colors
  static Color? _parseNamedColor(String colorName) {
    switch (colorName) {
      case 'red': return Colors.red;
      case 'blue': return Colors.blue;
      case 'green': return Colors.green;
      case 'yellow': return Colors.yellow;
      case 'orange': return Colors.orange;
      case 'purple': return Colors.purple;
      case 'pink': return Colors.pink;
      case 'brown': return Colors.brown;
      case 'grey':
      case 'gray': return Colors.grey;
      case 'black': return Colors.black;
      case 'white': return Colors.white;
      case 'amber': return Colors.amber;
      case 'cyan': return Colors.cyan;
      case 'indigo': return Colors.indigo;
      case 'lime': return Colors.lime;
      case 'teal': return Colors.teal;
      default: return null;
    }
  }

  /// Parse text alignment from JSON
  static TextAlign parseTextAlign(dynamic alignValue) {
    if (alignValue == null) return TextAlign.start;

    if (alignValue is String) {
      switch (alignValue.toLowerCase()) {
        case 'center': return TextAlign.center;
        case 'end':
        case 'right': return TextAlign.end;
        case 'start':
        case 'left': return TextAlign.start;
        case 'justify': return TextAlign.justify;
        default: return TextAlign.start;
      }
    }

    return TextAlign.start;
  }

  /// Parse font weight from JSON
  static FontWeight parseFontWeight(dynamic weightValue) {
    if (weightValue == null) return FontWeight.normal;

    if (weightValue is String) {
      switch (weightValue.toLowerCase()) {
        case 'bold': return FontWeight.bold;
        case 'normal': return FontWeight.normal;
        case 'light': return FontWeight.w300;
        default: return FontWeight.normal;
      }
    } else if (weightValue is int) {
      switch (weightValue) {
        case 100: return FontWeight.w100;
        case 200: return FontWeight.w200;
        case 300: return FontWeight.w300;
        case 400: return FontWeight.w400;
        case 500: return FontWeight.w500;
        case 600: return FontWeight.w600;
        case 700: return FontWeight.w700;
        case 800: return FontWeight.w800;
        case 900: return FontWeight.w900;
        default: return FontWeight.normal;
      }
    } else if (weightValue is bool && weightValue) {
      return FontWeight.bold;
    }

    return FontWeight.normal;
  }

  /// Parse icon data from JSON
  static IconData? parseIconData(dynamic iconValue) {
    if (iconValue == null) return null;

    if (iconValue is String) {
      return _parseNamedIcon(iconValue.toLowerCase());
    }

    return null;
  }

  /// Parse named icons
  static IconData? _parseNamedIcon(String iconName) {
    switch (iconName) {
      case 'upload_file': return Icons.upload_file;
      case 'file_upload': return Icons.file_upload;
      case 'attach_file': return Icons.attach_file;
      case 'cloud_upload': return Icons.cloud_upload;
      case 'add': return Icons.add;
      case 'add_circle': return Icons.add_circle;
      case 'add_circle_outline': return Icons.add_circle_outline;
      case 'folder': return Icons.folder;
      case 'folder_open': return Icons.folder_open;
      case 'description': return Icons.description;
      case 'insert_drive_file': return Icons.insert_drive_file;
      case 'picture_as_pdf': return Icons.picture_as_pdf;
      case 'image': return Icons.image;
      case 'photo': return Icons.photo;
      case 'video_file': return Icons.video_file;
      case 'audio_file': return Icons.audio_file;
      case 'text_snippet': return Icons.text_snippet;
      case 'table_chart': return Icons.table_chart;
      case 'slideshow': return Icons.slideshow;
      case 'folder_zip': return Icons.folder_zip;
      default: return null;
    }
  }

  /// Parse edge insets from JSON
  static EdgeInsetsGeometry parseEdgeInsets(dynamic insetsValue) {
    if (insetsValue == null) {
      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
    }

    if (insetsValue is Map<String, dynamic>) {
      final double left = (insetsValue['left'] as num?)?.toDouble() ?? 0.0;
      final double top = (insetsValue['top'] as num?)?.toDouble() ?? 0.0;
      final double right = (insetsValue['right'] as num?)?.toDouble() ?? 0.0;
      final double bottom = (insetsValue['bottom'] as num?)?.toDouble() ?? 0.0;

      if (insetsValue.containsKey('all')) {
        final double all = (insetsValue['all'] as num).toDouble();
        return EdgeInsets.all(all);
      } else if (insetsValue.containsKey('horizontal') ||
          insetsValue.containsKey('vertical')) {
        final double horizontal =
            (insetsValue['horizontal'] as num?)?.toDouble() ?? 0.0;
        final double vertical =
            (insetsValue['vertical'] as num?)?.toDouble() ?? 0.0;
        return EdgeInsets.symmetric(
          horizontal: horizontal,
          vertical: vertical,
        );
      } else {
        return EdgeInsets.fromLTRB(left, top, right, bottom);
      }
    } else if (insetsValue is num) {
      return EdgeInsets.all(insetsValue.toDouble());
    }

    return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
  }

  /// Parse file type from JSON
  static FileType parseFileType(dynamic fileTypeValue) {
    if (fileTypeValue == null) return FileType.any;

    if (fileTypeValue is String) {
      switch (fileTypeValue.toLowerCase()) {
        case 'any': return FileType.any;
        case 'image': return FileType.image;
        case 'video': return FileType.video;
        case 'audio': return FileType.audio;
        case 'media': return FileType.media;
        case 'custom': return FileType.custom;
        default: return FileType.any;
      }
    }

    return FileType.any;
  }

  /// Parse string list from JSON
  static List<String>? parseStringList(dynamic listValue) {
    if (listValue == null) return null;

    if (listValue is List) {
      return List<String>.from(listValue.map((e) => e.toString()));
    } else if (listValue is String) {
      // Handle comma-separated string
      return listValue.split(',').map((e) => e.trim()).toList();
    }

    return null;
  }

  /// Parse JSON callbacks
  static Map<String, dynamic> parseJsonCallbacks(Map<String, dynamic> json) {
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks =
              jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse additional callback properties for specific events
    final callbackEvents = ['onFilesSelected', 'onClear', 'onUpload', 'onTap'];
    for (final event in callbackEvents) {
      if (json[event] != null) {
        jsonCallbacks ??= {};
        jsonCallbacks[event] = json[event];
        useJsonCallbacks = true;
      }
    }

    return {
      'jsonCallbacks': jsonCallbacks,
      'useJsonCallbacks': useJsonCallbacks,
    };
  }

  /// Parse boolean with default value
  static bool parseBool(dynamic value, {bool defaultValue = false}) {
    if (value is bool) return value;
    if (value is String) {
      return value.toLowerCase() == 'true';
    }
    return defaultValue;
  }

  /// Parse double with default value
  static double parseDouble(dynamic value, {double defaultValue = 0.0}) {
    if (value is num) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }

  /// Parse int with default value
  static int parseInt(dynamic value, {int defaultValue = 0}) {
    if (value is num) return value.toInt();
    if (value is String) {
      return int.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }
}
