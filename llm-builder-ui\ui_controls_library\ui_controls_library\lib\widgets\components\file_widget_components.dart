import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as path;

/// Components for FileWidget display and interaction
class FileWidgetComponents {
  /// Get file icon based on file extension
  static IconData getFileIcon(PlatformFile file) {
    final extension = path.extension(file.name).toLowerCase();
    
    if (extension == '.pdf') {
      return Icons.picture_as_pdf;
    } else if (['.doc', '.docx'].contains(extension)) {
      return Icons.description;
    } else if (['.xls', '.xlsx', '.csv'].contains(extension)) {
      return Icons.table_chart;
    } else if (['.ppt', '.pptx'].contains(extension)) {
      return Icons.slideshow;
    } else if (['.txt', '.rtf'].contains(extension)) {
      return Icons.text_snippet;
    } else if (['.zip', '.rar', '.7z'].contains(extension)) {
      return Icons.archive;
    } else if (['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'].contains(extension)) {
      return Icons.image;
    } else if (['.mp4', '.avi', '.mov', '.wmv', '.flv'].contains(extension)) {
      return Icons.video_file;
    } else if (['.mp3', '.wav', '.flac', '.aac'].contains(extension)) {
      return Icons.audio_file;
    } else {
      return Icons.insert_drive_file;
    }
  }

  /// Format file size in human readable format
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Build file preview widget
  static Widget buildFilePreview(PlatformFile file, double borderRadius) {
    final extension = path.extension(file.name).toLowerCase();

    // Check if it's an image file and try to show preview
    if (['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'].contains(extension) &&
        file.path != null) {
      try {
        return ClipRRect(
          borderRadius: BorderRadius.circular(borderRadius),
          child: Image.file(
            File(file.path!),
            width: 60,
            height: 60,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return _buildFileIconContainer(file, borderRadius);
            },
          ),
        );
      } catch (e) {
        return _buildFileIconContainer(file, borderRadius);
      }
    }

    return _buildFileIconContainer(file, borderRadius);
  }

  /// Build file icon container
  static Widget _buildFileIconContainer(PlatformFile file, double borderRadius) {
    final iconData = getFileIcon(file);
    Color iconColor;

    final extension = path.extension(file.name).toLowerCase();
    if (extension == '.pdf') {
      iconColor = Colors.red;
    } else if (['.doc', '.docx'].contains(extension)) {
      iconColor = Colors.blue;
    } else if (['.xls', '.xlsx', '.csv'].contains(extension)) {
      iconColor = Colors.green;
    } else if (['.ppt', '.pptx'].contains(extension)) {
      iconColor = Colors.orange;
    } else if (['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'].contains(extension)) {
      iconColor = Colors.purple;
    } else if (['.mp4', '.avi', '.mov', '.wmv', '.flv'].contains(extension)) {
      iconColor = Colors.indigo;
    } else if (['.mp3', '.wav', '.flac', '.aac'].contains(extension)) {
      iconColor = Colors.teal;
    } else {
      iconColor = Colors.grey;
    }

    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: iconColor.withAlpha(25),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Center(child: Icon(iconData, color: iconColor, size: 30)),
    );
  }

  /// Build file info widget
  static Widget buildFileInfo({
    required PlatformFile file,
    required bool showFileName,
    required bool showFileSize,
    required bool showFileType,
    required Color textColor,
    required double fontSize,
    required FontWeight fontWeight,
    required bool isDarkTheme,
  }) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showFileName)
            Text(
              file.name,
              style: TextStyle(
                color: textColor,
                fontSize: fontSize,
                fontWeight: fontWeight,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          if (showFileSize || showFileType)
            Row(
              children: [
                if (showFileSize)
                  Text(
                    formatFileSize(file.size),
                    style: TextStyle(
                      color: isDarkTheme ? Colors.white70 : Colors.grey.shade600,
                      fontSize: fontSize * 0.8,
                    ),
                  ),
                if (showFileSize && showFileType)
                  Text(
                    ' • ',
                    style: TextStyle(
                      color: isDarkTheme ? Colors.white70 : Colors.grey.shade600,
                      fontSize: fontSize * 0.8,
                    ),
                  ),
                if (showFileType)
                  Text(
                    path.extension(file.name).toUpperCase().replaceFirst('.', ''),
                    style: TextStyle(
                      color: isDarkTheme ? Colors.white70 : Colors.grey.shade600,
                      fontSize: fontSize * 0.8,
                    ),
                  ),
              ],
            ),
        ],
      ),
    );
  }

  /// Build selected files list
  static Widget buildSelectedFilesList({
    required List<PlatformFile> selectedFiles,
    required bool showFileName,
    required bool showFileSize,
    required bool showFileType,
    required bool showClearButton,
    required bool isDisabled,
    required bool isReadOnly,
    required Color textColor,
    required double fontSize,
    required FontWeight fontWeight,
    required bool isDarkTheme,
    required double borderRadius,
    required Function(int) onRemoveFile,
  }) {
    if (selectedFiles.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 8),
        ...List.generate(selectedFiles.length, (index) {
          final file = selectedFiles[index];
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isDarkTheme ? Colors.grey.shade700 : Colors.grey.shade100,
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                color: isDarkTheme ? Colors.grey.shade600 : Colors.grey.shade300,
              ),
            ),
            child: Row(
              children: [
                buildFilePreview(file, borderRadius),
                const SizedBox(width: 12),
                buildFileInfo(
                  file: file,
                  showFileName: showFileName,
                  showFileSize: showFileSize,
                  showFileType: showFileType,
                  textColor: textColor,
                  fontSize: fontSize,
                  fontWeight: fontWeight,
                  isDarkTheme: isDarkTheme,
                ),
                if (showClearButton && !isDisabled && !isReadOnly)
                  IconButton(
                    icon: const Icon(Icons.close, size: 18),
                    onPressed: () => onRemoveFile(index),
                    tooltip: 'Remove file',
                    color: isDarkTheme ? Colors.red.shade300 : Colors.red,
                  ),
              ],
            ),
          );
        }),
      ],
    );
  }

  /// Build progress bar
  static Widget buildProgressBar({
    required bool isUploading,
    required bool showProgressBar,
    required double uploadProgress,
    required bool isDarkTheme,
    required double fontSize,
  }) {
    if (!isUploading || !showProgressBar) return const SizedBox.shrink();

    return Column(
      children: [
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: uploadProgress,
          backgroundColor: isDarkTheme ? Colors.grey.shade700 : Colors.grey.shade300,
          valueColor: AlwaysStoppedAnimation<Color>(
            isDarkTheme ? Colors.blue.shade300 : Colors.blue,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Uploading... ${(uploadProgress * 100).toInt()}%',
          style: TextStyle(
            color: isDarkTheme ? Colors.white70 : Colors.black54,
            fontSize: fontSize * 0.8,
          ),
        ),
      ],
    );
  }

  /// Build drag and drop area
  static Widget buildDragDropArea({
    required Widget child,
    required bool allowDragDrop,
    required bool isDragging,
    required bool isDarkTheme,
    required double borderRadius,
  }) {
    if (!allowDragDrop) return child;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        border: isDragging
            ? Border.all(
                color: isDarkTheme ? Colors.blue.shade300 : Colors.blue,
                width: 2,
                style: BorderStyle.solid,
              )
            : null,
      ),
      child: child,
    );
  }
}
