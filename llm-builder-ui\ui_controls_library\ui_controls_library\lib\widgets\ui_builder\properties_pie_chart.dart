import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:ui_controls_library/widgets/ui_builder/flexible_widget_serializer.dart';

import 'common/config.dart';

class RingPieChartUIBuilder extends StatefulWidget {
  final Function onPressed;
  final ChartSizeConfig config;
  final Map<String, double>? dataMap;
  final List<Color>? colorList;

  const RingPieChartUIBuilder({
    super.key,
    required this.onPressed,
    required this.config,
    this.dataMap,
    this.colorList,
  });

  @override
  State<RingPieChartUIBuilder> createState() => RingPieChartUIBuilderState();
}

class RingPieChartUIBuilderState extends State<RingPieChartUIBuilder> {
  late Widget screenWidgetTree;
  String jsonOutput = '';
  Widget? deserializedWidget;

  Map<String, double> get effectiveDataMap =>
      widget.dataMap ??
      {
        'Point 1': 1,
        'Point 2': 1,
        'Point 3': 1,
        'Point 4': 1,
        'Point 5': 1,
        'Point 6': 1,
        'Point 7': 1,
        'Point 8': 1,
      };

  List<Color> get effectiveColorList =>
      widget.colorList ??
      [
        const Color(0xFF0D47A1),
        const Color(0xFF1565C0),
        const Color(0xFF1976D2),
        const Color(0xFF1E88E5),
        const Color(0xFF42A5F5),
        const Color(0xFF64B5F6),
        const Color(0xFF90CAF9),
        const Color(0xFFBBDEFB),
      ];

  Widget buildChartSection({required ChartSizeConfig c}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          c.size?.name.toUpperCase() ?? "",
          style: const TextStyle(fontSize: 24),
        ),
        Row(
          children: [
            Expanded(
              flex: 10,
              child: Card(
                elevation: c.elevation,
                // shadowColor: Colors.black,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(c.borderRadius),
                  side: BorderSide(
                    color: const Color.fromARGB(255, 237, 235, 235),
                    width: c.borderThikness ?? 0,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Temperature theme",
                        style: TextStyle(
                          fontSize: c.bodyFontSize,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Show different layouts based on chart type
                      c.chartType == ChartType.bubble
                          ? _buildChart(
                            c,
                          ) // Bubble chart has its own legend built-in
                          : Row(
                            children: [
                              Expanded(child: _buildChart(c)),
                              // SfCircularChart(
                              //           title: ChartTitle(text: 'Sales by Category'),
                              //           legend: const Legend(
                              //             isVisible: true,
                              //             overflowMode: LegendItemOverflowMode.wrap,
                              //           ),
                              //           tooltipBehavior: TooltipBehavior(enable: true),
                              //           series: <PieSeries<_ChartData, String>>[
                              //             PieSeries<_ChartData, String>(
                              //               radius: "100%",
                              //               dataSource: _getChartData(),
                              //               xValueMapper: (_ChartData data, _) => data.category,
                              //               yValueMapper: (_ChartData data, _) => data.value,
                              //               dataLabelMapper: (_ChartData data, _) => '${data.category}: ${data.value}',
                              //               dataLabelSettings: const DataLabelSettings(isVisible: true),
                              //             )
                              //           ],
                              //         ),

                              //   child: PieChart(
                              //   dataMap: dataMap,
                              //   colorList: colorList,
                              //   chartType: c.chartType,
                              //   chartRadius: c.chartRadius ?? 80,
                              //   ringStrokeWidth: (c.chartRadius ?? 80) * 0.2,
                              //   centerWidget:c.chartType==ChartType.ring? Text(
                              //     "Total Value\n\$9,999.99",
                              //     textAlign: TextAlign.center,
                              //     style: TextStyle(
                              //       fontWeight: FontWeight.bold,
                              //       fontSize: (c.chartRadius ?? 80) / 8,
                              //     ),
                              //   ):null,
                              //   legendOptions: const LegendOptions(showLegends: false),
                              //   chartValuesOptions: const ChartValuesOptions(showChartValues: false),
                              // ),
                              Expanded(
                                child: buildLegend(c.labelFontSize ?? 0),
                              ),
                            ],
                          ),
                    ],
                  ),
                ),
              ),
            ),
            const Expanded(
              // flex: 15,
              child: SizedBox(width: 20),
            ),
          ],
        ),
        const SizedBox(height: 24),
        Text(
          "Properties",
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: c.bodyFontSize,
            color: Colors.black,
          ),
        ),
        Text(
          "Heading 1 Medium ${c.headingFontSize?.toInt()}",
          style: TextStyle(
            fontSize: c.headingFontSize,
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          "Body 1 Regular ${c.bodyFontSize?.toInt()}",
          style: TextStyle(fontSize: c.bodyFontSize),
        ),
        Text(
          "Label - Regular ${c.labelFontSize?.toInt()}",
          style: TextStyle(fontSize: c.labelFontSize, color: Colors.grey[700]),
        ),
      ],
    );
  }

  Widget _buildChart(ChartSizeConfig c) {
    // Calculate dynamic radius based on chart size
    String outerRadius = _getOuterRadius(c.size);
    String innerRadius = _getInnerRadius(c.size);

    switch (c.chartType) {
      case ChartType.ring:
        // Build Donut Chart
        return SfCircularChart(
          legend: const Legend(
            isVisible: false,
            overflowMode: LegendItemOverflowMode.wrap,
          ),
          tooltipBehavior: TooltipBehavior(enable: false),
          series: <DoughnutSeries<_ChartData, String>>[
            DoughnutSeries<_ChartData, String>(
              animationDuration: 0,
              dataSource: _getChartData(),
              xValueMapper: (data, _) => data.category,
              yValueMapper: (data, _) => data.value,
              pointColorMapper:
                  (data, index) =>
                      effectiveColorList[index % effectiveColorList.length],
              dataLabelMapper: (data, _) => '${data.category}: ${data.value}%',
              dataLabelSettings: const DataLabelSettings(isVisible: false),
              radius: outerRadius,
              innerRadius: innerRadius,
            ),
          ],
          annotations: <CircularChartAnnotation>[
            CircularChartAnnotation(
              widget: Text(
                "Total Value\n\$9,999.99",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: (c.chartRadius ?? 80) / 8,
                ),
              ),
            ),
          ],
        );

      case ChartType.disc:
        // Build Pie Chart
        return SfCircularChart(
          key: const ValueKey('no_animation_chart'),
          legend: const Legend(isVisible: false),
          tooltipBehavior: TooltipBehavior(enable: false),
          series: <PieSeries<_ChartData, String>>[
            PieSeries<_ChartData, String>(
              animationDuration: 0,
              dataSource: _getChartData(),
              xValueMapper: (data, _) => data.category,
              yValueMapper: (data, _) => data.value,
              pointColorMapper:
                  (data, index) =>
                      effectiveColorList[index % effectiveColorList.length],
              dataLabelSettings: const DataLabelSettings(isVisible: false),
              radius: outerRadius,
            ),
          ],
        );

      case ChartType.bubble:
        // Build Bubble Chart with custom layout
        return Row(
          children: [
            Expanded(
              flex: 2,
              child: SfCartesianChart(
                primaryXAxis: NumericAxis(
                  minimum: 0,
                  maximum: 100,
                  interval: 20,
                  axisLine: const AxisLine(width: 1),
                  majorTickLines: const MajorTickLines(size: 0),
                  majorGridLines: const MajorGridLines(
                    width: 1,
                    color: Colors.grey,
                  ),
                ),
                primaryYAxis: NumericAxis(
                  minimum: 0,
                  maximum: 50,
                  interval: 10,
                  axisLine: const AxisLine(width: 1),
                  majorTickLines: const MajorTickLines(size: 0),
                  majorGridLines: const MajorGridLines(
                    width: 1,
                    color: Colors.grey,
                  ),
                ),
                legend: const Legend(isVisible: false),
                tooltipBehavior: TooltipBehavior(enable: false),
                plotAreaBorderWidth: 1,
                series: <BubbleSeries<_BubbleChartData, double>>[
                  BubbleSeries<_BubbleChartData, double>(
                    animationDuration: 0,
                    dataSource: _getBubbleChartData(),
                    xValueMapper: (data, _) => data.x,
                    yValueMapper: (data, _) => data.y,
                    sizeValueMapper: (data, _) => data.size,
                    pointColorMapper:
                        (data, index) =>
                            effectiveColorList[index %
                                effectiveColorList.length],
                    dataLabelSettings: const DataLabelSettings(
                      isVisible: false,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(flex: 1, child: _buildBubbleLegend(c.labelFontSize ?? 10)),
          ],
        );
    }
  }

  String _getOuterRadius(ChartSizeType? size) {
    switch (size) {
      case ChartSizeType.small:
        return '85%';
      case ChartSizeType.medium:
        return '95%';
      case ChartSizeType.large:
        return '100%';
      default:
        return '100%';
    }
  }

  String _getInnerRadius(ChartSizeType? size) {
    switch (size) {
      case ChartSizeType.small:
        return '65%';
      case ChartSizeType.medium:
        return '65%';
      case ChartSizeType.large:
        return '65%';
      default:
        return '65%';
    }
  }

  Widget buildLegend(double fontSize) {
    final numRows = (effectiveDataMap.length / 2).ceil();

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: List.generate(numRows, (rowIndex) {
        final int item1Index = rowIndex;
        final double item2IndexDouble = rowIndex + numRows.toDouble();
        final int item2Index = item2IndexDouble.toInt();
        final labels = effectiveDataMap.keys.toList();

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 2.0),
          child: Row(
            children: [
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      color: effectiveColorList[item1Index],
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        labels[item1Index],
                        style: TextStyle(fontSize: fontSize),
                        overflow: TextOverflow.visible,
                        softWrap: true,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child:
                    item2Index < labels.length
                        ? Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              color: effectiveColorList[item2Index],
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                labels[item2Index],
                                style: TextStyle(fontSize: fontSize),
                                overflow: TextOverflow.visible,
                                softWrap: true,
                              ),
                            ),
                          ],
                        )
                        : const SizedBox(), // Placeholder for alignment if odd number of items
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildBubbleLegend(double fontSize) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // Group header
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.blue.shade100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            'Group1',
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w500,
              color: Colors.blue.shade700,
            ),
          ),
        ),
        const SizedBox(height: 12),

        // Legend items
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBubbleLegendItem(
              color: Colors.blue.shade600,
              label: 'X-label - 50',
              fontSize: fontSize,
            ),
            const SizedBox(height: 4),
            _buildBubbleLegendItem(
              color: Colors.blue.shade500,
              label: 'Y-label - 50',
              fontSize: fontSize,
            ),
            const SizedBox(height: 4),
            _buildBubbleLegendItem(
              color: Colors.blue.shade400,
              label: 'Value 3 - 100',
              fontSize: fontSize,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBubbleLegendItem({
    required Color color,
    required String label,
    required double fontSize,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(color: color, shape: BoxShape.rectangle),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: TextStyle(fontSize: fontSize, color: Colors.black87),
          ),
        ),
      ],
    );
  }

  void _rebuildWidgetTree() {
    final c = widget.config;
    screenWidgetTree = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          c.propertyType ?? "",
          style: const TextStyle(fontSize: 33, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 32),
        buildChartSection(c: c),
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    _rebuildWidgetTree();
  }

  @override
  void didUpdateWidget(covariant RingPieChartUIBuilder oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.config != widget.config) {
      setState(() => _rebuildWidgetTree());
    }
  }

  Future<void> generateBuildUI() async {
    try {
      // Generate JSON from current widget tree
      final generated = FlexibleWidgetSerializer.serialize(screenWidgetTree);
      final jsonString = const JsonEncoder.withIndent('  ').convert(generated);

      print("=== Ring Pie Chart Serialized Widget JSON ===\n$jsonString");

      // Deserialize JSON back to widget
      final rebuiltWidget = FlexibleWidgetSerializer.deserialize(generated);

      setState(() {
        deserializedWidget = rebuiltWidget;
      });

      print("=== JSON to Widget Conversion Complete ===");
    } catch (e) {
      print("Error in JSON generation/deserialization: $e");
      setState(() {
        jsonOutput = 'Error: $e';
        deserializedWidget = null;
      });
    }
  }

  Future<void> generateJson() async {
    try {
      // Generate JSON from current widget tree
      final generated = FlexibleWidgetSerializer.serialize(screenWidgetTree);
      final jsonString = const JsonEncoder.withIndent('  ').convert(generated);

      setState(() {
        jsonOutput = jsonString;
      });

      print("=== JSON to Widget Conversion Complete ===");
    } catch (e) {
      print("Error in JSON generation/deserialization: $e");
      setState(() {
        jsonOutput = 'Error: $e';
        deserializedWidget = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Generate JSON Button
          Row(
            children: [
              Center(
                child: ElevatedButton.icon(
                  onPressed: generateJson,
                  icon: const Icon(Icons.code),
                  label: const Text('Generate JSON'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    textStyle: const TextStyle(fontSize: 16),
                  ),
                ),
              ),
              Center(
                child: ElevatedButton.icon(
                  onPressed: generateBuildUI,
                  icon: const Icon(Icons.code),
                  label: const Text('Generate UI from JSON'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    textStyle: const TextStyle(fontSize: 16),
                  ),
                ),
              ),
              Center(
                child: ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      jsonOutput = '';
                      deserializedWidget = null;
                    });
                  },
                  icon: const Icon(Icons.code),
                  label: const Text('Clear'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    textStyle: const TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Horizontal scrollable row with widgets
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Original Widget
              Container(
                width: 450,
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: screenWidgetTree,
              ),

              // JSON Output
              if (jsonOutput.isNotEmpty) ...[
                Expanded(
                  child: Container(
                    height: 400,
                    margin: const EdgeInsets.all(8),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.blue.shade200),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: SelectableText(
                      jsonOutput.isNotEmpty ? jsonOutput : 'No JSON generated',
                      style: const TextStyle(
                        fontSize: 12,
                        fontFamily: 'Courier',
                      ),
                    ),
                  ),
                ),
              ],

              // Deserialized Widget
              if (deserializedWidget != null)
                Container(
                  width: 450,
                  margin: const EdgeInsets.all(8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.green.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: deserializedWidget!,
                ),
            ],
          ),
        ],
      ),
    );
  }

  List<_ChartData> _getChartData() {
    return [
      _ChartData('Food', 20),
      _ChartData('Rent', 15),
      _ChartData('Transport', 10),
      _ChartData('Savings', 12),
      _ChartData('Others', 8),
      _ChartData('Utilities', 10),
      _ChartData('Insurance', 15),
      _ChartData('Entertainment', 10),
    ];
  }

  List<_BubbleChartData> _getBubbleChartData() {
    return [
      _BubbleChartData(10, 20, 15),
      _BubbleChartData(20, 30, 25),
      _BubbleChartData(30, 15, 20),
      _BubbleChartData(40, 35, 30),
      _BubbleChartData(50, 25, 18),
      _BubbleChartData(60, 40, 35),
      _BubbleChartData(70, 30, 22),
      _BubbleChartData(80, 45, 28),
    ];
  }
}

class _ChartData {
  final String category;
  final double value;

  _ChartData(this.category, this.value);
}

class _BubbleChartData {
  final double x;
  final double y;
  final double size;

  _BubbleChartData(this.x, this.y, this.size);
}
