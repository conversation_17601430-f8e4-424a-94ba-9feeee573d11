import 'package:flutter/material.dart';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension RangeSliderColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// A comprehensive range slider widget that allows users to select a range of values.
///
/// This widget provides a range slider with various customization options
/// including min/max values, divisions, colors, labels, and more.
class RangeSliderWidget extends StatefulWidget {
  /// Minimum value for the slider
  final double min;

  /// Maximum value for the slider
  final double max;

  /// Initial start value of the range
  final double initialStart;

  /// Initial end value of the range
  final double initialEnd;

  /// Number of discrete divisions for the slider
  final int? divisions;

  /// Color of the active portion of the slider
  final Color activeColor;

  /// Color of the inactive portion of the slider
  final Color? inactiveColor;

  /// Color of the slider thumbs
  final Color? thumbColor;

  /// Height of the slider track
  final double? trackHeight;

  /// Style for the slider label
  final TextStyle? labelStyle;

  /// Whether to show the value labels
  final bool showLabels;

  /// Whether to show the range values below the slider
  final bool showRangeValues;

  /// Custom label format function
  final String Function(double)? labelFormat;

  /// Whether to show min and max labels
  final bool showMinMaxLabels;

  /// Style for min and max labels
  final TextStyle? minMaxLabelStyle;

  /// Whether the slider is vertical
  final bool isVertical;

  /// Height of the slider (if vertical, this is the height; if horizontal, this is optional)
  final double? height;

  /// Width of the slider (if vertical, this is the width; if horizontal, this is optional)
  final double? width;

  /// Padding around the slider
  final EdgeInsetsGeometry padding;

  /// Margin around the slider
  final EdgeInsetsGeometry margin;

  /// Whether to show a tooltip when dragging
  final bool showTooltip;

  /// Whether the slider is disabled
  final bool isDisabled;

  /// Whether to allow overlapping thumbs
  final bool allowOverlapping;

  /// Whether to allow crossing thumbs
  final bool allowCrossing;

  /// Minimum distance between start and end values
  final double? minDistance;

  /// Maximum distance between start and end values
  final double? maxDistance;

  /// Prefix for the range values display
  final String? prefix;

  /// Suffix for the range values display
  final String? suffix;

  /// Number of decimal places to show in the range values display
  final int decimalPlaces;

  /// Label for the slider
  final String? label;

  /// Text style for the label
  final TextStyle? labelTextStyle;

  /// Alignment for the label
  final TextAlign labelAlignment;

  /// Callback when the range values change
  final Function(RangeValues)? onChanged;

  /// Callback when the range value change ends
  final Function(RangeValues)? onChangeEnd;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is hovered
  final Color? hoverColor;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the widget is long-pressed
  final VoidCallback? onLongPress;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Slider-specific JSON configuration
  /// Whether to use JSON slider configuration
  final bool useJsonSliderConfig;

  /// Slider-specific JSON configuration
  final Map<String, dynamic>? sliderConfig;

  const RangeSliderWidget({
    super.key,
    this.min = 0.0,
    this.max = 100.0,
    this.initialStart = 25.0,
    this.initialEnd = 75.0,
    this.divisions,
    this.activeColor = const Color(0xFF0058FF),
    this.inactiveColor,
    this.thumbColor,
    this.trackHeight,
    this.labelStyle,
    this.showLabels = false,
    this.showRangeValues = false,
    this.labelFormat,
    this.showMinMaxLabels = true,
    this.minMaxLabelStyle,
    this.isVertical = false,
    this.height,
    this.width,
    this.padding = EdgeInsets.zero,
    this.margin = EdgeInsets.zero,
    this.showTooltip = false,
    this.isDisabled = false,
    this.allowOverlapping = false,
    this.allowCrossing = false,
    this.minDistance,
    this.maxDistance,
    this.prefix,
    this.suffix,
    this.decimalPlaces = 0,
    this.label,
    this.labelTextStyle,
    this.labelAlignment = TextAlign.start,
    this.onChanged,
    this.onChangeEnd,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Slider-specific JSON configuration
    this.useJsonSliderConfig = false,
    this.sliderConfig,
  });

  /// Creates a RangeSliderWidget from a JSON map
  ///
  /// This factory constructor allows for creating a RangeSliderWidget from a JSON map,
  /// making it easy to configure the widget from API responses or configuration files.
  factory RangeSliderWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color activeColor = const Color(0xFF0058FF);
    if (json.containsKey('activeColor')) {
      activeColor = _parseColor(json['activeColor']);
    }

    Color? inactiveColor;
    if (json.containsKey('inactiveColor')) {
      inactiveColor = _parseColor(json['inactiveColor']);
    }

    Color? thumbColor;
    if (json.containsKey('thumbColor')) {
      thumbColor = _parseColor(json['thumbColor']);
    }

    Color? hoverColor;
    if (json.containsKey('hoverColor')) {
      hoverColor = _parseColor(json['hoverColor']);
    }

    Color? focusColor;
    if (json.containsKey('focusColor')) {
      focusColor = _parseColor(json['focusColor']);
    }

    // Parse text styles
    TextStyle? labelStyle;
    if (json.containsKey('labelStyle')) {
      labelStyle = _parseTextStyle(json['labelStyle']);
    }

    TextStyle? minMaxLabelStyle;
    if (json.containsKey('minMaxLabelStyle')) {
      minMaxLabelStyle = _parseTextStyle(json['minMaxLabelStyle']);
    }

    TextStyle? labelTextStyle;
    if (json.containsKey('labelTextStyle')) {
      labelTextStyle = _parseTextStyle(json['labelTextStyle']);
    }

    // Parse text alignment
    TextAlign labelAlignment = TextAlign.start;
    if (json.containsKey('labelAlignment')) {
      final String alignStr = json['labelAlignment'].toString().toLowerCase();
      if (alignStr == 'center') {
        labelAlignment = TextAlign.center;
      } else if (alignStr == 'end') {
        labelAlignment = TextAlign.end;
      } else if (alignStr == 'justify') {
        labelAlignment = TextAlign.justify;
      } else if (alignStr == 'left') {
        labelAlignment = TextAlign.left;
      } else if (alignStr == 'right') {
        labelAlignment = TextAlign.right;
      }
    }

    // Parse padding and margin
    EdgeInsetsGeometry padding = const EdgeInsets.all(8.0);
    if (json.containsKey('padding')) {
      padding = _parseEdgeInsets(json['padding']);
    }

    EdgeInsetsGeometry margin = EdgeInsets.zero;
    if (json.containsKey('margin')) {
      margin = _parseEdgeInsets(json['margin']);
    }

    return RangeSliderWidget(
      // Basic properties
      min: json['min'] != null ? (json['min'] as num).toDouble() : 0.0,
      max: json['max'] != null ? (json['max'] as num).toDouble() : 100.0,
      initialStart:
          json['initialStart'] != null
              ? (json['initialStart'] as num).toDouble()
              : 25.0,
      initialEnd:
          json['initialEnd'] != null
              ? (json['initialEnd'] as num).toDouble()
              : 75.0,
      divisions: json['divisions'] as int?,

      // Color properties
      activeColor: activeColor,
      inactiveColor: inactiveColor,
      thumbColor: thumbColor,

      // Size properties
      trackHeight:
          json['trackHeight'] != null
              ? (json['trackHeight'] as num).toDouble()
              : null,
      height:
          json['height'] != null ? (json['height'] as num).toDouble() : null,
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,

      // Style properties
      labelStyle: labelStyle,
      minMaxLabelStyle: minMaxLabelStyle,
      labelTextStyle: labelTextStyle,

      // Display options
      showLabels: json['showLabels'] as bool? ?? false,
      showRangeValues: json['showRangeValues'] as bool? ?? false,
      showMinMaxLabels: json['showMinMaxLabels'] as bool? ?? true,
      isVertical: json['isVertical'] as bool? ?? false,
      showTooltip: json['showTooltip'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,

      // Constraint properties
      allowOverlapping: json['allowOverlapping'] as bool? ?? false,
      allowCrossing: json['allowCrossing'] as bool? ?? false,
      minDistance:
          json['minDistance'] != null
              ? (json['minDistance'] as num).toDouble()
              : null,
      maxDistance:
          json['maxDistance'] != null
              ? (json['maxDistance'] as num).toDouble()
              : null,

      // Formatting properties
      prefix: json['prefix'] as String?,
      suffix: json['suffix'] as String?,
      decimalPlaces: json['decimalPlaces'] as int? ?? 0,
      label: json['label'] as String?,
      labelAlignment: labelAlignment,

      // Layout properties
      padding: padding,
      margin: margin,

      // Advanced interaction properties
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: hoverColor,
      focusColor: focusColor,
      enableFeedback: json['enableFeedback'] as bool? ?? true,

      // Accessibility properties
      semanticsLabel: json['semanticsLabel'] as String?,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,

      // JSON configuration properties
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      useJsonSliderConfig: json['useJsonSliderConfig'] as bool? ?? false,
      jsonConfig: json,
      jsonCallbacks:
          json.containsKey('callbacks')
              ? json['callbacks'] as Map<String, dynamic>
              : null,
    );
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return const Color(0xFF0058FF);
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          default:
            return const Color(0xFF0058FF);
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a =
          colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return const Color(0xFF0058FF); // Default color
  }

  /// Parses edge insets from a map or number
  static EdgeInsetsGeometry _parseEdgeInsets(dynamic value) {
    if (value is num) {
      return EdgeInsets.all(value.toDouble());
    } else if (value is Map) {
      if (value.containsKey('all')) {
        return EdgeInsets.all((value['all'] as num).toDouble());
      } else if (value.containsKey('horizontal') ||
          value.containsKey('vertical')) {
        return EdgeInsets.symmetric(
          horizontal:
              value.containsKey('horizontal')
                  ? (value['horizontal'] as num).toDouble()
                  : 0.0,
          vertical:
              value.containsKey('vertical')
                  ? (value['vertical'] as num).toDouble()
                  : 0.0,
        );
      } else {
        return EdgeInsets.fromLTRB(
          value.containsKey('left') ? (value['left'] as num).toDouble() : 0.0,
          value.containsKey('top') ? (value['top'] as num).toDouble() : 0.0,
          value.containsKey('right') ? (value['right'] as num).toDouble() : 0.0,
          value.containsKey('bottom')
              ? (value['bottom'] as num).toDouble()
              : 0.0,
        );
      }
    }
    return const EdgeInsets.all(0.0); // Default padding
  }

  /// Parses a text style from a map
  static TextStyle? _parseTextStyle(dynamic value) {
    if (value is! Map) return null;

    Color color = Colors.black;
    if (value.containsKey('color')) {
      color = _parseColor(value['color']);
    }

    double fontSize = 14.0;
    if (value.containsKey('fontSize')) {
      fontSize = (value['fontSize'] as num).toDouble();
    }

    FontWeight fontWeight = FontWeight.normal;
    if (value.containsKey('fontWeight')) {
      if (value['fontWeight'] is String) {
        switch ((value['fontWeight'] as String).toLowerCase()) {
          case 'thin':
            fontWeight = FontWeight.w100;
            break;
          case 'extralight':
            fontWeight = FontWeight.w200;
            break;
          case 'light':
            fontWeight = FontWeight.w300;
            break;
          case 'regular':
            fontWeight = FontWeight.w400;
            break;
          case 'medium':
            fontWeight = FontWeight.w500;
            break;
          case 'semibold':
            fontWeight = FontWeight.w600;
            break;
          case 'bold':
            fontWeight = FontWeight.w700;
            break;
          case 'extrabold':
            fontWeight = FontWeight.w800;
            break;
          case 'black':
            fontWeight = FontWeight.w900;
            break;
        }
      } else if (value['fontWeight'] is int) {
        final int weight = value['fontWeight'] as int;
        switch (weight) {
          case 100:
            fontWeight = FontWeight.w100;
            break;
          case 200:
            fontWeight = FontWeight.w200;
            break;
          case 300:
            fontWeight = FontWeight.w300;
            break;
          case 400:
            fontWeight = FontWeight.w400;
            break;
          case 500:
            fontWeight = FontWeight.w500;
            break;
          case 600:
            fontWeight = FontWeight.w600;
            break;
          case 700:
            fontWeight = FontWeight.w700;
            break;
          case 800:
            fontWeight = FontWeight.w800;
            break;
          case 900:
            fontWeight = FontWeight.w900;
            break;
        }
      }
    }

    FontStyle fontStyle = FontStyle.normal;
    if (value.containsKey('fontStyle')) {
      if (value['fontStyle'] is String &&
          (value['fontStyle'] as String).toLowerCase() == 'italic') {
        fontStyle = FontStyle.italic;
      }
    }

    TextDecoration decoration = TextDecoration.none;
    if (value.containsKey('decoration')) {
      if (value['decoration'] is String) {
        switch ((value['decoration'] as String).toLowerCase()) {
          case 'underline':
            decoration = TextDecoration.underline;
            break;
          case 'overline':
            decoration = TextDecoration.overline;
            break;
          case 'linethrough':
            decoration = TextDecoration.lineThrough;
            break;
        }
      }
    }

    return TextStyle(
      color: color,
      fontSize: fontSize,
      fontWeight: fontWeight,
      fontStyle: fontStyle,
      decoration: decoration,
    );
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Basic properties
      'min': min,
      'max': max,
      'initialStart': initialStart,
      'initialEnd': initialEnd,
      'divisions': divisions,

      // Color properties
      'activeColor': '#${activeColor.toHexString()}',
      'inactiveColor':
          inactiveColor != null ? '#${inactiveColor!.toHexString()}' : null,
      'thumbColor': thumbColor != null ? '#${thumbColor!.toHexString()}' : null,

      // Size properties
      'trackHeight': trackHeight,
      'height': height,
      'width': width,

      // Display options
      'showLabels': showLabels,
      'showRangeValues': showRangeValues,
      'showMinMaxLabels': showMinMaxLabels,
      'isVertical': isVertical,
      'showTooltip': showTooltip,
      'isDisabled': isDisabled,

      // Constraint properties
      'allowOverlapping': allowOverlapping,
      'allowCrossing': allowCrossing,
      'minDistance': minDistance,
      'maxDistance': maxDistance,

      // Formatting properties
      'prefix': prefix,
      'suffix': suffix,
      'decimalPlaces': decimalPlaces,
      'label': label,
      'labelAlignment': labelAlignment.toString().split('.').last,

      // Layout properties
      'padding': _edgeInsetsToJson(padding),
      'margin': _edgeInsetsToJson(margin),

      // Advanced interaction properties
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
      'hoverColor': hoverColor != null ? '#${hoverColor!.toHexString()}' : null,
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration properties
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonSliderConfig': useJsonSliderConfig,
    };
  }

  /// Converts EdgeInsetsGeometry to a JSON representation
  static Map<String, dynamic> _edgeInsetsToJson(EdgeInsetsGeometry edgeInsets) {
    if (edgeInsets is EdgeInsets) {
      if (edgeInsets.left == edgeInsets.top &&
          edgeInsets.left == edgeInsets.right &&
          edgeInsets.left == edgeInsets.bottom) {
        return {'all': edgeInsets.left};
      } else if (edgeInsets.left == edgeInsets.right &&
          edgeInsets.top == edgeInsets.bottom) {
        return {'horizontal': edgeInsets.left, 'vertical': edgeInsets.top};
      } else {
        return {
          'left': edgeInsets.left,
          'top': edgeInsets.top,
          'right': edgeInsets.right,
          'bottom': edgeInsets.bottom,
        };
      }
    }
    return {'all': 0.0}; // Default
  }

  @override
  State<RangeSliderWidget> createState() => _RangeSliderWidgetState();
}

class _RangeSliderWidgetState extends State<RangeSliderWidget> {
  late RangeValues _currentRange;

  // Interaction state
  bool _isFocused = false;
  late FocusNode _focusNode;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();
    _initializeRange();

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (_isFocused && widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    });
  }

  @override
  void didUpdateWidget(RangeSliderWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update focus node if changed
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode = widget.focusNode ?? _focusNode;
      _focusNode.addListener(_onFocusChange);
    }

    // Update callback state if provided
    if (widget.callbackState != null &&
        widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  void _initializeRange() {
    // Ensure initial values are within bounds
    double start = widget.initialStart.clamp(widget.min, widget.max);
    double end = widget.initialEnd.clamp(widget.min, widget.max);

    // Ensure start <= end
    if (start > end) {
      final temp = start;
      start = end;
      end = temp;
    }

    // Apply min/max distance constraints if specified
    if (widget.minDistance != null) {
      if (end - start < widget.minDistance!) {
        // Try to increase end first
        if (end + widget.minDistance! <= widget.max) {
          end = start + widget.minDistance!;
        } else {
          // If can't increase end, decrease start
          start = (end - widget.minDistance!).clamp(widget.min, widget.max);
        }
      }
    }

    if (widget.maxDistance != null) {
      if (end - start > widget.maxDistance!) {
        // Try to decrease end first
        if (start + widget.maxDistance! >= widget.min) {
          end = start + widget.maxDistance!;
        } else {
          // If can't decrease end, increase start
          start = (end - widget.maxDistance!).clamp(widget.min, widget.max);
        }
      }
    }

    _currentRange = RangeValues(start, end);
  }

  String _formatValue(double value) {
    if (widget.labelFormat != null) {
      return widget.labelFormat!(value);
    }

    // Default formatting based on decimal places
    return value.toStringAsFixed(widget.decimalPlaces);
  }

  String _formatRangeValue(double value) {
    final formattedValue = _formatValue(value);
    return '${widget.prefix ?? ''}$formattedValue${widget.suffix ?? ''}';
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 16.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 14.0; // Large
    } else if (screenWidth >= 1280) {
      return 12.0; // Medium
    } else {
      return 12.0; // Default for very small screens
    }
  }

  double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.label != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 4.0),
                child: Text(
                  widget.label!,
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Inter',
                  ),
                ),
              ),
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                trackHeight: widget.trackHeight ?? 8.0, // Set track height to 8 pixels by default
                rangeThumbShape: CustomRangeSliderThumbShape(
                  thumbRadius: 12,
                  thumbColor: widget.activeColor,
                  min: widget.min,
                  max: widget.max,
                  currentRange: _currentRange,
                  textStyle: const TextStyle(
                    fontSize: 12,
                    fontFamily: 'Inter',
                    color: Colors.white,
                    fontWeight: FontWeight.normal,
                  ),
                ),
                overlayShape: SliderComponentShape.noOverlay,
                activeTrackColor: widget.activeColor,
                inactiveTrackColor: Colors.grey.shade300,
                thumbColor: Colors.transparent,
                showValueIndicator: ShowValueIndicator.never,
                rangeTrackShape: const CustomRangeSliderTrackShape(),
              ),
              child: RangeSlider(
                values: _currentRange,
                min: widget.min,
                max: widget.max,
                divisions: widget.divisions,
                onChanged: widget.isDisabled
                    ? null
                    : (RangeValues values) {
                        setState(() {
                          _currentRange = values;
                        });
                        if (widget.onChanged != null) {
                          widget.onChanged!(values);
                        }
                      },
                onChangeEnd: widget.onChangeEnd,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.min.round().toString(),
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize(context),
                    fontFamily: 'Inter',
                    color: Colors.black,
                  ),
                ),
                Text(
                  widget.max.round().toString(),
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize(context),
                    fontFamily: 'Inter',
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}

/// Custom range slider track shape for edge-to-edge coverage
class CustomRangeSliderTrackShape extends RangeSliderTrackShape {
  const CustomRangeSliderTrackShape();

  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double trackHeight = sliderTheme.trackHeight ?? 4.0;
    final double trackLeft = offset.dx;
    final double trackTop = offset.dy + (parentBox.size.height - trackHeight) / 2;
    final double trackWidth = parentBox.size.width;
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required TextDirection textDirection,
    required Offset startThumbCenter,
    required Offset endThumbCenter,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final Rect trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );

    final Canvas canvas = context.canvas;
    final Paint inactivePaint = Paint()
      ..color = sliderTheme.inactiveTrackColor ?? Colors.grey.shade300
      ..style = PaintingStyle.fill;

    final Paint activePaint = Paint()
      ..color = sliderTheme.activeTrackColor ?? const Color(0xFF0058FF)
      ..style = PaintingStyle.fill;

    // Draw inactive track (full width)
    final Rect inactiveTrackRect = Rect.fromLTRB(
      trackRect.left,
      trackRect.top,
      trackRect.right,
      trackRect.bottom,
    );
    final RRect inactiveRRect = RRect.fromRectAndRadius(
      inactiveTrackRect,
      Radius.circular(trackRect.height / 2),
    );
    canvas.drawRRect(inactiveRRect, inactivePaint);

    // Draw active track (between thumbs)
    final Rect activeTrackRect = Rect.fromLTRB(
      startThumbCenter.dx,
      trackRect.top,
      endThumbCenter.dx,
      trackRect.bottom,
    );
    final RRect activeRRect = RRect.fromRectAndRadius(
      activeTrackRect,
      Radius.circular(trackRect.height / 2),
    );
    canvas.drawRRect(activeRRect, activePaint);
  }
}

/// Custom range slider thumb shape to match the basic slider design
class CustomRangeSliderThumbShape extends RangeSliderThumbShape {
  final double thumbRadius;
  final Color thumbColor;
  final double min;
  final double max;
  final RangeValues currentRange;
  final TextStyle textStyle;

  CustomRangeSliderThumbShape({
    required this.thumbRadius,
    required this.thumbColor,
    required this.min,
    required this.max,
    required this.currentRange,
    required this.textStyle,
  });

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(thumbRadius);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    bool isDiscrete = false,
    bool isEnabled = true,
    bool? isOnTop,
    required SliderThemeData sliderTheme,
    TextDirection? textDirection,
    Thumb? thumb,
    bool? isPressed,
  }) {
    final canvas = context.canvas;

    // Simple scaling based on activation animation
    final double scale = 1.0 + (activationAnimation.value * 0.1);
    final double currentRadius = thumbRadius * scale;

    // Draw white background circle
    final backgroundPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, currentRadius, backgroundPaint);

    // Draw blue border
    final borderPaint = Paint()
      ..color = thumbColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    canvas.drawCircle(center, currentRadius - 1.0, borderPaint);

    // Get the actual value based on which thumb this is
    double value = 0;
    if (thumb == Thumb.start) {
      value = currentRange.start;
    } else {
      value = currentRange.end;
    }

    // Draw text (value inside thumb) with blue color to match border
    final textSpan = TextSpan(
      text: '${value.round()}',
      style: textStyle.copyWith(
        color: thumbColor,
        fontSize: textStyle.fontSize! * scale,
      ),
    );

    final tp = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: textDirection ?? TextDirection.ltr,
    );

    tp.layout();
    tp.paint(
      canvas,
      center - Offset(tp.width / 2, tp.height / 2),
    );
  }
}
