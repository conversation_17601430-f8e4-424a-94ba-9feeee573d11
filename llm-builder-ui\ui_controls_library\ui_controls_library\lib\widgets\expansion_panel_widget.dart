import 'package:flutter/material.dart';

/// A highly configurable expansion panel widget that can display expandable content.
///
/// This widget wraps Flutter's ExpansionPanel components and provides
/// additional customization options for creating expandable content containers.
class ExpansionPanelWidget extends StatefulWidget {
  /// The title of the expansion panel.
  final String title;

  /// The content to display when the panel is expanded.
  final Widget content;

  /// Optional subtitle to display below the title.
  final String? subtitle;

  /// Optional icon to display next to the title.
  final IconData? icon;

  /// Whether the panel is initially expanded.
  final bool initiallyExpanded;

  /// Whether the panel can be expanded/collapsed.
  final bool canExpand;

  /// Background color of the panel.
  final Color backgroundColor;

  /// Text color for the panel.
  final Color textColor;

  /// Color of the expansion icon.
  final Color iconColor;

  /// Color of the divider between header and content.
  final Color dividerColor;

  /// Width of the panel.
  final double? width;

  /// Minimum height of the panel when collapsed.
  final double? collapsedHeight;

  /// Padding inside the panel.
  final EdgeInsetsGeometry padding;

  /// Margin around the panel.
  final EdgeInsetsGeometry margin;

  /// Whether to show a border around the panel.
  final bool hasBorder;

  /// Color of the border.
  final Color borderColor;

  /// Width of the border.
  final double borderWidth;

  /// Radius of the panel's corners.
  final double borderRadius;

  /// Whether to show a shadow under the panel.
  final bool hasShadow;

  /// Elevation of the panel (affects shadow).
  final double elevation;

  /// Font size for the title.
  final double titleFontSize;

  /// Font weight for the title.
  final FontWeight titleFontWeight;

  /// Font size for the subtitle.
  final double subtitleFontSize;

  /// Whether to use a dark theme.
  final bool isDarkTheme;

  /// Whether the panel is disabled.
  final bool isDisabled;

  /// Callback when the panel is expanded or collapsed.
  final void Function(bool)? onExpansionChanged;

  /// Whether to show a divider between the header and content.
  final bool showHeaderDivider;

  /// Whether to animate the expansion/collapse.
  final bool animateExpansion;

  /// Duration of the expansion animation.
  final Duration animationDuration;

  /// Creates an expansion panel widget with the specified properties.
  const ExpansionPanelWidget({
    super.key,
    required this.title,
    required this.content,
    this.subtitle,
    this.icon,
    this.initiallyExpanded = false,
    this.canExpand = true,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
    this.iconColor = Colors.black54,
    this.dividerColor = Colors.grey,
    this.width,
    this.collapsedHeight,
    this.padding = const EdgeInsets.all(16.0),
    this.margin = const EdgeInsets.all(8.0),
    this.hasBorder = true,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasShadow = true,
    this.elevation = 2.0,
    this.titleFontSize = 16.0,
    this.titleFontWeight = FontWeight.bold,
    this.subtitleFontSize = 14.0,
    this.isDarkTheme = false,
    this.isDisabled = false,
    this.onExpansionChanged,
    this.showHeaderDivider = true,
    this.animateExpansion = true,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  State<ExpansionPanelWidget> createState() => _ExpansionPanelWidgetState();

  /// Creates an ExpansionPanelWidget from a JSON map.
  ///
  /// This factory constructor allows creating an ExpansionPanelWidget from a JSON object,
  /// making it easy to configure the widget dynamically.
  factory ExpansionPanelWidget.fromJson(Map<String, dynamic> json) {
    // Helper function to get a color from a string or int
    Color getColorFromValue(dynamic value) {
      if (value is String) {
        if (value.startsWith('#')) {
          // Handle hex color
          final hex = value.replaceFirst('#', '');
          final hexValue = int.tryParse(hex, radix: 16);
          if (hexValue != null) {
            return Color(hexValue | 0xFF000000); // Add alpha if needed
          }
        }

        // Handle named colors
        switch (value.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'transparent': return Colors.transparent;
          default: return Colors.blue; // Default color
        }
      } else if (value is int) {
        return Color(value);
      }
      return Colors.blue; // Default color
    }

    // Helper function to get a font weight from a string or int
    FontWeight getFontWeightFromValue(dynamic value) {
      if (value is String) {
        switch (value.toLowerCase()) {
          case 'bold': return FontWeight.bold;
          case 'normal': return FontWeight.normal;
          case 'light': return FontWeight.w300;
          case 'medium': return FontWeight.w500;
          case 'semibold': return FontWeight.w600;
          case 'extrabold': return FontWeight.w800;
          case 'black': return FontWeight.w900;
          default: return FontWeight.normal;
        }
      } else if (value is int) {
        return FontWeight.values.firstWhere(
          (weight) => weight.index == value,
          orElse: () => FontWeight.normal,
        );
      }
      return FontWeight.normal;
    }

    // Helper function to get an icon from a string
    IconData? getIconFromValue(dynamic value) {
      if (value is String) {
        switch (value.toLowerCase()) {
          case 'expand_more': return Icons.expand_more;
          case 'expand_less': return Icons.expand_less;
          case 'arrow_drop_down': return Icons.arrow_drop_down;
          case 'arrow_drop_up': return Icons.arrow_drop_up;
          case 'arrow_downward': return Icons.arrow_downward;
          case 'arrow_upward': return Icons.arrow_upward;
          case 'keyboard_arrow_down': return Icons.keyboard_arrow_down;
          case 'keyboard_arrow_up': return Icons.keyboard_arrow_up;
          case 'add': return Icons.add;
          case 'remove': return Icons.remove;
          case 'add_circle': return Icons.add_circle;
          case 'remove_circle': return Icons.remove_circle;
          case 'add_circle_outline': return Icons.add_circle_outline;
          case 'remove_circle_outline': return Icons.remove_circle_outline;
          case 'info': return Icons.info;
          case 'info_outline': return Icons.info_outline;
          case 'help': return Icons.help;
          case 'help_outline': return Icons.help_outline;
          case 'settings': return Icons.settings;
          case 'settings_applications': return Icons.settings_applications;
          case 'tune': return Icons.tune;
          case 'list': return Icons.list;
          case 'menu': return Icons.menu;
          case 'more_vert': return Icons.more_vert;
          case 'more_horiz': return Icons.more_horiz;
          case 'folder': return Icons.folder;
          case 'folder_open': return Icons.folder_open;
          case 'description': return Icons.description;
          case 'article': return Icons.article;
          case 'subject': return Icons.subject;
          case 'text_snippet': return Icons.text_snippet;
          case 'category': return Icons.category;
          case 'label': return Icons.label;
          case 'label_outline': return Icons.label_outline;
          case 'bookmark': return Icons.bookmark;
          case 'bookmark_outline': return Icons.bookmark_outline;
          case 'star': return Icons.star;
          case 'star_outline': return Icons.star_outline;
          case 'favorite': return Icons.favorite;
          case 'favorite_outline': return Icons.favorite_outline;
          default: return null;
        }
      }
      return null;
    }

    // Helper function to get EdgeInsets from value
    EdgeInsetsGeometry getEdgeInsetsFromValue(dynamic value) {
      if (value is String) {
        // Parse formats like "16.0" or "16.0,8.0,16.0,8.0" (top,right,bottom,left)
        final parts = value.split(',');
        if (parts.length == 1) {
          final all = double.tryParse(parts[0]) ?? 16.0;
          return EdgeInsets.all(all);
        } else if (parts.length == 2) {
          final vertical = double.tryParse(parts[0]) ?? 16.0;
          final horizontal = double.tryParse(parts[1]) ?? 16.0;
          return EdgeInsets.symmetric(vertical: vertical, horizontal: horizontal);
        } else if (parts.length == 4) {
          final top = double.tryParse(parts[0]) ?? 16.0;
          final right = double.tryParse(parts[1]) ?? 16.0;
          final bottom = double.tryParse(parts[2]) ?? 16.0;
          final left = double.tryParse(parts[3]) ?? 16.0;
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (value is double) {
        return EdgeInsets.all(value);
      } else if (value is int) {
        return EdgeInsets.all(value.toDouble());
      } else if (value is Map) {
        final top = (value['top'] as num?)?.toDouble() ?? 0.0;
        final right = (value['right'] as num?)?.toDouble() ?? 0.0;
        final bottom = (value['bottom'] as num?)?.toDouble() ?? 0.0;
        final left = (value['left'] as num?)?.toDouble() ?? 0.0;

        if (value.containsKey('all')) {
          final all = (value['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (value.containsKey('horizontal') || value.containsKey('vertical')) {
          final horizontal = (value['horizontal'] as num?)?.toDouble() ?? 0.0;
          final vertical = (value['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(horizontal: horizontal, vertical: vertical);
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      }
      return const EdgeInsets.all(16.0);
    }

    // Create content widget based on content type
    Widget createContentWidget(dynamic contentValue, Color textColor) {
      if (contentValue is String) {
        return Text(
          contentValue,
          style: TextStyle(
            fontSize: 14.0,
            color: textColor,
          ),
        );
      } else if (contentValue is Map<String, dynamic>) {
        // Handle structured content
        if (contentValue.containsKey('type')) {
          final type = contentValue['type'] as String;
          switch (type) {
            case 'text':
              return Text(
                contentValue['text'] as String? ?? 'Content',
                style: TextStyle(
                  fontSize: (contentValue['fontSize'] as num?)?.toDouble() ?? 14.0,
                  color: contentValue.containsKey('color')
                      ? getColorFromValue(contentValue['color'])
                      : textColor,
                  fontWeight: contentValue.containsKey('fontWeight')
                      ? getFontWeightFromValue(contentValue['fontWeight'])
                      : FontWeight.normal,
                ),
              );
            case 'column':
              final children = (contentValue['children'] as List?)?.map((child) {
                return createContentWidget(child, textColor);
              }).toList() ?? <Widget>[];

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: children,
              );
            case 'row':
              final children = (contentValue['children'] as List?)?.map((child) {
                return createContentWidget(child, textColor);
              }).toList() ?? <Widget>[];

              return Row(
                children: children,
              );
            case 'container':
              return Container(
                padding: contentValue.containsKey('padding')
                    ? getEdgeInsetsFromValue(contentValue['padding'])
                    : null,
                margin: contentValue.containsKey('margin')
                    ? getEdgeInsetsFromValue(contentValue['margin'])
                    : null,
                color: contentValue.containsKey('color')
                    ? getColorFromValue(contentValue['color'])
                    : null,
                child: contentValue.containsKey('child')
                    ? createContentWidget(contentValue['child'], textColor)
                    : const SizedBox.shrink(),
              );
            default:
              return Text(
                'Unsupported content type: $type',
                style: TextStyle(color: textColor),
              );
          }
        }
      }

      // Default content
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'This is a sample expansion panel content. You can customize this panel with various properties like colors, borders, shadows, and more.',
            style: TextStyle(
              fontSize: 14.0,
              color: textColor,
            ),
          ),
          const SizedBox(height: 8.0),
          Text(
            'Use natural language prompts to configure this panel according to your needs.',
            style: TextStyle(
              fontSize: 14.0,
              color: textColor.withAlpha(179), // 0.7 * 255 = 179
            ),
          ),
        ],
      );
    }

    // Extract properties from JSON
    final title = json['title'] as String? ?? 'Expansion Panel';
    final subtitle = json['subtitle'] as String?;
    final icon = json.containsKey('icon') ? getIconFromValue(json['icon']) : null;

    // Extract behavior properties
    final initiallyExpanded = json['initiallyExpanded'] as bool? ?? false;
    final canExpand = json['canExpand'] as bool? ?? true;

    // Extract color properties
    final backgroundColor = json.containsKey('backgroundColor')
        ? getColorFromValue(json['backgroundColor'])
        : Colors.white;
    final textColor = json.containsKey('textColor')
        ? getColorFromValue(json['textColor'])
        : Colors.black87;
    final iconColor = json.containsKey('iconColor')
        ? getColorFromValue(json['iconColor'])
        : Colors.black54;
    final dividerColor = json.containsKey('dividerColor')
        ? getColorFromValue(json['dividerColor'])
        : Colors.grey;

    // Extract dimension properties
    final width = (json['width'] as num?)?.toDouble();
    final collapsedHeight = (json['collapsedHeight'] as num?)?.toDouble();
    final padding = json.containsKey('padding')
        ? getEdgeInsetsFromValue(json['padding'])
        : const EdgeInsets.all(16.0);
    final margin = json.containsKey('margin')
        ? getEdgeInsetsFromValue(json['margin'])
        : const EdgeInsets.all(8.0);

    // Extract border properties
    final hasBorder = json['hasBorder'] as bool? ?? true;
    final borderColor = json.containsKey('borderColor')
        ? getColorFromValue(json['borderColor'])
        : Colors.grey;
    final borderWidth = (json['borderWidth'] as num?)?.toDouble() ?? 1.0;
    final borderRadius = (json['borderRadius'] as num?)?.toDouble() ?? 8.0;

    // Extract shadow properties
    final hasShadow = json['hasShadow'] as bool? ?? true;
    final elevation = (json['elevation'] as num?)?.toDouble() ?? 2.0;

    // Extract font properties
    final titleFontSize = (json['titleFontSize'] as num?)?.toDouble() ?? 16.0;
    final titleFontWeight = json.containsKey('titleFontWeight')
        ? getFontWeightFromValue(json['titleFontWeight'])
        : FontWeight.bold;
    final subtitleFontSize = (json['subtitleFontSize'] as num?)?.toDouble() ?? 14.0;

    // Extract theme properties
    final isDarkTheme = json['isDarkTheme'] as bool? ?? false;
    final isDisabled = json['isDisabled'] as bool? ?? false;

    // Extract behavior properties
    final showHeaderDivider = json['showHeaderDivider'] as bool? ?? true;
    final animateExpansion = json['animateExpansion'] as bool? ?? true;
    final animationDuration = json.containsKey('animationDuration')
        ? Duration(milliseconds: (json['animationDuration'] as int?) ?? 300)
        : const Duration(milliseconds: 300);

    // Create content widget
    final content = json.containsKey('content')
        ? createContentWidget(json['content'], textColor)
        : createContentWidget(null, textColor);

    // Return the ExpansionPanelWidget with properties from JSON
    return ExpansionPanelWidget(
      title: title,
      content: content,
      subtitle: subtitle,
      icon: icon,
      initiallyExpanded: initiallyExpanded,
      canExpand: canExpand,
      backgroundColor: backgroundColor,
      textColor: textColor,
      iconColor: iconColor,
      dividerColor: dividerColor,
      width: width,
      collapsedHeight: collapsedHeight,
      padding: padding,
      margin: margin,
      hasBorder: hasBorder,
      borderColor: borderColor,
      borderWidth: borderWidth,
      borderRadius: borderRadius,
      hasShadow: hasShadow,
      elevation: elevation,
      titleFontSize: titleFontSize,
      titleFontWeight: titleFontWeight,
      subtitleFontSize: subtitleFontSize,
      isDarkTheme: isDarkTheme,
      isDisabled: isDisabled,
      showHeaderDivider: showHeaderDivider,
      animateExpansion: animateExpansion,
      animationDuration: animationDuration,
    );
  }

  /// Converts the ExpansionPanelWidget to a JSON map.
  ///
  /// This method allows serializing the widget's configuration to JSON,
  /// making it easy to save and restore widget states.
  Map<String, dynamic> toJson() {
    // Helper function to convert Color to hex string
    String colorToHex(Color color) {
      return '#${color.toString().substring(10, 16)}';
    }

    return {
      'title': title,
      'subtitle': subtitle,
      'icon': icon?.codePoint,
      'initiallyExpanded': initiallyExpanded,
      'canExpand': canExpand,
      'backgroundColor': colorToHex(backgroundColor),
      'textColor': colorToHex(textColor),
      'iconColor': colorToHex(iconColor),
      'dividerColor': colorToHex(dividerColor),
      'width': width,
      'collapsedHeight': collapsedHeight,
      'padding': {
        'all': padding is EdgeInsets ? (padding as EdgeInsets).top : null,
      },
      'margin': {
        'all': margin is EdgeInsets ? (margin as EdgeInsets).top : null,
      },
      'hasBorder': hasBorder,
      'borderColor': colorToHex(borderColor),
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'titleFontSize': titleFontSize,
      'titleFontWeight': titleFontWeight.toString(),
      'subtitleFontSize': subtitleFontSize,
      'isDarkTheme': isDarkTheme,
      'isDisabled': isDisabled,
      'showHeaderDivider': showHeaderDivider,
      'animateExpansion': animateExpansion,
      'animationDuration': animationDuration.inMilliseconds,
      // We can't serialize the actual content widget, so we'll just indicate it was serialized
      'contentSerialized': true,
    };
  }
}

class _ExpansionPanelWidgetState extends State<ExpansionPanelWidget> {
  late bool _isExpanded;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
  }

  @override
  Widget build(BuildContext context) {
    // Determine the effective background color based on theme
    final effectiveBackgroundColor = widget.isDarkTheme
        ? Colors.grey.shade800
        : widget.backgroundColor;

    // Determine the effective text color based on theme
    final effectiveTextColor = widget.isDarkTheme
        ? Colors.white
        : widget.textColor;

    // Determine the effective icon color based on theme
    final effectiveIconColor = widget.isDarkTheme
        ? Colors.white70
        : widget.iconColor;

    // Create the panel
    final panel = Container(
      width: widget.width,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(26), // 0.1 opacity
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      margin: widget.margin,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(
          widget.hasBorder
              ? widget.borderRadius - widget.borderWidth
              : widget.borderRadius,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            InkWell(
              onTap: widget.canExpand && !widget.isDisabled
                  ? _toggleExpansion
                  : null,
              child: Padding(
                padding: widget.padding,
                child: Row(
                  children: [
                    // Icon (if provided)
                    if (widget.icon != null) ...[
                      Icon(
                        widget.icon,
                        color: effectiveIconColor,
                        size: widget.titleFontSize * 1.2,
                      ),
                      const SizedBox(width: 12),
                    ],
                    // Title and subtitle
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.title,
                            style: TextStyle(
                              fontSize: widget.titleFontSize,
                              fontWeight: widget.titleFontWeight,
                              color: effectiveTextColor,
                            ),
                          ),
                          if (widget.subtitle != null) ...[
                            const SizedBox(height: 4),
                            Text(
                              widget.subtitle!,
                              style: TextStyle(
                                fontSize: widget.subtitleFontSize,
                                color: effectiveTextColor.withAlpha(179), // 0.7 * 255 = 179
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    // Expansion icon
                    if (widget.canExpand)
                      RotationTransition(
                        turns: AlwaysStoppedAnimation(
                          _isExpanded ? 0.5 : 0.0,
                        ),
                        child: Icon(
                          Icons.keyboard_arrow_down,
                          color: effectiveIconColor,
                        ),
                      ),
                  ],
                ),
              ),
            ),
            // Divider
            if (widget.showHeaderDivider && _isExpanded)
              Divider(
                height: 1,
                thickness: 1,
                color: widget.dividerColor.withAlpha(76), // 0.3 * 255 = 76
              ),
            // Content
            if (widget.animateExpansion)
              AnimatedCrossFade(
                firstChild: const SizedBox(height: 0),
                secondChild: Padding(
                  padding: widget.padding,
                  child: widget.content,
                ),
                crossFadeState: _isExpanded
                    ? CrossFadeState.showSecond
                    : CrossFadeState.showFirst,
                duration: widget.animationDuration,
              )
            else if (_isExpanded)
              Padding(
                padding: widget.padding,
                child: widget.content,
              ),
          ],
        ),
      ),
    );

    // Apply disabled state if needed
    if (widget.isDisabled) {
      return Opacity(
        opacity: 0.5,
        child: panel,
      );
    }

    return panel;
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (widget.onExpansionChanged != null) {
      widget.onExpansionChanged!(_isExpanded);
    }
  }
}
