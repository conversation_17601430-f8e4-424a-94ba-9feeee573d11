import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';

/// A widget that calculates and displays the moving average of a series of values.
///
/// This widget allows users to input a series of values, specify a window size,
/// and visualize the moving average calculation with a chart.
class MovingAvgWidget extends StatefulWidget {
  /// Initial data values as a comma-separated string
  final String? initialValues;

  /// Initial window size for the moving average calculation
  final int windowSize;

  /// Whether to show a chart visualization of the values and moving average
  final bool showChart;

  /// Whether to show the result of the moving average calculation
  final bool showResult;

  /// Whether to show detailed calculations for each window
  final bool showDetailedCalculations;

  /// Whether to allow editing of the values
  final bool isReadOnly;

  /// Whether the widget is disabled
  final bool isDisabled;

  /// The title or label for the widget
  final String? title;

  /// Helper text to display below the inputs
  final String? helperText;

  /// Error text to display when there's an input error
  final String? errorText;

  /// The color of the text
  final Color textColor;

  /// The background color of the widget
  final Color backgroundColor;

  /// The color of the border
  final Color borderColor;

  /// The width of the border
  final double borderWidth;

  /// The radius of the border corners
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The font size for the text
  final double fontSize;

  /// The font weight for the text
  final FontWeight fontWeight;

  /// The color of the chart lines for original values
  final Color originalLineColor;

  /// The color of the chart lines for moving average values
  final Color movingAvgLineColor;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// Callback when the moving average values change
  final Function(List<double>)? onMovingAvgChanged;

  /// Callback when the input values change
  final Function(List<double>)? onValuesChanged;

  /// Creates a moving average widget.
  const MovingAvgWidget({
    super.key,
    this.initialValues,
    this.windowSize = 3,
    this.showChart = true,
    this.showResult = true,
    this.showDetailedCalculations = false,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.title,
    this.helperText,
    this.errorText,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasBorder = true,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.originalLineColor = Colors.blue,
    this.movingAvgLineColor = Colors.red,
    this.width,
    this.height,
    this.onMovingAvgChanged,
    this.onValuesChanged,
  });

  @override
  State<MovingAvgWidget> createState() => _MovingAvgWidgetState();
}

class _MovingAvgWidgetState extends State<MovingAvgWidget> {
  final TextEditingController _valuesController = TextEditingController();
  final TextEditingController _windowSizeController = TextEditingController();

  List<double> _values = [];
  List<double> _movingAvgValues = [];
  int _windowSize = 3;
  String? _inputError;
  bool _hasCalculated = false;

  @override
  void initState() {
    super.initState();

    // Initialize window size
    _windowSize = widget.windowSize;
    _windowSizeController.text = _windowSize.toString();

    // Initialize with provided values if available
    if (widget.initialValues != null) {
      _valuesController.text = widget.initialValues!;
      _parseValues();
    }

    // Calculate moving average if values are provided
    if (_values.isNotEmpty) {
      _calculateMovingAverage();
    }
  }

  @override
  void dispose() {
    _valuesController.dispose();
    _windowSizeController.dispose();
    super.dispose();
  }

  void _parseValues() {
    try {
      _values = _valuesController.text
          .split(',')
          .map((s) => double.parse(s.trim()))
          .toList();
      setState(() {
        _inputError = null;
      });
    } catch (e) {
      setState(() {
        _inputError = 'Invalid values format. Use comma-separated numbers.';
        _values = [];
        _movingAvgValues = [];
        _hasCalculated = false;
      });
    }
  }

  void _parseWindowSize() {
    try {
      final parsedSize = int.parse(_windowSizeController.text.trim());
      if (parsedSize < 1) {
        setState(() {
          _inputError = 'Window size must be at least 1.';
        });
        return;
      }
      setState(() {
        _windowSize = parsedSize;
        _inputError = null;
      });
    } catch (e) {
      setState(() {
        _inputError = 'Invalid window size. Enter a positive integer.';
      });
    }
  }

  void _calculateMovingAverage() {
    if (_values.isEmpty || _windowSize < 1) {
      setState(() {
        _movingAvgValues = [];
        _hasCalculated = false;
      });
      return;
    }

    // Calculate moving average
    final List<double> result = [];

    for (int i = 0; i < _values.length; i++) {
      if (i < _windowSize - 1) {
        // Not enough values for a full window yet
        final sublist = _values.sublist(0, i + 1);
        final sum = sublist.reduce((a, b) => a + b);
        result.add(sum / sublist.length);
      } else {
        // Full window
        final sublist = _values.sublist(i - _windowSize + 1, i + 1);
        final sum = sublist.reduce((a, b) => a + b);
        result.add(sum / _windowSize);
      }
    }

    setState(() {
      _movingAvgValues = result;
      _hasCalculated = true;
    });

    // Notify listeners
    if (widget.onMovingAvgChanged != null) {
      widget.onMovingAvgChanged!(_movingAvgValues);
    }

    if (widget.onValuesChanged != null) {
      widget.onValuesChanged!(_values);
    }
  }

  @override
  Widget build(BuildContext context) {
    final Color effectiveTextColor = widget.isDisabled
        ? Colors.grey
        : widget.textColor;

    return Container(
      width: widget.width,
      height: widget.height,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title
          if (widget.title != null) ...[
            Text(
              widget.title!,
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize + 2,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Values Input
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Values',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _valuesController,
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                ),
                decoration: InputDecoration(
                  hintText: 'Enter comma-separated values (e.g., 10,20,30,40,50)',
                  hintStyle: TextStyle(
                    color: effectiveTextColor.withAlpha(128),
                    fontSize: widget.fontSize,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                enabled: !widget.isDisabled && !widget.isReadOnly,
                onChanged: (value) {
                  _parseValues();
                  if (_values.isNotEmpty) {
                    _calculateMovingAverage();
                  }
                },
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9,.\-]')),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Window Size Input
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Window Size',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _windowSizeController,
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                ),
                decoration: InputDecoration(
                  hintText: 'Enter window size (e.g., 3)',
                  hintStyle: TextStyle(
                    color: effectiveTextColor.withAlpha(128),
                    fontSize: widget.fontSize,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                enabled: !widget.isDisabled && !widget.isReadOnly,
                onChanged: (value) {
                  _parseWindowSize();
                  if (_values.isNotEmpty && _windowSize > 0) {
                    _calculateMovingAverage();
                  }
                },
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
              ),
            ],
          ),

          // Calculate Button
          if (!widget.isDisabled && !widget.isReadOnly) ...[
            const SizedBox(height: 16),
            Center(
              child: ElevatedButton(
                onPressed: () {
                  _parseValues();
                  _parseWindowSize();
                  _calculateMovingAverage();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.originalLineColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  ),
                ),
                child: const Text('Calculate Moving Average'),
              ),
            ),
          ],

          // Error Message
          if (_inputError != null || widget.errorText != null) ...[
            const SizedBox(height: 8),
            Text(
              _inputError ?? widget.errorText!,
              style: TextStyle(
                color: Colors.red,
                fontSize: widget.fontSize - 2,
              ),
            ),
          ],

          // Result Display
          if (widget.showResult && _hasCalculated) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.backgroundColor.withAlpha(179),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Moving Average (Window Size: $_windowSize):',
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _movingAvgValues.map((v) => v.toStringAsFixed(2)).join(', '),
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize,
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Detailed Calculations
          if (widget.showDetailedCalculations && _hasCalculated && _values.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Detailed Calculations:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 120,
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
              ),
              child: ListView.builder(
                itemCount: _movingAvgValues.length,
                itemBuilder: (context, index) {
                  final int startIdx = max(0, index - _windowSize + 1);
                  final int endIdx = index;
                  final List<double> window = _values.sublist(startIdx, endIdx + 1);
                  final String windowStr = window.map((v) => v.toStringAsFixed(2)).join(' + ');
                  final double sum = window.reduce((a, b) => a + b);
                  final double avg = sum / window.length;

                  return Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    child: Text(
                      'Position ${index + 1}: ($windowStr) / ${window.length} = $avg',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize - 2,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],

          // Chart
          if (widget.showChart && _hasCalculated && _values.isNotEmpty && _movingAvgValues.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Visualization:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 200,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
              ),
              child: _buildChart(),
            ),
          ],

          // Helper Text
          if (widget.helperText != null) ...[
            const SizedBox(height: 8),
            Text(
              widget.helperText!,
              style: TextStyle(
                color: effectiveTextColor.withAlpha(179),
                fontSize: widget.fontSize - 2,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildChart() {
    // Ensure we have data to display
    if (_values.isEmpty || _movingAvgValues.isEmpty) {
      return const Center(
        child: Text('No data to display'),
      );
    }

    // Find min and max values for Y axis
    double minY = double.infinity;
    double maxY = double.negativeInfinity;

    for (final value in _values) {
      minY = min(minY, value);
      maxY = max(maxY, value);
    }

    for (final value in _movingAvgValues) {
      minY = min(minY, value);
      maxY = max(maxY, value);
    }

    // Add some padding to the min and max values
    final double yPadding = (maxY - minY) * 0.1;
    minY = minY - yPadding;
    maxY = maxY + yPadding;

    return CustomPaint(
      size: Size.infinite,
      painter: MovingAvgChartPainter(
        originalValues: _values,
        movingAvgValues: _movingAvgValues,
        minY: minY,
        maxY: maxY,
        originalColor: widget.originalLineColor,
        movingAvgColor: widget.movingAvgLineColor,
        textColor: widget.textColor,
        borderColor: widget.borderColor,
        fontSize: widget.fontSize,
      ),
    );
  }
}

/// Custom painter for Moving Average chart
class MovingAvgChartPainter extends CustomPainter {
  final List<double> originalValues;
  final List<double> movingAvgValues;
  final double minY;
  final double maxY;
  final Color originalColor;
  final Color movingAvgColor;
  final Color textColor;
  final Color borderColor;
  final double fontSize;

  MovingAvgChartPainter({
    required this.originalValues,
    required this.movingAvgValues,
    required this.minY,
    required this.maxY,
    required this.originalColor,
    required this.movingAvgColor,
    required this.textColor,
    required this.borderColor,
    required this.fontSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final int dataLength = originalValues.length;
    if (dataLength == 0) return;

    // Setup
    final double width = size.width;
    final double height = size.height;
    final double chartPadding = 40.0;
    final double chartWidth = width - (chartPadding * 2);
    final double chartHeight = height - (chartPadding * 2);
    final double xStep = chartWidth / (dataLength - 1 > 0 ? dataLength - 1 : 1);

    // Draw border
    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    canvas.drawRect(
      Rect.fromLTWH(chartPadding, chartPadding, chartWidth, chartHeight),
      borderPaint,
    );

    // Draw grid lines
    final gridPaint = Paint()
      ..color = borderColor.withAlpha(51)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Horizontal grid lines
    final int horizontalLines = 5;
    for (int i = 0; i <= horizontalLines; i++) {
      final double y = chartPadding + (chartHeight / horizontalLines * i);
      canvas.drawLine(
        Offset(chartPadding, y),
        Offset(chartPadding + chartWidth, y),
        gridPaint,
      );

      // Draw Y-axis labels
      final double value = maxY - ((maxY - minY) / horizontalLines * i);
      final textSpan = TextSpan(
        text: value.toStringAsFixed(1),
        style: TextStyle(
          color: textColor,
          fontSize: fontSize - 4,
        ),
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(chartPadding - textPainter.width - 5, y - textPainter.height / 2),
      );
    }

    // Vertical grid lines
    final int verticalLines = min(dataLength, 10);
    for (int i = 0; i <= verticalLines; i++) {
      final double x = chartPadding + (chartWidth / verticalLines * i);
      canvas.drawLine(
        Offset(x, chartPadding),
        Offset(x, chartPadding + chartHeight),
        gridPaint,
      );

      // Draw X-axis labels
      if (i < dataLength) {
        final int index = (i * (dataLength - 1) / verticalLines).round();
        final textSpan = TextSpan(
          text: index.toString(),
          style: TextStyle(
            color: textColor,
            fontSize: fontSize - 4,
          ),
        );
        final textPainter = TextPainter(
          text: textSpan,
          textDirection: TextDirection.ltr,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(x - textPainter.width / 2, chartPadding + chartHeight + 5),
        );
      }
    }

    // Draw original values line
    final originalPaint = Paint()
      ..color = originalColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final originalPath = Path();
    for (int i = 0; i < dataLength; i++) {
      final double x = chartPadding + (i * xStep);
      final double normalizedY = (originalValues[i] - minY) / (maxY - minY);
      final double y = chartPadding + chartHeight - (normalizedY * chartHeight);

      if (i == 0) {
        originalPath.moveTo(x, y);
      } else {
        originalPath.lineTo(x, y);
      }

      // Draw point
      canvas.drawCircle(
        Offset(x, y),
        4.0,
        Paint()..color = originalColor,
      );
    }
    canvas.drawPath(originalPath, originalPaint);

    // Draw moving average line
    final movingAvgPaint = Paint()
      ..color = movingAvgColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final movingAvgPath = Path();
    for (int i = 0; i < movingAvgValues.length; i++) {
      final double x = chartPadding + (i * xStep);
      final double normalizedY = (movingAvgValues[i] - minY) / (maxY - minY);
      final double y = chartPadding + chartHeight - (normalizedY * chartHeight);

      if (i == 0) {
        movingAvgPath.moveTo(x, y);
      } else {
        movingAvgPath.lineTo(x, y);
      }

      // Draw point
      canvas.drawCircle(
        Offset(x, y),
        4.0,
        Paint()..color = movingAvgColor,
      );
    }
    canvas.drawPath(movingAvgPath, movingAvgPaint);

    // Draw legend
    final legendY = chartPadding / 2;

    // Original values legend
    canvas.drawCircle(
      Offset(chartPadding, legendY),
      4.0,
      Paint()..color = originalColor,
    );

    final originalTextSpan = TextSpan(
      text: 'Original Values',
      style: TextStyle(
        color: textColor,
        fontSize: fontSize - 2,
      ),
    );
    final originalTextPainter = TextPainter(
      text: originalTextSpan,
      textDirection: TextDirection.ltr,
    );
    originalTextPainter.layout();
    originalTextPainter.paint(
      canvas,
      Offset(chartPadding + 10, legendY - originalTextPainter.height / 2),
    );

    // Moving average legend
    final movingAvgLegendX = chartPadding + 10 + originalTextPainter.width + 20;
    canvas.drawCircle(
      Offset(movingAvgLegendX, legendY),
      4.0,
      Paint()..color = movingAvgColor,
    );

    final movingAvgTextSpan = TextSpan(
      text: 'Moving Average',
      style: TextStyle(
        color: textColor,
        fontSize: fontSize - 2,
      ),
    );
    final movingAvgTextPainter = TextPainter(
      text: movingAvgTextSpan,
      textDirection: TextDirection.ltr,
    );
    movingAvgTextPainter.layout();
    movingAvgTextPainter.paint(
      canvas,
      Offset(movingAvgLegendX + 10, legendY - movingAvgTextPainter.height / 2),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}