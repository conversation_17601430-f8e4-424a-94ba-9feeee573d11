import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension RichTextWidget1ColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// A rich text widget that supports multiple display states and text formatting
class RichTextWidget1 extends StatefulWidget {
  // Content properties
  final String initialText;
  final String placeholderText;
  final String memberText;
  final Map<String, dynamic>? jsonData;
  final String displayKey;
  final bool isEditable;

  // Appearance properties
  final Color backgroundColor;
  final Color hoverColor;
  final Color borderColor;
  final Color textColor;
  final double fontSize;
  final FontWeight fontWeight;
  final String? fontFamily;
  final bool isDarkTheme;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double? width;
  final double? height;

  // Animation properties
  final Duration animationDuration;
  final double compactHeight;
  final double expandedHeight;

  // Toolbar properties
  final bool showToolbar;
  final bool showFontSizePicker;
  final bool showBoldButton;
  final bool showItalicButton;
  final bool showUnderlineButton;
  final bool showStrikethroughButton;
  final bool showColorPicker;
  final bool showAlignmentButtons;
  final bool showExpandButton;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool autofocus;

  // Callbacks
  final Function(String)? onTextChanged;
  final Function()? onTap;
  final Function()? onExpand;
  final Function(bool)? onHover;
  final Function(bool)? onFocus;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;

  const RichTextWidget1({
    super.key,
    this.initialText = 'Class',
    this.placeholderText = 'Class',
    this.memberText = 'Member',
    this.jsonData,
    this.displayKey = 'text',
    this.isEditable = true,
    this.backgroundColor = Colors.white,
    this.hoverColor = const Color(0xFFF8F9FA),
    this.borderColor = const Color(0xFFE1E5E9),
    this.textColor = const Color(0xFF2D3748),
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.w400,
    this.fontFamily,
    this.isDarkTheme = false,
    this.borderRadius = 4.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = EdgeInsets.zero,
    this.width,
    this.height,
    this.animationDuration = const Duration(milliseconds: 200),
    this.compactHeight = 36.0,
    this.expandedHeight = 120.0,
    this.showToolbar = true,
    this.showFontSizePicker = true,
    this.showBoldButton = true,
    this.showItalicButton = true,
    this.showUnderlineButton = true,
    this.showStrikethroughButton = true,
    this.showColorPicker = true,
    this.showAlignmentButtons = true,
    this.showExpandButton = true,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.autofocus = false,
    this.onTextChanged,
    this.onTap,
    this.onExpand,
    this.onHover,
    this.onFocus,
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
  });

  /// Creates a RichTextWidget1 from a JSON map
  factory RichTextWidget1.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color textColor = Colors.black;
    if (json.containsKey('textColor')) {
      textColor = _parseColor(json['textColor']);
    }

    Color backgroundColor = Colors.white;
    if (json.containsKey('backgroundColor')) {
      backgroundColor = _parseColor(json['backgroundColor']);
    }

    Color hoverColor = const Color(0xFFF8F9FA);
    if (json.containsKey('hoverColor')) {
      hoverColor = _parseColor(json['hoverColor']);
    }

    Color borderColor = const Color(0xFFE1E5E9);
    if (json.containsKey('borderColor')) {
      borderColor = _parseColor(json['borderColor']);
    }

    // Parse font weight
    FontWeight fontWeight = FontWeight.w400;
    if (json.containsKey('fontWeight')) {
      fontWeight = _parseFontWeight(json['fontWeight']);
    }

    // Parse padding and margin
    EdgeInsetsGeometry padding = const EdgeInsets.symmetric(
      horizontal: 12.0,
      vertical: 8.0,
    );
    if (json.containsKey('padding')) {
      padding = _parseEdgeInsets(json['padding']);
    }

    EdgeInsetsGeometry margin = EdgeInsets.zero;
    if (json.containsKey('margin')) {
      margin = _parseEdgeInsets(json['margin']);
    }

    // Parse animation duration
    Duration animationDuration = const Duration(milliseconds: 200);
    if (json.containsKey('animationDuration')) {
      animationDuration = Duration(
        milliseconds: json['animationDuration'] as int? ?? 200,
      );
    }

    return RichTextWidget1(
      initialText: json['initialText'] as String? ?? 'Class',
      placeholderText: json['placeholderText'] as String? ?? 'Class',
      memberText: json['memberText'] as String? ?? 'Member',
      jsonData: json['jsonData'] as Map<String, dynamic>?,
      displayKey: json['displayKey'] as String? ?? 'text',
      isEditable: json['isEditable'] as bool? ?? true,
      textColor: textColor,
      backgroundColor: backgroundColor,
      hoverColor: hoverColor,
      borderColor: borderColor,
      fontSize:
          json['fontSize'] != null
              ? (json['fontSize'] as num).toDouble()
              : 14.0,
      fontWeight: fontWeight,
      fontFamily: json['fontFamily'] as String?,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 4.0,
      padding: padding,
      margin: margin,
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height:
          json['height'] != null ? (json['height'] as num).toDouble() : null,
      animationDuration: animationDuration,
      compactHeight:
          json['compactHeight'] != null
              ? (json['compactHeight'] as num).toDouble()
              : 36.0,
      expandedHeight:
          json['expandedHeight'] != null
              ? (json['expandedHeight'] as num).toDouble()
              : 120.0,
      showToolbar: json['showToolbar'] as bool? ?? true,
      showFontSizePicker: json['showFontSizePicker'] as bool? ?? true,
      showBoldButton: json['showBoldButton'] as bool? ?? true,
      showItalicButton: json['showItalicButton'] as bool? ?? true,
      showUnderlineButton: json['showUnderlineButton'] as bool? ?? true,
      showStrikethroughButton: json['showStrikethroughButton'] as bool? ?? true,
      showColorPicker: json['showColorPicker'] as bool? ?? true,
      showAlignmentButtons: json['showAlignmentButtons'] as bool? ?? true,
      showExpandButton: json['showExpandButton'] as bool? ?? true,
      isReadOnly: json['isReadOnly'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      autofocus: json['autofocus'] as bool? ?? false,
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      jsonCallbacks:
          json.containsKey('callbacks')
              ? json['callbacks'] as Map<String, dynamic>
              : null,
    );
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex';
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'transparent':
            return Colors.transparent;
          default:
            return Colors.black;
        }
      }
    } else if (colorValue is Map) {
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a =
          colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return Colors.black;
  }

  /// Parses font weight from string or int
  static FontWeight _parseFontWeight(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'thin':
          return FontWeight.w100;
        case 'extralight':
          return FontWeight.w200;
        case 'light':
          return FontWeight.w300;
        case 'regular':
          return FontWeight.w400;
        case 'medium':
          return FontWeight.w500;
        case 'semibold':
          return FontWeight.w600;
        case 'bold':
          return FontWeight.w700;
        case 'extrabold':
          return FontWeight.w800;
        case 'black':
          return FontWeight.w900;
        default:
          return FontWeight.w400;
      }
    } else if (value is int) {
      switch (value) {
        case 100:
          return FontWeight.w100;
        case 200:
          return FontWeight.w200;
        case 300:
          return FontWeight.w300;
        case 400:
          return FontWeight.w400;
        case 500:
          return FontWeight.w500;
        case 600:
          return FontWeight.w600;
        case 700:
          return FontWeight.w700;
        case 800:
          return FontWeight.w800;
        case 900:
          return FontWeight.w900;
        default:
          return FontWeight.w400;
      }
    }
    return FontWeight.w400;
  }

  /// Parses edge insets from a map or number
  static EdgeInsetsGeometry _parseEdgeInsets(dynamic value) {
    if (value is num) {
      return EdgeInsets.all(value.toDouble());
    } else if (value is Map) {
      if (value.containsKey('all')) {
        return EdgeInsets.all((value['all'] as num).toDouble());
      } else if (value.containsKey('horizontal') ||
          value.containsKey('vertical')) {
        return EdgeInsets.symmetric(
          horizontal:
              value.containsKey('horizontal')
                  ? (value['horizontal'] as num).toDouble()
                  : 0.0,
          vertical:
              value.containsKey('vertical')
                  ? (value['vertical'] as num).toDouble()
                  : 0.0,
        );
      } else {
        return EdgeInsets.fromLTRB(
          value.containsKey('left') ? (value['left'] as num).toDouble() : 0.0,
          value.containsKey('top') ? (value['top'] as num).toDouble() : 0.0,
          value.containsKey('right') ? (value['right'] as num).toDouble() : 0.0,
          value.containsKey('bottom')
              ? (value['bottom'] as num).toDouble()
              : 0.0,
        );
      }
    }
    return const EdgeInsets.all(0.0);
  }

  @override
  State<RichTextWidget1> createState() => _RichTextWidget1State();
}

class _RichTextWidget1State extends State<RichTextWidget1>
    with TickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _hoverAnimationController;
  late Animation<double> _heightAnimation;
  late Animation<Color?> _backgroundColorAnimation;

  bool _isHovered = false;
  bool _isFocused = false;
  String _currentText = '';

  // Text formatting state
  bool _isBold = false;
  bool _isItalic = false;
  bool _isUnderlined = false;
  bool _isStrikethrough = false;
  Color _selectedTextColor = Colors.black;
  double _selectedFontSize = 14.0;
  TextAlign _textAlign = TextAlign.left;

  @override
  void initState() {
    super.initState();
    _currentText = widget.initialText;
    _controller = TextEditingController(text: _currentText);
    _focusNode = FocusNode();
    _selectedTextColor = widget.textColor;
    _selectedFontSize = widget.fontSize;

    // Initialize animation controller
    _hoverAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    // Height animation for hover state
    _heightAnimation = Tween<double>(
      begin: 36.0, // Default height
      end: 120.0, // Expanded height with toolbar
    ).animate(
      CurvedAnimation(
        parent: _hoverAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // Background color animation
    _backgroundColorAnimation = ColorTween(
      begin: widget.backgroundColor,
      end: widget.hoverColor,
    ).animate(
      CurvedAnimation(
        parent: _hoverAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // Add listeners
    _controller.addListener(_handleTextChange);
    _focusNode.addListener(_handleFocusChange);
  }

  @override
  void dispose() {
    _controller.removeListener(_handleTextChange);
    _controller.dispose();
    _focusNode.removeListener(_handleFocusChange);
    _focusNode.dispose();
    _hoverAnimationController.dispose();
    super.dispose();
  }

  void _handleTextChange() {
    final newText = _controller.text;
    if (_currentText != newText) {
      setState(() {
        _currentText = newText;
      });
      widget.onTextChanged?.call(newText);
    }
  }

  void _handleFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
    widget.onFocus?.call(_isFocused);
  }

  void _handleHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });

    if (isHovered && !_isFocused) {
      _hoverAnimationController.forward();
    } else if (!isHovered && !_isFocused) {
      _hoverAnimationController.reverse();
    }

    widget.onHover?.call(isHovered);
  }

  void _handleTap() {
    if (widget.isEditable) {
      _focusNode.requestFocus();
      if (!_isHovered) {
        _hoverAnimationController.forward();
      }
    }
    widget.onTap?.call();
  }

  void _handleExpand() {
    _showExpandedEditor();
    widget.onExpand?.call();
  }

  void _showExpandedEditor() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder:
          (context) => _ExpandedEditor(
            initialText: _currentText,
            onTextChanged: (text) {
              setState(() {
                _currentText = text;
                _controller.text = text;
              });
              widget.onTextChanged?.call(text);
            },
            isDarkTheme: widget.isDarkTheme,
            fontSize: widget.fontSize,
            fontFamily: widget.fontFamily,
          ),
    );
  }

  // Formatting methods
  void _toggleBold() {
    setState(() {
      _isBold = !_isBold;
    });
  }

  void _toggleItalic() {
    setState(() {
      _isItalic = !_isItalic;
    });
  }

  void _toggleUnderline() {
    setState(() {
      _isUnderlined = !_isUnderlined;
    });
  }

  void _toggleStrikethrough() {
    setState(() {
      _isStrikethrough = !_isStrikethrough;
    });
  }

  void _changeFontSize(double size) {
    setState(() {
      _selectedFontSize = size;
    });
  }

  void _changeTextAlign(TextAlign align) {
    setState(() {
      _textAlign = align;
    });
  }

  Widget _buildDefaultState() {
    return Container(
      height: 36.0,
      child: Row(
        children: [
          Expanded(
            child: Text(
              _currentText.isEmpty ? widget.placeholderText : _currentText,
              style: TextStyle(
                color: widget.textColor,
                fontSize: _getResponsiveValueFontSize(context),
                fontWeight: widget.fontWeight,
                fontFamily: widget.fontFamily,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          const SizedBox(width: 8.0),
          Icon(Icons.edit_outlined, size: 16.0, color: Colors.grey.shade600),
        ],
      ),
    );
  }

  Widget _buildHoverState() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text content area
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (_isFocused)
                Expanded(
                  child: TextField(
                    controller: _controller,
                    focusNode: _focusNode,
                    style: TextStyle(
                      color: widget.textColor,
                      fontSize: _selectedFontSize,
                      fontWeight: _isBold ? FontWeight.bold : FontWeight.normal,
                      fontStyle:
                          _isItalic ? FontStyle.italic : FontStyle.normal,
                      decoration: _getTextDecoration(),
                      fontFamily: widget.fontFamily,
                    ),
                    textAlign: _textAlign,
                    maxLines: null,
                    expands: true,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: 'Enter your text...',
                      hintStyle: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: _selectedFontSize,
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                )
              else
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _currentText.isEmpty
                          ? widget.placeholderText
                          : _currentText,
                      style: TextStyle(
                        color: widget.textColor,
                        fontSize: widget.fontSize,
                        fontWeight: widget.fontWeight,
                        fontFamily: widget.fontFamily,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4.0),
                    Text(
                      'Member',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12.0,
                        fontWeight: FontWeight.w400,
                        fontFamily: widget.fontFamily,
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
        // Bottom toolbar
        _buildBottomToolbar(),
      ],
    );
  }

  Widget _buildBottomToolbar() {
    return Container(
      height: 36.0,
      margin: const EdgeInsets.only(top: 8.0),
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(4.0),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          // Font size dropdown
          DropdownButton<double>(
            value: _selectedFontSize,
            underline: const SizedBox(),
            style: TextStyle(color: Colors.grey.shade700, fontSize: 12.0),
            items:
                [10.0, 12.0, 14.0, 16.0, 18.0, 20.0, 24.0].map((size) {
                  return DropdownMenuItem(
                    value: size,
                    child: Text('${size.toInt()}'),
                  );
                }).toList(),
            onChanged: (size) {
              if (size != null) _changeFontSize(size);
            },
          ),
          const SizedBox(width: 8.0),
          // Text alignment
          _buildToolbarButton(
            Icons.format_align_left,
            _textAlign == TextAlign.left,
            () => _changeTextAlign(TextAlign.left),
          ),
          // Bold
          _buildToolbarButton(Icons.format_bold, _isBold, _toggleBold),
          // Italic
          _buildToolbarButton(Icons.format_italic, _isItalic, _toggleItalic),
          // Underline
          _buildToolbarButton(
            Icons.format_underlined,
            _isUnderlined,
            _toggleUnderline,
          ),
          // Strikethrough
          _buildToolbarButton(
            Icons.strikethrough_s,
            _isStrikethrough,
            _toggleStrikethrough,
          ),
          // Color picker
          _buildColorPicker(),
          const Spacer(),
          // Expand button
          _buildToolbarButton(Icons.open_in_full, false, _handleExpand),
        ],
      ),
    );
  }

  Widget _buildToolbarButton(
    IconData icon,
    bool isActive,
    VoidCallback onPressed,
  ) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(4.0),
      child: Container(
        width: 28.0,
        height: 28.0,
        decoration: BoxDecoration(
          color: isActive ? Colors.blue.shade100 : Colors.transparent,
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Icon(
          icon,
          size: 16.0,
          color: isActive ? Colors.blue.shade700 : Colors.grey.shade600,
        ),
      ),
    );
  }

  Widget _buildColorPicker() {
    return PopupMenuButton<Color>(
      child: Container(
        width: 28.0,
        height: 28.0,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(4.0)),
        child: Icon(
          Icons.format_color_text,
          size: 16.0,
          color: _selectedTextColor,
        ),
      ),
      itemBuilder:
          (context) => [
            PopupMenuItem(
              value: Colors.black,
              child: _ColorOption(color: Colors.black, name: 'Black'),
            ),
            PopupMenuItem(
              value: Colors.red,
              child: _ColorOption(color: Colors.red, name: 'Red'),
            ),
            PopupMenuItem(
              value: Colors.blue,
              child: _ColorOption(color: Colors.blue, name: 'Blue'),
            ),
            PopupMenuItem(
              value: Colors.green,
              child: _ColorOption(color: Colors.green, name: 'Green'),
            ),
            PopupMenuItem(
              value: Colors.orange,
              child: _ColorOption(color: Colors.orange, name: 'Orange'),
            ),
            PopupMenuItem(
              value: Colors.purple,
              child: _ColorOption(color: Colors.purple, name: 'Purple'),
            ),
          ],
      onSelected: (color) {
        setState(() {
          _selectedTextColor = color;
        });
      },
    );
  }

  TextDecoration _getTextDecoration() {
    List<TextDecoration> decorations = [];
    if (_isUnderlined) decorations.add(TextDecoration.underline);
    if (_isStrikethrough) decorations.add(TextDecoration.lineThrough);

    if (decorations.isEmpty) return TextDecoration.none;
    if (decorations.length == 1) return decorations.first;
    return TextDecoration.combine(decorations);
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _handleHover(true),
      onExit: (_) => _handleHover(false),
      child: GestureDetector(
        onTap: _handleTap,
        child: AnimatedBuilder(
          animation: _hoverAnimationController,
          builder: (context, child) {
            final isExpanded = _isHovered || _isFocused;
            return Container(
              width: widget.width,
              height:
                  isExpanded
                      ? _heightAnimation.value
                      : _getResponsiveHeight(context),
              //height: _getResponsiveHeight(context),
              margin: widget.margin,
              padding: EdgeInsets.only(left: 16, right: 16),
              decoration: BoxDecoration(
                color:
                    _backgroundColorAnimation.value ?? widget.backgroundColor,
                //borderRadius: BorderRadius.circular(widget.borderRadius),
                borderRadius: BorderRadius.circular(4.0),
                border: Border.all(
                  color: _isFocused ? Colors.blue.shade300 : widget.borderColor,
                  width: _isFocused ? 2.0 : 1.0,
                ),
                boxShadow:
                    _isFocused
                        ? [
                          BoxShadow(
                            color: Colors.blue.shade100,
                            blurRadius: 4.0,
                            offset: const Offset(0, 2),
                          ),
                        ]
                        : null,
              ),
              child: isExpanded ? _buildHoverState() : _buildDefaultState(),
            );
          },
        ),
      ),
    );
  }
}

/// Color option widget for the color picker
class _ColorOption extends StatelessWidget {
  final Color color;
  final String name;

  const _ColorOption({required this.color, required this.name});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 20.0,
          height: 20.0,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(3.0),
            border: Border.all(color: Colors.grey.shade300),
          ),
        ),
        const SizedBox(width: 8.0),
        Text(name),
      ],
    );
  }
}

/// Expanded editor dialog
class _ExpandedEditor extends StatefulWidget {
  final String initialText;
  final Function(String) onTextChanged;
  final bool isDarkTheme;
  final double fontSize;
  final String? fontFamily;

  const _ExpandedEditor({
    required this.initialText,
    required this.onTextChanged,
    this.isDarkTheme = false,
    this.fontSize = 16.0,
    this.fontFamily,
  });

  @override
  State<_ExpandedEditor> createState() => _ExpandedEditorState();
}

class _ExpandedEditorState extends State<_ExpandedEditor> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  String _currentText = '';
  int _wordCount = 0;

  // Formatting state
  bool _isBold = false;
  bool _isItalic = false;
  bool _isUnderlined = false;
  bool _isStrikethrough = false;
  Color _selectedColor = Colors.black;
  double _selectedFontSize = 16.0;
  TextAlign _textAlign = TextAlign.left;
  String _selectedFont = 'Arial';

  @override
  void initState() {
    super.initState();
    _currentText = widget.initialText;
    _controller = TextEditingController(text: _currentText);
    _focusNode = FocusNode();
    _selectedFontSize = widget.fontSize;
    _selectedColor = widget.isDarkTheme ? Colors.white : Colors.black;
    _updateWordCount();

    _controller.addListener(_handleTextChange);
  }

  @override
  void dispose() {
    _controller.removeListener(_handleTextChange);
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _handleTextChange() {
    final newText = _controller.text;
    if (_currentText != newText) {
      setState(() {
        _currentText = newText;
        _updateWordCount();
      });
      widget.onTextChanged(newText);
    }
  }

  void _updateWordCount() {
    _wordCount =
        _currentText.trim().isEmpty
            ? 0
            : _currentText.trim().split(RegExp(r'\s+')).length;
  }

  void _toggleBold() => setState(() => _isBold = !_isBold);
  void _toggleItalic() => setState(() => _isItalic = !_isItalic);
  void _toggleUnderline() => setState(() => _isUnderlined = !_isUnderlined);
  void _toggleStrikethrough() =>
      setState(() => _isStrikethrough = !_isStrikethrough);

  void _changeFont(String font) => setState(() => _selectedFont = font);
  void _changeFontSize(double size) => setState(() => _selectedFontSize = size);
  void _changeColor(Color color) => setState(() => _selectedColor = color);
  void _changeAlignment(TextAlign align) => setState(() => _textAlign = align);

  void _insertBulletList() {
    final text = _controller.text;
    final selection = _controller.selection;
    final newText = text.replaceRange(
      selection.start,
      selection.end,
      '• ${selection.textInside(text)}',
    );
    _controller.text = newText;
    _controller.selection = TextSelection.collapsed(
      offset: selection.start + 2,
    );
  }

  void _insertNumberedList() {
    final text = _controller.text;
    final selection = _controller.selection;
    final newText = text.replaceRange(
      selection.start,
      selection.end,
      '1. ${selection.textInside(text)}',
    );
    _controller.text = newText;
    _controller.selection = TextSelection.collapsed(
      offset: selection.start + 3,
    );
  }

  void _insertLink() {
    showDialog(
      context: context,
      builder:
          (context) => _LinkDialog(
            onInsert: (text, url) {
              final currentText = _controller.text;
              final selection = _controller.selection;
              final linkText = '[$text]($url)';
              final newText = currentText.replaceRange(
                selection.start,
                selection.end,
                linkText,
              );
              _controller.text = newText;
              _controller.selection = TextSelection.collapsed(
                offset: selection.start + linkText.length,
              );
            },
          ),
    );
  }

  void _clearFormatting() {
    setState(() {
      _isBold = false;
      _isItalic = false;
      _isUnderlined = false;
      _isStrikethrough = false;
      _selectedColor = widget.isDarkTheme ? Colors.white : Colors.black;
      _selectedFontSize = widget.fontSize;
      _textAlign = TextAlign.left;
    });
  }

  Widget _buildTopToolbar() {
    return Container(
      height: 60.0,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: widget.isDarkTheme ? Colors.grey.shade800 : Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(
            color:
                widget.isDarkTheme
                    ? Colors.grey.shade600
                    : Colors.grey.shade200,
          ),
        ),
      ),
      child: Row(
        children: [
          // Font dropdown
          DropdownButton<String>(
            value: _selectedFont,
            underline: const SizedBox(),
            items:
                ['Arial', 'Times New Roman', 'Helvetica', 'Georgia', 'Verdana']
                    .map(
                      (font) =>
                          DropdownMenuItem(value: font, child: Text(font)),
                    )
                    .toList(),
            onChanged: (font) {
              if (font != null) _changeFont(font);
            },
          ),
          const SizedBox(width: 16.0),
          // Font size dropdown
          DropdownButton<double>(
            value: _selectedFontSize,
            underline: const SizedBox(),
            items:
                [10.0, 12.0, 14.0, 16.0, 18.0, 20.0, 24.0, 28.0, 32.0]
                    .map(
                      (size) => DropdownMenuItem(
                        value: size,
                        child: Text('${size.toInt()}'),
                      ),
                    )
                    .toList(),
            onChanged: (size) {
              if (size != null) _changeFontSize(size);
            },
          ),
          const SizedBox(width: 16.0),
          // Formatting buttons
          _buildToolbarButton(Icons.format_bold, _isBold, _toggleBold),
          _buildToolbarButton(Icons.format_italic, _isItalic, _toggleItalic),
          _buildToolbarButton(
            Icons.format_underlined,
            _isUnderlined,
            _toggleUnderline,
          ),
          _buildToolbarButton(
            Icons.strikethrough_s,
            _isStrikethrough,
            _toggleStrikethrough,
          ),
          const SizedBox(width: 8.0),
          // Color picker
          PopupMenuButton<Color>(
            child: Icon(Icons.format_color_text, color: _selectedColor),
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: Colors.black,
                    child: _ColorOption(color: Colors.black, name: 'Black'),
                  ),
                  PopupMenuItem(
                    value: Colors.red,
                    child: _ColorOption(color: Colors.red, name: 'Red'),
                  ),
                  PopupMenuItem(
                    value: Colors.blue,
                    child: _ColorOption(color: Colors.blue, name: 'Blue'),
                  ),
                  PopupMenuItem(
                    value: Colors.green,
                    child: _ColorOption(color: Colors.green, name: 'Green'),
                  ),
                  PopupMenuItem(
                    value: Colors.orange,
                    child: _ColorOption(color: Colors.orange, name: 'Orange'),
                  ),
                  PopupMenuItem(
                    value: Colors.purple,
                    child: _ColorOption(color: Colors.purple, name: 'Purple'),
                  ),
                ],
            onSelected: _changeColor,
          ),
          const SizedBox(width: 8.0),
          // Alignment buttons
          PopupMenuButton<TextAlign>(
            child: const Icon(Icons.format_align_left),
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: TextAlign.left,
                    child: Row(
                      children: [
                        Icon(Icons.format_align_left),
                        SizedBox(width: 8),
                        Text('Left'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: TextAlign.center,
                    child: Row(
                      children: [
                        Icon(Icons.format_align_center),
                        SizedBox(width: 8),
                        Text('Center'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: TextAlign.right,
                    child: Row(
                      children: [
                        Icon(Icons.format_align_right),
                        SizedBox(width: 8),
                        Text('Right'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: TextAlign.justify,
                    child: Row(
                      children: [
                        Icon(Icons.format_align_justify),
                        SizedBox(width: 8),
                        Text('Justify'),
                      ],
                    ),
                  ),
                ],
            onSelected: _changeAlignment,
          ),
          const SizedBox(width: 8.0),
          // List buttons
          _buildToolbarButton(
            Icons.format_list_bulleted,
            false,
            _insertBulletList,
          ),
          _buildToolbarButton(
            Icons.format_list_numbered,
            false,
            _insertNumberedList,
          ),
          _buildToolbarButton(Icons.link, false, _insertLink),
          _buildToolbarButton(Icons.format_clear, false, _clearFormatting),
          const Spacer(),
          // Close button
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  Widget _buildToolbarButton(
    IconData icon,
    bool isActive,
    VoidCallback onPressed,
  ) {
    return IconButton(
      icon: Icon(icon),
      onPressed: onPressed,
      color: isActive ? Colors.blue : null,
      iconSize: 20.0,
    );
  }

  Widget _buildBottomBar() {
    return Container(
      height: 40.0,
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      decoration: BoxDecoration(
        color: widget.isDarkTheme ? Colors.grey.shade800 : Colors.grey.shade50,
        border: Border(
          top: BorderSide(
            color:
                widget.isDarkTheme
                    ? Colors.grey.shade600
                    : Colors.grey.shade200,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            '$_wordCount Words',
            style: TextStyle(
              color:
                  widget.isDarkTheme
                      ? Colors.grey.shade400
                      : Colors.grey.shade600,
              fontSize: 12.0,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.keyboard_arrow_down),
            onPressed: () => Navigator.of(context).pop(),
            iconSize: 20.0,
          ),
        ],
      ),
    );
  }

  TextDecoration _getTextDecoration() {
    List<TextDecoration> decorations = [];
    if (_isUnderlined) decorations.add(TextDecoration.underline);
    if (_isStrikethrough) decorations.add(TextDecoration.lineThrough);

    if (decorations.isEmpty) return TextDecoration.none;
    if (decorations.length == 1) return decorations.first;
    return TextDecoration.combine(decorations);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.all(20.0),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: widget.isDarkTheme ? Colors.grey.shade900 : Colors.white,
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Column(
          children: [
            _buildTopToolbar(),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextField(
                  controller: _controller,
                  focusNode: _focusNode,
                  style: TextStyle(
                    color: _selectedColor,
                    fontSize: _selectedFontSize,
                    fontWeight: _isBold ? FontWeight.bold : FontWeight.normal,
                    fontStyle: _isItalic ? FontStyle.italic : FontStyle.normal,
                    decoration: _getTextDecoration(),
                    fontFamily:
                        _selectedFont == 'Arial'
                            ? widget.fontFamily
                            : _selectedFont,
                  ),
                  textAlign: _textAlign,
                  maxLines: null,
                  expands: true,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText:
                        'Lorem Ipsum is Simply Dummy Text Of The Printing And Typesetting Industry...',
                    hintStyle: TextStyle(
                      color:
                          widget.isDarkTheme
                              ? Colors.grey.shade500
                              : Colors.grey.shade400,
                      fontSize: _selectedFontSize,
                    ),
                  ),
                  autofocus: true,
                ),
              ),
            ),
            _buildBottomBar(),
          ],
        ),
      ),
    );
  }
}

/// Link insertion dialog
class _LinkDialog extends StatefulWidget {
  final Function(String text, String url) onInsert;

  const _LinkDialog({required this.onInsert});

  @override
  State<_LinkDialog> createState() => _LinkDialogState();
}

class _LinkDialogState extends State<_LinkDialog> {
  final _textController = TextEditingController();
  final _urlController = TextEditingController();

  @override
  void dispose() {
    _textController.dispose();
    _urlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Insert Link'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _textController,
            decoration: const InputDecoration(
              labelText: 'Display Text',
              hintText: 'Enter text to display',
            ),
          ),
          const SizedBox(height: 16.0),
          TextField(
            controller: _urlController,
            decoration: const InputDecoration(
              labelText: 'URL',
              hintText: 'Enter URL',
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            final text = _textController.text.trim();
            final url = _urlController.text.trim();
            if (text.isNotEmpty && url.isNotEmpty) {
              widget.onInsert(text, url);
              Navigator.of(context).pop();
            }
          },
          child: const Text('Insert'),
        ),
      ],
    );
  }
}

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 32.0; // Small (768-1024px)
  } else {
    return 32.0; // Default for very small screens
  }
}

double _getResponsiveValueFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 14.0; // Default for very small screens
  }
}
