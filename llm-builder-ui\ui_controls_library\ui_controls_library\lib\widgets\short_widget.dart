import 'package:flutter/material.dart';

/// A widget that calculates and displays short selling metrics and related financial data.
///
/// This widget allows users to input values related to short selling and calculate
/// various metrics such as short interest, days to cover, short ratio, and potential
/// profit/loss scenarios.
class ShortWidget extends StatefulWidget {
  /// Initial stock price
  final double? initialStockPrice;

  /// Initial short price (entry price)
  final double? initialShortPrice;

  /// Initial number of shares
  final int? initialShares;

  /// Initial short interest percentage
  final double? initialShortInterest;

  /// Initial average daily volume
  final int? initialAverageDailyVolume;

  /// Initial short type
  final ShortType shortType;

  /// Whether to show the short interest visualization
  final bool showShortInterestVisualization;

  /// Whether to show the profit/loss calculation
  final bool showProfitLossCalculation;

  /// Whether to show the risk analysis
  final bool showRiskAnalysis;

  /// Whether to show the short squeeze potential
  final bool showShortSqueezePotential;

  /// Whether to allow editing of the values
  final bool isReadOnly;

  /// Whether the widget is disabled
  final bool isDisabled;

  /// The title or label for the widget
  final String? title;

  /// Helper text to display below the inputs
  final String? helperText;

  /// Error text to display when there's an input error
  final String? errorText;

  /// The color of the text
  final Color textColor;

  /// The background color of the widget
  final Color backgroundColor;

  /// The color of the border
  final Color borderColor;

  /// The width of the border
  final double borderWidth;

  /// The radius of the border corners
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The font size for the text
  final double fontSize;

  /// The font weight for the text
  final FontWeight fontWeight;

  /// The color for profit values
  final Color profitColor;

  /// The color for loss values
  final Color lossColor;

  /// The color for neutral values
  final Color neutralColor;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// Callback when the short calculation results change
  final Function(Map<String, dynamic>)? onCalculationComplete;

  /// Creates a short widget.
  const ShortWidget({
    super.key,
    this.initialStockPrice,
    this.initialShortPrice,
    this.initialShares,
    this.initialShortInterest,
    this.initialAverageDailyVolume,
    this.shortType = ShortType.stock,
    this.showShortInterestVisualization = true,
    this.showProfitLossCalculation = true,
    this.showRiskAnalysis = true,
    this.showShortSqueezePotential = true,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.title,
    this.helperText,
    this.errorText,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasBorder = true,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.profitColor = Colors.green,
    this.lossColor = Colors.red,
    this.neutralColor = Colors.blue,
    this.width,
    this.height,
    this.onCalculationComplete,
  });

  @override
  State<ShortWidget> createState() => _ShortWidgetState();
}

/// Enum for short types
enum ShortType {
  /// Short selling a stock
  stock,

  /// Short selling an ETF
  etf,

  /// Short selling a futures contract
  futures,

  /// Short selling an option
  option,

  /// Short selling a cryptocurrency
  crypto,
}

class _ShortWidgetState extends State<ShortWidget> {
  final TextEditingController _stockPriceController = TextEditingController();
  final TextEditingController _shortPriceController = TextEditingController();
  final TextEditingController _sharesController = TextEditingController();
  final TextEditingController _shortInterestController = TextEditingController();
  final TextEditingController _avgDailyVolumeController = TextEditingController();

  ShortType _shortType = ShortType.stock;
  double? _stockPrice;
  double? _shortPrice;
  int? _shares;
  double? _shortInterest;
  int? _avgDailyVolume;
  String? _errorMessage;

  // Calculation results
  double? _profitLoss;
  double? _profitLossPercentage;
  double? _daysTocover;
  double? _shortRatio;
  double? _shortSqueezeRisk;
  double? _maxLossRisk;

  // Map of short types to their labels
  final Map<ShortType, String> _shortTypeLabels = {
    ShortType.stock: 'Stock',
    ShortType.etf: 'ETF',
    ShortType.futures: 'Futures',
    ShortType.option: 'Option',
    ShortType.crypto: 'Cryptocurrency',
  };

  // Map of short types to their descriptions
  final Map<ShortType, String> _shortTypeDescriptions = {
    ShortType.stock: 'Short selling individual stocks involves borrowing shares and selling them with the expectation of buying them back at a lower price.',
    ShortType.etf: 'Short selling ETFs allows you to bet against entire sectors, indices, or asset classes rather than individual securities.',
    ShortType.futures: 'Short selling futures contracts involves taking a position that profits from falling prices in commodities, indices, or other assets.',
    ShortType.option: 'Using options to create a short position, typically by buying put options or selling call options.',
    ShortType.crypto: 'Short selling cryptocurrencies involves borrowing and selling digital assets with the expectation of buying them back at a lower price.',
  };

  @override
  void initState() {
    super.initState();

    _shortType = widget.shortType;

    // Initialize with provided values
    if (widget.initialStockPrice != null) {
      _stockPrice = widget.initialStockPrice;
      _stockPriceController.text = _stockPrice.toString();
    }

    if (widget.initialShortPrice != null) {
      _shortPrice = widget.initialShortPrice;
      _shortPriceController.text = _shortPrice.toString();
    }

    if (widget.initialShares != null) {
      _shares = widget.initialShares;
      _sharesController.text = _shares.toString();
    }

    if (widget.initialShortInterest != null) {
      _shortInterest = widget.initialShortInterest;
      _shortInterestController.text = _shortInterest.toString();
    }

    if (widget.initialAverageDailyVolume != null) {
      _avgDailyVolume = widget.initialAverageDailyVolume;
      _avgDailyVolumeController.text = _avgDailyVolume.toString();
    }

    // Calculate initial values if all required fields are provided
    if (_stockPrice != null && _shortPrice != null && _shares != null) {
      _calculateProfitLoss();
    }

    if (_shortInterest != null && _avgDailyVolume != null) {
      _calculateShortMetrics();
    }
  }

  @override
  void dispose() {
    _stockPriceController.dispose();
    _shortPriceController.dispose();
    _sharesController.dispose();
    _shortInterestController.dispose();
    _avgDailyVolumeController.dispose();
    super.dispose();
  }

  void _parseStockPrice() {
    try {
      final input = _stockPriceController.text.trim();
      if (input.isEmpty) {
        setState(() {
          _stockPrice = null;
          _errorMessage = 'Please enter a stock price';
        });
        return;
      }

      final value = double.parse(input);
      if (value <= 0) {
        setState(() {
          _errorMessage = 'Stock price must be greater than zero';
        });
        return;
      }

      setState(() {
        _stockPrice = value;
        _errorMessage = null;
      });

      if (_shortPrice != null && _shares != null) {
        _calculateProfitLoss();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid stock price format';
      });
    }
  }

  void _parseShortPrice() {
    try {
      final input = _shortPriceController.text.trim();
      if (input.isEmpty) {
        setState(() {
          _shortPrice = null;
          _errorMessage = 'Please enter a short price';
        });
        return;
      }

      final value = double.parse(input);
      if (value <= 0) {
        setState(() {
          _errorMessage = 'Short price must be greater than zero';
        });
        return;
      }

      setState(() {
        _shortPrice = value;
        _errorMessage = null;
      });

      if (_stockPrice != null && _shares != null) {
        _calculateProfitLoss();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid short price format';
      });
    }
  }

  void _parseShares() {
    try {
      final input = _sharesController.text.trim();
      if (input.isEmpty) {
        setState(() {
          _shares = null;
          _errorMessage = 'Please enter number of shares';
        });
        return;
      }

      final value = int.parse(input);
      if (value <= 0) {
        setState(() {
          _errorMessage = 'Number of shares must be greater than zero';
        });
        return;
      }

      setState(() {
        _shares = value;
        _errorMessage = null;
      });

      if (_stockPrice != null && _shortPrice != null) {
        _calculateProfitLoss();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid shares format';
      });
    }
  }

  void _parseShortInterest() {
    try {
      final input = _shortInterestController.text.trim();
      if (input.isEmpty) {
        setState(() {
          _shortInterest = null;
          _errorMessage = 'Please enter short interest percentage';
        });
        return;
      }

      final value = double.parse(input);
      if (value < 0 || value > 100) {
        setState(() {
          _errorMessage = 'Short interest must be between 0 and 100';
        });
        return;
      }

      setState(() {
        _shortInterest = value;
        _errorMessage = null;
      });

      if (_avgDailyVolume != null) {
        _calculateShortMetrics();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid short interest format';
      });
    }
  }

  void _parseAvgDailyVolume() {
    try {
      final input = _avgDailyVolumeController.text.trim();
      if (input.isEmpty) {
        setState(() {
          _avgDailyVolume = null;
          _errorMessage = 'Please enter average daily volume';
        });
        return;
      }

      final value = int.parse(input);
      if (value <= 0) {
        setState(() {
          _errorMessage = 'Average daily volume must be greater than zero';
        });
        return;
      }

      setState(() {
        _avgDailyVolume = value;
        _errorMessage = null;
      });

      if (_shortInterest != null) {
        _calculateShortMetrics();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid average daily volume format';
      });
    }
  }

  void _calculateProfitLoss() {
    if (_stockPrice == null || _shortPrice == null || _shares == null) {
      setState(() {
        _profitLoss = null;
        _profitLossPercentage = null;
      });
      return;
    }

    try {
      // For short selling, profit is made when the stock price goes down
      // Profit = (Short Price - Current Price) * Number of Shares
      final profitLoss = (_shortPrice! - _stockPrice!) * _shares!;
      final profitLossPercentage = ((_shortPrice! - _stockPrice!) / _shortPrice!) * 100;

      setState(() {
        _profitLoss = profitLoss;
        _profitLossPercentage = profitLossPercentage;
        _maxLossRisk = _shortPrice! * _shares!; // Theoretical maximum loss (if stock price goes to infinity)
        _errorMessage = null;
      });

      _notifyCalculationComplete();
    } catch (e) {
      setState(() {
        _errorMessage = 'Error calculating profit/loss: ${e.toString()}';
      });
    }
  }

  void _calculateShortMetrics() {
    if (_shortInterest == null || _avgDailyVolume == null) {
      setState(() {
        _daysTocover = null;
        _shortRatio = null;
        _shortSqueezeRisk = null;
      });
      return;
    }

    try {
      // Assuming total outstanding shares is 10 million for this example
      // In a real application, this would be a parameter or fetched from an API
      const totalOutstandingShares = 10000000;

      // Calculate short interest in shares
      final shortInterestShares = (totalOutstandingShares * _shortInterest!) / 100;

      // Days to cover = Short Interest (shares) / Average Daily Volume
      final daysTocover = shortInterestShares / _avgDailyVolume!;

      // Short ratio is another name for days to cover
      final shortRatio = daysTocover;

      // Short squeeze risk (simplified model)
      // Higher values indicate higher risk
      // Factors: high short interest, high days to cover
      final shortSqueezeRisk = (_shortInterest! / 20) * (daysTocover / 5);

      setState(() {
        _daysTocover = daysTocover;
        _shortRatio = shortRatio;
        _shortSqueezeRisk = shortSqueezeRisk > 10 ? 10 : shortSqueezeRisk; // Cap at 10
        _errorMessage = null;
      });

      _notifyCalculationComplete();
    } catch (e) {
      setState(() {
        _errorMessage = 'Error calculating short metrics: ${e.toString()}';
      });
    }
  }

  void _setShortType(ShortType type) {
    setState(() {
      _shortType = type;
    });
  }

  String _getShortTypeLabel(ShortType type) {
    return _shortTypeLabels[type] ?? 'Unknown';
  }

  String _getShortTypeDescription(ShortType type) {
    return _shortTypeDescriptions[type] ?? 'No description available.';
  }

  String _getShortSqueezeRiskLevel() {
    if (_shortSqueezeRisk == null) return 'Unknown';

    if (_shortSqueezeRisk! < 2) {
      return 'Low';
    } else if (_shortSqueezeRisk! < 5) {
      return 'Moderate';
    } else if (_shortSqueezeRisk! < 8) {
      return 'High';
    } else {
      return 'Very High';
    }
  }

  Color _getShortSqueezeRiskColor() {
    if (_shortSqueezeRisk == null) return widget.neutralColor;

    if (_shortSqueezeRisk! < 2) {
      return Colors.green;
    } else if (_shortSqueezeRisk! < 5) {
      return Colors.orange;
    } else if (_shortSqueezeRisk! < 8) {
      return Colors.deepOrange;
    } else {
      return Colors.red;
    }
  }

  void _notifyCalculationComplete() {
    if (widget.onCalculationComplete != null) {
      final results = <String, dynamic>{
        'stockPrice': _stockPrice,
        'shortPrice': _shortPrice,
        'shares': _shares,
        'shortInterest': _shortInterest,
        'avgDailyVolume': _avgDailyVolume,
        'profitLoss': _profitLoss,
        'profitLossPercentage': _profitLossPercentage,
        'daysTocover': _daysTocover,
        'shortRatio': _shortRatio,
        'shortSqueezeRisk': _shortSqueezeRisk,
        'maxLossRisk': _maxLossRisk,
        'shortType': _shortType,
      };

      widget.onCalculationComplete!(results);
    }
  }

  @override
  Widget build(BuildContext context) {
    final Color effectiveTextColor = widget.isDisabled
        ? Colors.grey
        : widget.textColor;

    return Container(
      width: widget.width,
      height: widget.height,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            if (widget.title != null) ...[
              Text(
                widget.title!,
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize + 2,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Short Type Selection
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Short Type:',
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                    fontWeight: widget.fontWeight,
                  ),
                ),
                const SizedBox(height: 8),
                SizedBox(
                  height: 40,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      for (final type in ShortType.values)
                        Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: ChoiceChip(
                            label: Text(
                              _getShortTypeLabel(type),
                              style: TextStyle(
                                color: _shortType == type ? Colors.white : effectiveTextColor,
                                fontSize: widget.fontSize - 2,
                              ),
                            ),
                            selected: _shortType == type,
                            onSelected: widget.isDisabled || widget.isReadOnly
                                ? null
                                : (selected) {
                                    if (selected) {
                                      _setShortType(type);
                                    }
                                  },
                            backgroundColor: widget.backgroundColor,
                            selectedColor: widget.neutralColor,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Short Type Description
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: widget.backgroundColor.withAlpha(179),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
              ),
              child: Text(
                _getShortTypeDescription(_shortType),
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize - 1,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Input Fields - First Row
            Row(
              children: [
                // Stock Price
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Price:',
                        style: TextStyle(
                          color: effectiveTextColor,
                          fontSize: widget.fontSize,
                          fontWeight: widget.fontWeight,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _stockPriceController,
                        style: TextStyle(
                          color: effectiveTextColor,
                          fontSize: widget.fontSize,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Enter current price',
                          hintStyle: TextStyle(
                            color: effectiveTextColor.withAlpha(128),
                            fontSize: widget.fontSize,
                          ),
                          prefixText: '\$',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        enabled: !widget.isDisabled && !widget.isReadOnly,
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        onChanged: (value) {
                          _parseStockPrice();
                        },
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // Short Price
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Short Price:',
                        style: TextStyle(
                          color: effectiveTextColor,
                          fontSize: widget.fontSize,
                          fontWeight: widget.fontWeight,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _shortPriceController,
                        style: TextStyle(
                          color: effectiveTextColor,
                          fontSize: widget.fontSize,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Enter short price',
                          hintStyle: TextStyle(
                            color: effectiveTextColor.withAlpha(128),
                            fontSize: widget.fontSize,
                          ),
                          prefixText: '\$',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        enabled: !widget.isDisabled && !widget.isReadOnly,
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        onChanged: (value) {
                          _parseShortPrice();
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Input Fields - Second Row
            Row(
              children: [
                // Shares
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Number of Shares:',
                        style: TextStyle(
                          color: effectiveTextColor,
                          fontSize: widget.fontSize,
                          fontWeight: widget.fontWeight,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _sharesController,
                        style: TextStyle(
                          color: effectiveTextColor,
                          fontSize: widget.fontSize,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Enter shares',
                          hintStyle: TextStyle(
                            color: effectiveTextColor.withAlpha(128),
                            fontSize: widget.fontSize,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        enabled: !widget.isDisabled && !widget.isReadOnly,
                        keyboardType: TextInputType.number,
                        onChanged: (value) {
                          _parseShares();
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Input Fields - Third Row
            Row(
              children: [
                // Short Interest
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Short Interest (%):',
                        style: TextStyle(
                          color: effectiveTextColor,
                          fontSize: widget.fontSize,
                          fontWeight: widget.fontWeight,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _shortInterestController,
                        style: TextStyle(
                          color: effectiveTextColor,
                          fontSize: widget.fontSize,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Enter short interest',
                          hintStyle: TextStyle(
                            color: effectiveTextColor.withAlpha(128),
                            fontSize: widget.fontSize,
                          ),
                          suffixText: '%',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        enabled: !widget.isDisabled && !widget.isReadOnly,
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        onChanged: (value) {
                          _parseShortInterest();
                        },
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // Average Daily Volume
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Avg. Daily Volume:',
                        style: TextStyle(
                          color: effectiveTextColor,
                          fontSize: widget.fontSize,
                          fontWeight: widget.fontWeight,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _avgDailyVolumeController,
                        style: TextStyle(
                          color: effectiveTextColor,
                          fontSize: widget.fontSize,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Enter volume',
                          hintStyle: TextStyle(
                            color: effectiveTextColor.withAlpha(128),
                            fontSize: widget.fontSize,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        enabled: !widget.isDisabled && !widget.isReadOnly,
                        keyboardType: TextInputType.number,
                        onChanged: (value) {
                          _parseAvgDailyVolume();
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Error Message
            if (_errorMessage != null || widget.errorText != null) ...[
              const SizedBox(height: 8),
              Text(
                _errorMessage ?? widget.errorText!,
                style: TextStyle(
                  color: widget.lossColor,
                  fontSize: widget.fontSize - 2,
                ),
              ),
            ],

            // Profit/Loss Calculation
            if (widget.showProfitLossCalculation && _profitLoss != null) ...[
              const SizedBox(height: 16),
              Text(
                'Profit/Loss Calculation:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: widget.backgroundColor.withAlpha(179),
                  borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  border: Border.all(
                    color: widget.borderColor.withAlpha(128),
                    width: widget.borderWidth / 2,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Short Price: \$${_shortPrice!.toStringAsFixed(2)}',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                    ),
                    Text(
                      'Current Price: \$${_stockPrice!.toStringAsFixed(2)}',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                    ),
                    Text(
                      'Number of Shares: ${_shares!}',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Profit/Loss: \$${_profitLoss!.toStringAsFixed(2)} (${_profitLossPercentage!.toStringAsFixed(2)}%)',
                      style: TextStyle(
                        color: _profitLoss! >= 0 ? widget.profitColor : widget.lossColor,
                        fontSize: widget.fontSize,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Short Interest Visualization
            if (widget.showShortInterestVisualization && _shortInterest != null) ...[
              const SizedBox(height: 16),
              Text(
                'Short Interest Visualization:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 60,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: widget.borderColor.withAlpha(128),
                    width: widget.borderWidth / 2,
                  ),
                  borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: _buildShortInterestVisualization(),
                ),
              ),
            ],

            // Short Metrics
            if (_daysTocover != null) ...[
              const SizedBox(height: 16),
              Text(
                'Short Metrics:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: widget.backgroundColor.withAlpha(179),
                  borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  border: Border.all(
                    color: widget.borderColor.withAlpha(128),
                    width: widget.borderWidth / 2,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Short Interest: ${_shortInterest!.toStringAsFixed(2)}%',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                    ),
                    Text(
                      'Days to Cover: ${_daysTocover!.toStringAsFixed(2)}',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                    ),
                    Text(
                      'Short Ratio: ${_shortRatio!.toStringAsFixed(2)}',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Short Squeeze Potential
            if (widget.showShortSqueezePotential && _shortSqueezeRisk != null) ...[
              const SizedBox(height: 16),
              Text(
                'Short Squeeze Potential:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: widget.backgroundColor.withAlpha(179),
                  borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  border: Border.all(
                    color: widget.borderColor.withAlpha(128),
                    width: widget.borderWidth / 2,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Risk Level: ${_getShortSqueezeRiskLevel()}',
                      style: TextStyle(
                        color: _getShortSqueezeRiskColor(),
                        fontSize: widget.fontSize,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: _shortSqueezeRisk! / 10,
                      backgroundColor: Colors.grey.withAlpha(50),
                      valueColor: AlwaysStoppedAnimation<Color>(_getShortSqueezeRiskColor()),
                      minHeight: 10,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Factors: High short interest (${_shortInterest!.toStringAsFixed(1)}%) and days to cover (${_daysTocover!.toStringAsFixed(1)})',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize - 1,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Risk Analysis
            if (widget.showRiskAnalysis && _maxLossRisk != null) ...[
              const SizedBox(height: 16),
              Text(
                'Risk Analysis:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: widget.backgroundColor.withAlpha(179),
                  borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  border: Border.all(
                    color: widget.borderColor.withAlpha(128),
                    width: widget.borderWidth / 2,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Maximum Theoretical Loss: \$${_maxLossRisk!.toStringAsFixed(2)}',
                      style: TextStyle(
                        color: widget.lossColor,
                        fontSize: widget.fontSize,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Note: Short selling has theoretically unlimited risk if the stock price rises significantly.',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize - 1,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Helper Text
            if (widget.helperText != null) ...[
              const SizedBox(height: 8),
              Text(
                widget.helperText!,
                style: TextStyle(
                  color: effectiveTextColor.withAlpha(179),
                  fontSize: widget.fontSize - 2,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildShortInterestVisualization() {
    if (_shortInterest == null) {
      return const Center(child: Text('No data to display'));
    }

    return CustomPaint(
      size: const Size(double.infinity, 60),
      painter: _ShortInterestPainter(
        shortInterest: _shortInterest!,
        textColor: widget.textColor,
        shortColor: widget.lossColor,
        neutralColor: widget.neutralColor,
      ),
    );
  }
}

class _ShortInterestPainter extends CustomPainter {
  final double shortInterest;
  final Color textColor;
  final Color shortColor;
  final Color neutralColor;

  _ShortInterestPainter({
    required this.shortInterest,
    required this.textColor,
    required this.shortColor,
    required this.neutralColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    // Draw background bar
    final backgroundRect = Rect.fromLTWH(0, 10, size.width, 20);
    canvas.drawRect(backgroundRect, paint..color = Colors.grey.withAlpha(50));

    // Draw short interest bar
    final shortWidth = (shortInterest / 100) * size.width;
    final shortRect = Rect.fromLTWH(0, 10, shortWidth, 20);

    // Gradient color based on short interest level
    Color shortBarColor;
    if (shortInterest < 10) {
      shortBarColor = neutralColor;
    } else if (shortInterest < 20) {
      shortBarColor = Colors.orange;
    } else {
      shortBarColor = shortColor;
    }

    canvas.drawRect(shortRect, paint..color = shortBarColor);

    // Draw percentage markers
    void drawMarker(double percentage, String label) {
      final x = (percentage / 100) * size.width;

      // Draw vertical line
      canvas.drawLine(
        Offset(x, 5),
        Offset(x, 35),
        Paint()
          ..color = textColor.withAlpha(128)
          ..strokeWidth = 1,
      );

      // Draw label
      textPainter.text = TextSpan(
        text: label,
        style: TextStyle(color: textColor, fontSize: 10),
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, 40));
    }

    drawMarker(0, '0%');
    drawMarker(25, '25%');
    drawMarker(50, '50%');
    drawMarker(75, '75%');
    drawMarker(100, '100%');

    // Draw current short interest marker
    final trianglePath = Path()
      ..moveTo(shortWidth, 5)
      ..lineTo(shortWidth - 5, 0)
      ..lineTo(shortWidth + 5, 0)
      ..close();

    canvas.drawPath(trianglePath, paint..color = shortBarColor);

    // Draw short interest value
    textPainter.text = TextSpan(
      text: '${shortInterest.toStringAsFixed(1)}%',
      style: TextStyle(color: shortBarColor, fontSize: 12, fontWeight: FontWeight.bold),
    );
    textPainter.layout();

    // Position the text above the triangle
    final textX = shortWidth - textPainter.width / 2;
    // Ensure text stays within canvas bounds
    final adjustedTextX = textX.clamp(0.0, size.width - textPainter.width);

    textPainter.paint(canvas, Offset(adjustedTextX, -15));
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
