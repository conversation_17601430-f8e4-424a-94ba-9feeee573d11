enum ChartSizeType { small, medium, large }
enum SelectedChartType {ring, disc, bubble}
enum ChartType { ring, disc, bubble }

class ChartSizeConfig {
  final ChartSizeType? size;
  final double? headingFontSize;
  final double? bodyFontSize;
  final double? labelFontSize;
  final double? chartRadius;
  final String? propertyType;
  final double? borderThikness;
  final double borderRadius;
  final double elevation;
  final ChartType chartType;

  const ChartSizeConfig({
    this.size,
    this.headingFontSize,
    this.bodyFontSize,
    this.labelFontSize,
    this.chartRadius,
    this.propertyType,
    this.borderThikness,
    this.borderRadius=6,
    this.elevation=0,
    this.chartType=ChartType.ring
  });

  ChartSizeConfig copyWith({
    ChartSizeType? size,
    double? headingFontSize,
    double? bodyFontSize,
    double? labelFontSize,
    double? chartRadius,
    String? propertyType,
    double? borderThikness,
    double? borderRadius,
    double? elevation,
    ChartType? chartType,
  }) {
    return ChartSizeConfig(
      size: size ?? this.size,
      headingFontSize: headingFontSize ?? this.headingFontSize,
      bodyFontSize: bodyFontSize ?? this.bodyFontSize,
      labelFontSize: labelFontSize ?? this.labelFontSize,
      chartRadius: chartRadius ?? this.chartRadius,
      propertyType: propertyType ?? this.propertyType,
      borderThikness: borderThikness ?? this.borderThikness,
      borderRadius: borderRadius??this.borderRadius,
      elevation: elevation??this.elevation,
      chartType: chartType??this.chartType );
  }
}
