import 'package:flutter/material.dart';

class GlobalSnackbar {
  static final GlobalKey<ScaffoldMessengerState> messengerKey = GlobalKey<ScaffoldMessengerState>();

  static void show(String message, {Color? backgroundColor, SnackBarAction? action}) {
    hide();
    messengerKey.currentState?.showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor,
        action: action,
      ),
    );
  }

  static void hide() {
    messengerKey.currentState?.hideCurrentSnackBar();
  }
} 