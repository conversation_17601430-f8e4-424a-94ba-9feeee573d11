import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';

/// A widget that calculates and displays multiple linear regression.
///
/// This widget allows users to input dependent variable (Y) and multiple independent variables (X),
/// calculates the regression coefficients, and visualizes the results.
class MultiLinearRegressionWidget extends StatefulWidget {
  /// Initial dependent variable (Y) values as a comma-separated string
  final String? initialYValues;

  /// Initial independent variables (X) values as a list of comma-separated strings
  final List<String>? initialXValuesList;

  /// Initial variable names for X columns
  final List<String>? initialVariableNames;

  /// Whether to show a chart visualization of the actual vs predicted values
  final bool showChart;

  /// Whether to show the result of the regression calculation
  final bool showResult;

  /// Whether to show detailed calculations
  final bool showDetailedCalculations;

  /// Whether to allow editing of the values
  final bool isReadOnly;

  /// Whether the widget is disabled
  final bool isDisabled;

  /// The title or label for the widget
  final String? title;

  /// Helper text to display below the inputs
  final String? helperText;

  /// Error text to display when there's an input error
  final String? errorText;

  /// The color of the text
  final Color textColor;

  /// The background color of the widget
  final Color backgroundColor;

  /// The color of the border
  final Color borderColor;

  /// The width of the border
  final double borderWidth;

  /// The radius of the border corners
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The font size for the text
  final double fontSize;

  /// The font weight for the text
  final FontWeight fontWeight;

  /// The color of the chart lines for actual values
  final Color actualLineColor;

  /// The color of the chart lines for predicted values
  final Color predictedLineColor;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// Callback when the regression coefficients change
  final Function(List<double>)? onCoefficientsChanged;

  /// Callback when the input values change
  final Function(List<double>, List<List<double>>)? onValuesChanged;

  /// Creates a multi linear regression widget.
  const MultiLinearRegressionWidget({
    super.key,
    this.initialYValues,
    this.initialXValuesList,
    this.initialVariableNames,
    this.showChart = true,
    this.showResult = true,
    this.showDetailedCalculations = false,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.title,
    this.helperText,
    this.errorText,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasBorder = true,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.actualLineColor = Colors.blue,
    this.predictedLineColor = Colors.green,
    this.width,
    this.height,
    this.onCoefficientsChanged,
    this.onValuesChanged,
  });

  @override
  State<MultiLinearRegressionWidget> createState() => _MultiLinearRegressionWidgetState();
}

class _MultiLinearRegressionWidgetState extends State<MultiLinearRegressionWidget> {
  final TextEditingController _yValuesController = TextEditingController();
  final List<TextEditingController> _xValuesControllers = [];
  final List<TextEditingController> _variableNameControllers = [];

  List<double> _yValues = [];
  List<List<double>> _xValuesList = [];
  List<String> _variableNames = [];
  List<double> _coefficients = [];
  double _intercept = 0.0;
  List<double> _predictedValues = [];
  double _rSquared = 0.0;

  String? _inputError;
  bool _hasCalculated = false;
  int _numVariables = 2; // Default to 2 independent variables

  @override
  void initState() {
    super.initState();

    // Initialize controllers for X variables
    _initializeControllers();

    // Initialize with provided values if available
    if (widget.initialYValues != null) {
      _yValuesController.text = widget.initialYValues!;
      _parseYValues();
    }

    if (widget.initialXValuesList != null) {
      final minLength = min(_xValuesControllers.length, widget.initialXValuesList!.length);
      for (int i = 0; i < minLength; i++) {
        _xValuesControllers[i].text = widget.initialXValuesList![i];
        _parseXValues(i);
      }
    }

    if (widget.initialVariableNames != null) {
      final minLength = min(_variableNameControllers.length, widget.initialVariableNames!.length);
      for (int i = 0; i < minLength; i++) {
        _variableNameControllers[i].text = widget.initialVariableNames![i];
      }
      _updateVariableNames();
    } else {
      // Set default variable names (X1, X2, etc.)
      for (int i = 0; i < _numVariables; i++) {
        _variableNameControllers[i].text = 'X${i + 1}';
      }
      _updateVariableNames();
    }

    // Calculate regression if all inputs are provided
    if (_yValues.isNotEmpty && _xValuesList.isNotEmpty &&
        _xValuesList.every((xList) => xList.isNotEmpty)) {
      _calculateRegression();
    }
  }

  @override
  void dispose() {
    _yValuesController.dispose();
    for (var controller in _xValuesControllers) {
      controller.dispose();
    }
    for (var controller in _variableNameControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _initializeControllers() {
    _xValuesControllers.clear();
    _variableNameControllers.clear();

    for (int i = 0; i < _numVariables; i++) {
      _xValuesControllers.add(TextEditingController());
      _variableNameControllers.add(TextEditingController(text: 'X${i + 1}'));
    }

    _xValuesList = List.generate(_numVariables, (_) => []);
    _updateVariableNames();
  }

  void _updateVariableNames() {
    _variableNames = _variableNameControllers.map((controller) => controller.text).toList();
  }

  void _parseYValues() {
    try {
      _yValues = _yValuesController.text
          .split(',')
          .map((s) => double.parse(s.trim()))
          .toList();
      setState(() {
        _inputError = null;
      });
    } catch (e) {
      setState(() {
        _inputError = 'Invalid Y values format. Use comma-separated numbers.';
        _yValues = [];
        _hasCalculated = false;
      });
    }
  }

  void _parseXValues(int index) {
    try {
      if (index >= _xValuesControllers.length) return;

      final values = _xValuesControllers[index].text
          .split(',')
          .map((s) => double.parse(s.trim()))
          .toList();

      setState(() {
        if (index >= _xValuesList.length) {
          _xValuesList.add(values);
        } else {
          _xValuesList[index] = values;
        }
        _inputError = null;
      });
    } catch (e) {
      setState(() {
        _inputError = 'Invalid X values format for ${_variableNames[index]}. Use comma-separated numbers.';
        if (index < _xValuesList.length) {
          _xValuesList[index] = [];
        }
        _hasCalculated = false;
      });
    }
  }

  void _addVariable() {
    setState(() {
      _numVariables++;
      _xValuesControllers.add(TextEditingController());
      _variableNameControllers.add(TextEditingController(text: 'X$_numVariables'));
      _xValuesList.add([]);
      _variableNames.add('X$_numVariables');
    });
  }

  void _removeVariable() {
    if (_numVariables <= 1) return;

    setState(() {
      _numVariables--;
      final lastXController = _xValuesControllers.removeLast();
      final lastNameController = _variableNameControllers.removeLast();
      lastXController.dispose();
      lastNameController.dispose();
      _xValuesList.removeLast();
      _variableNames.removeLast();
    });
  }

  bool _validateData() {
    // Check if we have Y values
    if (_yValues.isEmpty) {
      setState(() {
        _inputError = 'Please enter Y values.';
      });
      return false;
    }

    // Check if we have X values for all variables
    for (int i = 0; i < _xValuesList.length; i++) {
      if (_xValuesList[i].isEmpty) {
        setState(() {
          _inputError = 'Please enter values for ${_variableNames[i]}.';
        });
        return false;
      }
    }

    // Check if all lists have the same length
    final yLength = _yValues.length;
    for (int i = 0; i < _xValuesList.length; i++) {
      if (_xValuesList[i].length != yLength) {
        setState(() {
          _inputError = '${_variableNames[i]} must have the same number of values as Y.';
        });
        return false;
      }
    }

    setState(() {
      _inputError = null;
    });
    return true;
  }

  void _calculateRegression() {
    if (!_validateData()) {
      setState(() {
        _hasCalculated = false;
      });
      return;
    }

    // Prepare data for calculation
    final int n = _yValues.length;
    final int p = _xValuesList.length;

    // Create X matrix with a column of 1s for the intercept
    List<List<double>> X = List.generate(n, (_) => List.filled(p + 1, 0.0));
    for (int i = 0; i < n; i++) {
      X[i][0] = 1.0; // Intercept term
      for (int j = 0; j < p; j++) {
        X[i][j + 1] = _xValuesList[j][i];
      }
    }

    // Calculate X^T * X
    List<List<double>> XtX = List.generate(p + 1, (_) => List.filled(p + 1, 0.0));
    for (int i = 0; i < p + 1; i++) {
      for (int j = 0; j < p + 1; j++) {
        double sum = 0.0;
        for (int k = 0; k < n; k++) {
          sum += X[k][i] * X[k][j];
        }
        XtX[i][j] = sum;
      }
    }

    // Calculate X^T * Y
    List<double> XtY = List.filled(p + 1, 0.0);
    for (int i = 0; i < p + 1; i++) {
      double sum = 0.0;
      for (int j = 0; j < n; j++) {
        sum += X[j][i] * _yValues[j];
      }
      XtY[i] = sum;
    }

    // Solve the system (X^T * X) * beta = X^T * Y using Gaussian elimination
    List<double> beta = _solveLinearSystem(XtX, XtY);

    // Extract intercept and coefficients
    _intercept = beta[0];
    _coefficients = beta.sublist(1);

    // Calculate predicted values
    _predictedValues = List.filled(n, 0.0);
    for (int i = 0; i < n; i++) {
      double predicted = _intercept;
      for (int j = 0; j < p; j++) {
        predicted += _coefficients[j] * _xValuesList[j][i];
      }
      _predictedValues[i] = predicted;
    }

    // Calculate R-squared
    double yMean = _yValues.reduce((a, b) => a + b) / n;
    double totalSS = 0.0;
    double residualSS = 0.0;

    for (int i = 0; i < n; i++) {
      totalSS += pow(_yValues[i] - yMean, 2);
      residualSS += pow(_yValues[i] - _predictedValues[i], 2);
    }

    _rSquared = 1.0 - (residualSS / totalSS);

    setState(() {
      _hasCalculated = true;
    });

    // Notify listeners
    if (widget.onCoefficientsChanged != null) {
      List<double> allCoefficients = [_intercept, ..._coefficients];
      widget.onCoefficientsChanged!(allCoefficients);
    }

    if (widget.onValuesChanged != null) {
      widget.onValuesChanged!(_yValues, _xValuesList);
    }
  }

  // Solve a linear system using Gaussian elimination
  List<double> _solveLinearSystem(List<List<double>> A, List<double> b) {
    int n = A.length;

    // Create augmented matrix [A|b]
    List<List<double>> augmented = List.generate(n, (i) => [...A[i], b[i]]);

    // Gaussian elimination
    for (int i = 0; i < n; i++) {
      // Find pivot
      int maxRow = i;
      double maxVal = augmented[i][i].abs();

      for (int j = i + 1; j < n; j++) {
        if (augmented[j][i].abs() > maxVal) {
          maxRow = j;
          maxVal = augmented[j][i].abs();
        }
      }

      // Swap rows if needed
      if (maxRow != i) {
        final temp = augmented[i];
        augmented[i] = augmented[maxRow];
        augmented[maxRow] = temp;
      }

      // Eliminate below
      for (int j = i + 1; j < n; j++) {
        double factor = augmented[j][i] / augmented[i][i];
        for (int k = i; k <= n; k++) {
          augmented[j][k] -= factor * augmented[i][k];
        }
      }
    }

    // Back substitution
    List<double> x = List.filled(n, 0.0);
    for (int i = n - 1; i >= 0; i--) {
      x[i] = augmented[i][n];
      for (int j = i + 1; j < n; j++) {
        x[i] -= augmented[i][j] * x[j];
      }
      x[i] /= augmented[i][i];
    }

    return x;
  }

  @override
  Widget build(BuildContext context) {
    final Color effectiveTextColor = widget.isDisabled
        ? Colors.grey
        : widget.textColor;

    return Container(
      width: widget.width,
      height: widget.height,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            if (widget.title != null) ...[
              Text(
                widget.title!,
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize + 2,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Y Values Input
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Dependent Variable (Y)',
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                    fontWeight: widget.fontWeight,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: _yValuesController,
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Enter comma-separated Y values (e.g., 10,20,30)',
                    hintStyle: TextStyle(
                      color: effectiveTextColor.withAlpha(128),
                      fontSize: widget.fontSize,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  enabled: !widget.isDisabled && !widget.isReadOnly,
                  onChanged: (value) {
                    _parseYValues();
                  },
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9,.\-]')),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),

            // X Variables Inputs
            Text(
              'Independent Variables (X)',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),

            // Variable controls
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (!widget.isDisabled && !widget.isReadOnly) ...[
                  ElevatedButton.icon(
                    onPressed: _addVariable,
                    icon: const Icon(Icons.add),
                    label: const Text('Add Variable'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: widget.actualLineColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: _numVariables > 1 ? _removeVariable : null,
                    icon: const Icon(Icons.remove),
                    label: const Text('Remove Variable'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                      ),
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 8),

            // X Variables List
            for (int i = 0; i < _numVariables; i++) ...[
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Variable name
                  SizedBox(
                    width: 80,
                    child: TextField(
                      controller: _variableNameControllers[i],
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Name',
                        hintStyle: TextStyle(
                          color: effectiveTextColor.withAlpha(128),
                          fontSize: widget.fontSize,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      enabled: !widget.isDisabled && !widget.isReadOnly,
                      onChanged: (value) {
                        _updateVariableNames();
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Variable values
                  Expanded(
                    child: TextField(
                      controller: _xValuesControllers[i],
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Enter comma-separated values (e.g., 5,15,25)',
                        hintStyle: TextStyle(
                          color: effectiveTextColor.withAlpha(128),
                          fontSize: widget.fontSize,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      enabled: !widget.isDisabled && !widget.isReadOnly,
                      onChanged: (value) {
                        _parseXValues(i);
                      },
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[0-9,.\-]')),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],

            // Calculate Button
            if (!widget.isDisabled && !widget.isReadOnly) ...[
              const SizedBox(height: 8),
              Center(
                child: ElevatedButton(
                  onPressed: _calculateRegression,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.actualLineColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                    ),
                  ),
                  child: const Text('Calculate Regression'),
                ),
              ),
            ],

            // Error Message
            if (_inputError != null || widget.errorText != null) ...[
              const SizedBox(height: 8),
              Text(
                _inputError ?? widget.errorText!,
                style: TextStyle(
                  color: Colors.red,
                  fontSize: widget.fontSize - 2,
                ),
              ),
            ],

            // Result Display
            if (widget.showResult && _hasCalculated) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: widget.backgroundColor.withAlpha(179),
                  borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  border: Border.all(
                    color: widget.borderColor.withAlpha(128),
                    width: widget.borderWidth / 2,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Regression Results:',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Equation: Y = ${_intercept.toStringAsFixed(4)} + '
                      '${_coefficients.asMap().entries.map((entry) {
                        int i = entry.key;
                        double coef = entry.value;
                        return '${coef.toStringAsFixed(4)} × ${_variableNames[i]}';
                      }).join(' + ')}',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'R² = ${_rSquared.toStringAsFixed(4)}',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Detailed Calculations
            if (widget.showDetailedCalculations && _hasCalculated) ...[
              const SizedBox(height: 16),
              Text(
                'Detailed Results:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 150,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: widget.borderColor.withAlpha(128),
                    width: widget.borderWidth / 2,
                  ),
                  borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                ),
                child: ListView.builder(
                  itemCount: _yValues.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 4,
                      ),
                      child: Row(
                        children: [
                          Text(
                            'Observation ${index + 1}:',
                            style: TextStyle(
                              color: effectiveTextColor,
                              fontSize: widget.fontSize - 2,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Y = ${_yValues[index].toStringAsFixed(2)}',
                            style: TextStyle(
                              color: widget.actualLineColor,
                              fontSize: widget.fontSize - 2,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Predicted = ${_predictedValues[index].toStringAsFixed(2)}',
                            style: TextStyle(
                              color: widget.predictedLineColor,
                              fontSize: widget.fontSize - 2,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Error = ${(_yValues[index] - _predictedValues[index]).toStringAsFixed(2)}',
                            style: TextStyle(
                              color: Colors.red,
                              fontSize: widget.fontSize - 2,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],

            // Chart
            if (widget.showChart && _hasCalculated && _yValues.isNotEmpty && _predictedValues.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'Actual vs Predicted:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 200,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: widget.borderColor.withAlpha(128),
                    width: widget.borderWidth / 2,
                  ),
                  borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                ),
                child: _buildChart(),
              ),
            ],

            // Helper Text
            if (widget.helperText != null) ...[
              const SizedBox(height: 8),
              Text(
                widget.helperText!,
                style: TextStyle(
                  color: effectiveTextColor.withAlpha(179),
                  fontSize: widget.fontSize - 2,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildChart() {
    // Ensure we have data to display
    if (_yValues.isEmpty || _predictedValues.isEmpty) {
      return const Center(
        child: Text('No data to display'),
      );
    }

    // Find min and max values for Y axis
    double minY = double.infinity;
    double maxY = double.negativeInfinity;

    for (final value in _yValues) {
      minY = min(minY, value);
      maxY = max(maxY, value);
    }

    for (final value in _predictedValues) {
      minY = min(minY, value);
      maxY = max(maxY, value);
    }

    // Add some padding to the min and max values
    final double yPadding = (maxY - minY) * 0.1;
    minY = minY - yPadding;
    maxY = maxY + yPadding;

    return CustomPaint(
      size: Size.infinite,
      painter: RegressionChartPainter(
        actualValues: _yValues,
        predictedValues: _predictedValues,
        minY: minY,
        maxY: maxY,
        actualColor: widget.actualLineColor,
        predictedColor: widget.predictedLineColor,
        textColor: widget.textColor,
        borderColor: widget.borderColor,
        fontSize: widget.fontSize,
      ),
    );
  }
}

/// Custom painter for Regression chart
class RegressionChartPainter extends CustomPainter {
  final List<double> actualValues;
  final List<double> predictedValues;
  final double minY;
  final double maxY;
  final Color actualColor;
  final Color predictedColor;
  final Color textColor;
  final Color borderColor;
  final double fontSize;

  RegressionChartPainter({
    required this.actualValues,
    required this.predictedValues,
    required this.minY,
    required this.maxY,
    required this.actualColor,
    required this.predictedColor,
    required this.textColor,
    required this.borderColor,
    required this.fontSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final int dataLength = actualValues.length;
    if (dataLength == 0) return;

    // Setup
    final double width = size.width;
    final double height = size.height;
    final double chartPadding = 40.0;
    final double chartWidth = width - (chartPadding * 2);
    final double chartHeight = height - (chartPadding * 2);
    final double xStep = chartWidth / (dataLength - 1 > 0 ? dataLength - 1 : 1);

    // Draw border
    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    canvas.drawRect(
      Rect.fromLTWH(chartPadding, chartPadding, chartWidth, chartHeight),
      borderPaint,
    );

    // Draw grid lines
    final gridPaint = Paint()
      ..color = borderColor.withAlpha(51)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Horizontal grid lines
    final int horizontalLines = 5;
    for (int i = 0; i <= horizontalLines; i++) {
      final double y = chartPadding + (chartHeight / horizontalLines * i);
      canvas.drawLine(
        Offset(chartPadding, y),
        Offset(chartPadding + chartWidth, y),
        gridPaint,
      );

      // Draw Y-axis labels
      final double value = maxY - ((maxY - minY) / horizontalLines * i);
      final textSpan = TextSpan(
        text: value.toStringAsFixed(1),
        style: TextStyle(
          color: textColor,
          fontSize: fontSize - 4,
        ),
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(chartPadding - textPainter.width - 5, y - textPainter.height / 2),
      );
    }

    // Vertical grid lines
    final int verticalLines = min(dataLength, 10);
    for (int i = 0; i <= verticalLines; i++) {
      final double x = chartPadding + (chartWidth / verticalLines * i);
      canvas.drawLine(
        Offset(x, chartPadding),
        Offset(x, chartPadding + chartHeight),
        gridPaint,
      );

      // Draw X-axis labels
      if (i < dataLength) {
        final int index = (i * (dataLength - 1) / verticalLines).round();
        final textSpan = TextSpan(
          text: index.toString(),
          style: TextStyle(
            color: textColor,
            fontSize: fontSize - 4,
          ),
        );
        final textPainter = TextPainter(
          text: textSpan,
          textDirection: TextDirection.ltr,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(x - textPainter.width / 2, chartPadding + chartHeight + 5),
        );
      }
    }

    // Draw actual values as points
    for (int i = 0; i < dataLength; i++) {
      final double x = chartPadding + (i * xStep);
      final double normalizedY = (actualValues[i] - minY) / (maxY - minY);
      final double y = chartPadding + chartHeight - (normalizedY * chartHeight);

      // Draw point
      canvas.drawCircle(
        Offset(x, y),
        4.0,
        Paint()..color = actualColor,
      );
    }

    // Draw predicted values line
    final predictedPaint = Paint()
      ..color = predictedColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final predictedPath = Path();
    for (int i = 0; i < dataLength; i++) {
      final double x = chartPadding + (i * xStep);
      final double normalizedY = (predictedValues[i] - minY) / (maxY - minY);
      final double y = chartPadding + chartHeight - (normalizedY * chartHeight);

      if (i == 0) {
        predictedPath.moveTo(x, y);
      } else {
        predictedPath.lineTo(x, y);
      }
    }
    canvas.drawPath(predictedPath, predictedPaint);

    // Draw legend
    final legendY = chartPadding / 2;

    // Actual values legend
    canvas.drawCircle(
      Offset(chartPadding, legendY),
      4.0,
      Paint()..color = actualColor,
    );

    final actualTextSpan = TextSpan(
      text: 'Actual Values',
      style: TextStyle(
        color: textColor,
        fontSize: fontSize - 2,
      ),
    );
    final actualTextPainter = TextPainter(
      text: actualTextSpan,
      textDirection: TextDirection.ltr,
    );
    actualTextPainter.layout();
    actualTextPainter.paint(
      canvas,
      Offset(chartPadding + 10, legendY - actualTextPainter.height / 2),
    );

    // Predicted values legend
    final predictedLegendX = chartPadding + 10 + actualTextPainter.width + 20;
    canvas.drawLine(
      Offset(predictedLegendX - 10, legendY),
      Offset(predictedLegendX + 10, legendY),
      predictedPaint,
    );

    final predictedTextSpan = TextSpan(
      text: 'Predicted Values',
      style: TextStyle(
        color: textColor,
        fontSize: fontSize - 2,
      ),
    );
    final predictedTextPainter = TextPainter(
      text: predictedTextSpan,
      textDirection: TextDirection.ltr,
    );
    predictedTextPainter.layout();
    predictedTextPainter.paint(
      canvas,
      Offset(predictedLegendX + 15, legendY - predictedTextPainter.height / 2),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}