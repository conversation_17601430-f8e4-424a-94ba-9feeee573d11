import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';

/// A widget that calculates and displays the Mean Squared Error (MSE) between actual and predicted values.
///
/// This widget allows users to input actual and predicted values, calculates the MSE,
/// and optionally displays a visual representation of the error.
class MeanSquaredErrorWidget extends StatefulWidget {
  /// Initial actual values as a comma-separated string
  final String? initialActualValues;

  /// Initial predicted values as a comma-separated string
  final String? initialPredictedValues;

  /// Whether to show a chart visualization of the values and errors
  final bool showChart;

  /// Whether to show the result of the MSE calculation
  final bool showResult;

  /// Whether to show detailed error calculations for each pair of values
  final bool showDetailedErrors;

  /// Whether to allow editing of the values
  final bool isReadOnly;

  /// Whether the widget is disabled
  final bool isDisabled;

  /// The title or label for the widget
  final String? title;

  /// Helper text to display below the inputs
  final String? helperText;

  /// Error text to display when there's an input error
  final String? errorText;

  /// The color of the text
  final Color textColor;

  /// The background color of the widget
  final Color backgroundColor;

  /// The color of the border
  final Color borderColor;

  /// The width of the border
  final double borderWidth;

  /// The radius of the border corners
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The font size for the text
  final double fontSize;

  /// The font weight for the text
  final FontWeight fontWeight;

  /// The color of the chart lines for actual values
  final Color actualLineColor;

  /// The color of the chart lines for predicted values
  final Color predictedLineColor;

  /// The color used to highlight errors in the chart
  final Color errorColor;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// Callback when the MSE value changes
  final Function(double)? onMseChanged;

  /// Callback when the input values change
  final Function(List<double>, List<double>)? onValuesChanged;

  /// Creates a mean squared error widget.
  const MeanSquaredErrorWidget({
    super.key,
    this.initialActualValues,
    this.initialPredictedValues,
    this.showChart = true,
    this.showResult = true,
    this.showDetailedErrors = false,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.title,
    this.helperText,
    this.errorText,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasBorder = true,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.actualLineColor = Colors.blue,
    this.predictedLineColor = Colors.green,
    this.errorColor = Colors.red,
    this.width,
    this.height,
    this.onMseChanged,
    this.onValuesChanged,
  });

  @override
  _MeanSquaredErrorWidgetState createState() => _MeanSquaredErrorWidgetState();
}

class _MeanSquaredErrorWidgetState extends State<MeanSquaredErrorWidget> {
  final TextEditingController _actualController = TextEditingController();
  final TextEditingController _predictedController = TextEditingController();

  List<double> _actualValues = [];
  List<double> _predictedValues = [];
  double _mse = 0.0;
  List<double> _squaredErrors = [];
  String? _inputError;
  bool _hasCalculated = false;

  @override
  void initState() {
    super.initState();

    // Initialize with provided values if available
    if (widget.initialActualValues != null) {
      _actualController.text = widget.initialActualValues!;
      _parseActualValues();
    }

    if (widget.initialPredictedValues != null) {
      _predictedController.text = widget.initialPredictedValues!;
      _parsePredictedValues();
    }

    // Calculate MSE if both inputs are provided
    if (_actualValues.isNotEmpty && _predictedValues.isNotEmpty) {
      _calculateMSE();
    }
  }

  @override
  void dispose() {
    _actualController.dispose();
    _predictedController.dispose();
    super.dispose();
  }

  void _parseActualValues() {
    try {
      _actualValues = _actualController.text
          .split(',')
          .map((s) => double.parse(s.trim()))
          .toList();
      setState(() {
        _inputError = null;
      });
    } catch (e) {
      setState(() {
        _inputError = 'Invalid actual values format. Use comma-separated numbers.';
        _actualValues = [];
      });
    }
  }

  void _parsePredictedValues() {
    try {
      _predictedValues = _predictedController.text
          .split(',')
          .map((s) => double.parse(s.trim()))
          .toList();
      setState(() {
        _inputError = null;
      });
    } catch (e) {
      setState(() {
        _inputError = 'Invalid predicted values format. Use comma-separated numbers.';
        _predictedValues = [];
      });
    }
  }

  void _calculateMSE() {
    if (_actualValues.isEmpty || _predictedValues.isEmpty) {
      setState(() {
        _mse = 0.0;
        _squaredErrors = [];
        _hasCalculated = false;
      });
      return;
    }

    // Ensure both lists have the same length by using the shorter length
    final int minLength = min(_actualValues.length, _predictedValues.length);

    if (minLength == 0) {
      setState(() {
        _mse = 0.0;
        _squaredErrors = [];
        _hasCalculated = false;
      });
      return;
    }

    // Calculate squared errors for each pair
    _squaredErrors = List.generate(minLength, (i) {
      final double diff = _actualValues[i] - _predictedValues[i];
      return diff * diff;
    });

    // Calculate MSE
    final double sum = _squaredErrors.reduce((a, b) => a + b);
    setState(() {
      _mse = sum / minLength;
      _hasCalculated = true;
    });

    // Notify listeners
    if (widget.onMseChanged != null) {
      widget.onMseChanged!(_mse);
    }

    if (widget.onValuesChanged != null) {
      widget.onValuesChanged!(
        _actualValues.sublist(0, minLength),
        _predictedValues.sublist(0, minLength)
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final Color effectiveTextColor = widget.isDisabled
        ? Colors.grey
        : widget.textColor;

    return Container(
      width: widget.width,
      height: widget.height,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title
          if (widget.title != null) ...[
            Text(
              widget.title!,
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize + 2,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Actual Values Input
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Actual Values',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _actualController,
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                ),
                decoration: InputDecoration(
                  hintText: 'Enter comma-separated values (e.g., 10,20,30)',
                  hintStyle: TextStyle(
                    color: effectiveTextColor.withAlpha(128),
                    fontSize: widget.fontSize,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                enabled: !widget.isDisabled && !widget.isReadOnly,
                onChanged: (value) {
                  _parseActualValues();
                  if (_actualValues.isNotEmpty && _predictedValues.isNotEmpty) {
                    _calculateMSE();
                  }
                },
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9,.\-]')),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Predicted Values Input
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Predicted Values',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _predictedController,
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                ),
                decoration: InputDecoration(
                  hintText: 'Enter comma-separated values (e.g., 12,18,32)',
                  hintStyle: TextStyle(
                    color: effectiveTextColor.withAlpha(128),
                    fontSize: widget.fontSize,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                enabled: !widget.isDisabled && !widget.isReadOnly,
                onChanged: (value) {
                  _parsePredictedValues();
                  if (_actualValues.isNotEmpty && _predictedValues.isNotEmpty) {
                    _calculateMSE();
                  }
                },
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9,.\-]')),
                ],
              ),
            ],
          ),

          // Calculate Button
          if (!widget.isDisabled && !widget.isReadOnly) ...[
            const SizedBox(height: 16),
            Center(
              child: ElevatedButton(
                onPressed: () {
                  _parseActualValues();
                  _parsePredictedValues();
                  _calculateMSE();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.actualLineColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  ),
                ),
                child: const Text('Calculate MSE'),
              ),
            ),
          ],

          // Error Message
          if (_inputError != null || widget.errorText != null) ...[
            const SizedBox(height: 8),
            Text(
              _inputError ?? widget.errorText!,
              style: TextStyle(
                color: widget.errorColor,
                fontSize: widget.fontSize - 2,
              ),
            ),
          ],

          // Result Display
          if (widget.showResult && _hasCalculated) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.backgroundColor.withAlpha(179),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Mean Squared Error (MSE):',
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _mse.toStringAsFixed(6),
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize + 4,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Detailed Errors
          if (widget.showDetailedErrors && _hasCalculated && _squaredErrors.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Detailed Errors:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 120,
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
              ),
              child: ListView.builder(
                itemCount: _squaredErrors.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    child: Row(
                      children: [
                        Text(
                          'Item ${index + 1}:',
                          style: TextStyle(
                            color: effectiveTextColor,
                            fontSize: widget.fontSize - 2,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Actual: ${_actualValues[index].toStringAsFixed(2)}',
                          style: TextStyle(
                            color: widget.actualLineColor,
                            fontSize: widget.fontSize - 2,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Predicted: ${_predictedValues[index].toStringAsFixed(2)}',
                          style: TextStyle(
                            color: widget.predictedLineColor,
                            fontSize: widget.fontSize - 2,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Error²: ${_squaredErrors[index].toStringAsFixed(2)}',
                          style: TextStyle(
                            color: widget.errorColor,
                            fontSize: widget.fontSize - 2,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],

          // Chart
          if (widget.showChart && _hasCalculated && _actualValues.isNotEmpty && _predictedValues.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Visualization:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 200,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
              ),
              child: _buildChart(),
            ),
          ],

          // Helper Text
          if (widget.helperText != null) ...[
            const SizedBox(height: 8),
            Text(
              widget.helperText!,
              style: TextStyle(
                color: effectiveTextColor.withAlpha(179),
                fontSize: widget.fontSize - 2,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildChart() {
    // Ensure we have data to display
    if (_actualValues.isEmpty || _predictedValues.isEmpty) {
      return const Center(
        child: Text('No data to display'),
      );
    }

    // Get the minimum length of both arrays
    final int minLength = min(_actualValues.length, _predictedValues.length);

    // Find min and max values for Y axis
    double minY = double.infinity;
    double maxY = double.negativeInfinity;

    for (int i = 0; i < minLength; i++) {
      minY = min(minY, min(_actualValues[i], _predictedValues[i]));
      maxY = max(maxY, max(_actualValues[i], _predictedValues[i]));
    }

    // Add some padding to the min and max values
    final double yPadding = (maxY - minY) * 0.1;
    minY = minY - yPadding;
    maxY = maxY + yPadding;

    return CustomPaint(
      size: Size.infinite,
      painter: MseChartPainter(
        actualValues: _actualValues.sublist(0, minLength),
        predictedValues: _predictedValues.sublist(0, minLength),
        squaredErrors: _squaredErrors,
        minY: minY,
        maxY: maxY,
        actualColor: widget.actualLineColor,
        predictedColor: widget.predictedLineColor,
        errorColor: widget.errorColor,
        textColor: widget.textColor,
        borderColor: widget.borderColor,
        fontSize: widget.fontSize,
      ),
    );
  }
}

/// Custom painter for MSE chart
class MseChartPainter extends CustomPainter {
  final List<double> actualValues;
  final List<double> predictedValues;
  final List<double> squaredErrors;
  final double minY;
  final double maxY;
  final Color actualColor;
  final Color predictedColor;
  final Color errorColor;
  final Color textColor;
  final Color borderColor;
  final double fontSize;

  MseChartPainter({
    required this.actualValues,
    required this.predictedValues,
    required this.squaredErrors,
    required this.minY,
    required this.maxY,
    required this.actualColor,
    required this.predictedColor,
    required this.errorColor,
    required this.textColor,
    required this.borderColor,
    required this.fontSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final int dataLength = actualValues.length;
    if (dataLength == 0) return;

    // Setup
    final double width = size.width;
    final double height = size.height;
    final double chartPadding = 40.0;
    final double chartWidth = width - (chartPadding * 2);
    final double chartHeight = height - (chartPadding * 2);
    final double xStep = chartWidth / (dataLength - 1 > 0 ? dataLength - 1 : 1);

    // Draw border
    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    canvas.drawRect(
      Rect.fromLTWH(chartPadding, chartPadding, chartWidth, chartHeight),
      borderPaint,
    );

    // Draw grid lines
    final gridPaint = Paint()
      ..color = borderColor.withAlpha(51)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Horizontal grid lines
    final int horizontalLines = 5;
    for (int i = 0; i <= horizontalLines; i++) {
      final double y = chartPadding + (chartHeight / horizontalLines * i);
      canvas.drawLine(
        Offset(chartPadding, y),
        Offset(chartPadding + chartWidth, y),
        gridPaint,
      );

      // Draw Y-axis labels
      final double value = maxY - ((maxY - minY) / horizontalLines * i);
      final textSpan = TextSpan(
        text: value.toStringAsFixed(1),
        style: TextStyle(
          color: textColor,
          fontSize: fontSize - 4,
        ),
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(chartPadding - textPainter.width - 5, y - textPainter.height / 2),
      );
    }

    // Vertical grid lines
    final int verticalLines = min(dataLength, 10);
    for (int i = 0; i <= verticalLines; i++) {
      final double x = chartPadding + (chartWidth / verticalLines * i);
      canvas.drawLine(
        Offset(x, chartPadding),
        Offset(x, chartPadding + chartHeight),
        gridPaint,
      );

      // Draw X-axis labels
      if (i < dataLength) {
        final int index = (i * (dataLength - 1) / verticalLines).round();
        final textSpan = TextSpan(
          text: index.toString(),
          style: TextStyle(
            color: textColor,
            fontSize: fontSize - 4,
          ),
        );
        final textPainter = TextPainter(
          text: textSpan,
          textDirection: TextDirection.ltr,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(x - textPainter.width / 2, chartPadding + chartHeight + 5),
        );
      }
    }

    // Draw actual values line
    final actualPaint = Paint()
      ..color = actualColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final actualPath = Path();
    for (int i = 0; i < dataLength; i++) {
      final double x = chartPadding + (i * xStep);
      final double normalizedY = (actualValues[i] - minY) / (maxY - minY);
      final double y = chartPadding + chartHeight - (normalizedY * chartHeight);

      if (i == 0) {
        actualPath.moveTo(x, y);
      } else {
        actualPath.lineTo(x, y);
      }

      // Draw point
      canvas.drawCircle(
        Offset(x, y),
        4.0,
        Paint()..color = actualColor,
      );
    }
    canvas.drawPath(actualPath, actualPaint);

    // Draw predicted values line
    final predictedPaint = Paint()
      ..color = predictedColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final predictedPath = Path();
    for (int i = 0; i < dataLength; i++) {
      final double x = chartPadding + (i * xStep);
      final double normalizedY = (predictedValues[i] - minY) / (maxY - minY);
      final double y = chartPadding + chartHeight - (normalizedY * chartHeight);

      if (i == 0) {
        predictedPath.moveTo(x, y);
      } else {
        predictedPath.lineTo(x, y);
      }

      // Draw point
      canvas.drawCircle(
        Offset(x, y),
        4.0,
        Paint()..color = predictedColor,
      );
    }
    canvas.drawPath(predictedPath, predictedPaint);

    // Draw error lines
    final errorPaint = Paint()
      ..color = errorColor.withAlpha(128)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    for (int i = 0; i < dataLength; i++) {
      final double x = chartPadding + (i * xStep);
      final double normalizedActualY = (actualValues[i] - minY) / (maxY - minY);
      final double actualY = chartPadding + chartHeight - (normalizedActualY * chartHeight);

      final double normalizedPredictedY = (predictedValues[i] - minY) / (maxY - minY);
      final double predictedY = chartPadding + chartHeight - (normalizedPredictedY * chartHeight);

      canvas.drawLine(
        Offset(x, actualY),
        Offset(x, predictedY),
        errorPaint,
      );
    }

    // Draw legend
    final legendY = chartPadding / 2;

    // Actual values legend
    canvas.drawCircle(
      Offset(chartPadding, legendY),
      4.0,
      Paint()..color = actualColor,
    );

    final actualTextSpan = TextSpan(
      text: 'Actual',
      style: TextStyle(
        color: textColor,
        fontSize: fontSize - 2,
      ),
    );
    final actualTextPainter = TextPainter(
      text: actualTextSpan,
      textDirection: TextDirection.ltr,
    );
    actualTextPainter.layout();
    actualTextPainter.paint(
      canvas,
      Offset(chartPadding + 10, legendY - actualTextPainter.height / 2),
    );

    // Predicted values legend
    final predictedLegendX = chartPadding + 10 + actualTextPainter.width + 20;
    canvas.drawCircle(
      Offset(predictedLegendX, legendY),
      4.0,
      Paint()..color = predictedColor,
    );

    final predictedTextSpan = TextSpan(
      text: 'Predicted',
      style: TextStyle(
        color: textColor,
        fontSize: fontSize - 2,
      ),
    );
    final predictedTextPainter = TextPainter(
      text: predictedTextSpan,
      textDirection: TextDirection.ltr,
    );
    predictedTextPainter.layout();
    predictedTextPainter.paint(
      canvas,
      Offset(predictedLegendX + 10, legendY - predictedTextPainter.height / 2),
    );

    // Error legend
    final errorLegendX = predictedLegendX + 10 + predictedTextPainter.width + 20;
    canvas.drawLine(
      Offset(errorLegendX, legendY - 4),
      Offset(errorLegendX, legendY + 4),
      errorPaint..strokeWidth = 2.0,
    );

    final errorTextSpan = TextSpan(
      text: 'Error',
      style: TextStyle(
        color: textColor,
        fontSize: fontSize - 2,
      ),
    );
    final errorTextPainter = TextPainter(
      text: errorTextSpan,
      textDirection: TextDirection.ltr,
    );
    errorTextPainter.layout();
    errorTextPainter.paint(
      canvas,
      Offset(errorLegendX + 10, legendY - errorTextPainter.height / 2),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}