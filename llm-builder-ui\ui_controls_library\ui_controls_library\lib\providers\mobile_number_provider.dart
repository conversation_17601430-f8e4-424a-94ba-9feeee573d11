import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Provider class for managing mobile number widget state
class MobileNumberProvider extends ChangeNotifier {
  // Private fields
  String _value = '';
  String _selectedCountryCode = '+91';
  bool _hasFocus = false;
  bool _hasError = false;
  String? _errorText;
  bool _isHovering = false;
  bool _isDropdownOpen = false;
  List<String> _filteredCountryCodes = [];
  String _searchQuery = '';

  // Getters
  String get value => _value;
  String get selectedCountryCode => _selectedCountryCode;
  bool get hasFocus => _hasFocus;
  bool get hasError => _hasError;
  String? get errorText => _errorText;
  bool get isHovering => _isHovering;
  bool get isDropdownOpen => _isDropdownOpen;
  List<String> get filteredCountryCodes => _filteredCountryCodes;
  String get searchQuery => _searchQuery;

  // Common country codes with their names and flags
  final Map<String, Map<String, dynamic>> _commonCountryCodes = {
    '+1': {'name': 'United States', 'flag': '🇺🇸'},
    '+44': {'name': 'United Kingdom', 'flag': '🇬🇧'},
    '+91': {'name': 'India', 'flag': '🇮🇳'},
    '+61': {'name': 'Australia', 'flag': '🇦🇺'},
    '+86': {'name': 'China', 'flag': '🇨🇳'},
    '+49': {'name': 'Germany', 'flag': '🇩🇪'},
    '+33': {'name': 'France', 'flag': '🇫🇷'},
    '+81': {'name': 'Japan', 'flag': '🇯🇵'},
    '+7': {'name': 'Russia', 'flag': '🇷🇺'},
    '+55': {'name': 'Brazil', 'flag': '🇧🇷'},
    '+52': {'name': 'Mexico', 'flag': '🇲🇽'},
    '+82': {'name': 'South Korea', 'flag': '🇰🇷'},
    '+39': {'name': 'Italy', 'flag': '🇮🇹'},
    '+34': {'name': 'Spain', 'flag': '🇪🇸'},
    '+1_CA': {'name': 'Canada', 'flag': '🇨🇦'},
  };

  /// Initialize the provider with initial values
  void initialize({
    String? initialValue,
    String? countryCode,
    List<String>? availableCountryCodes,
  }) {
    _value = initialValue ?? '';
    _selectedCountryCode = countryCode ?? '+91';
    _filteredCountryCodes = availableCountryCodes ?? _commonCountryCodes.keys.toList();
  }

  /// Update the mobile number value
  void updateValue(String newValue) {
    if (_value != newValue) {
      _value = newValue;
      notifyListeners();
    }
  }

  /// Update the selected country code
  void updateCountryCode(String newCountryCode) {
    if (_selectedCountryCode != newCountryCode) {
      _selectedCountryCode = newCountryCode;
      notifyListeners();
    }
  }

  /// Update focus state
  void updateFocus(bool hasFocus) {
    if (_hasFocus != hasFocus) {
      _hasFocus = hasFocus;
      notifyListeners();
    }
  }

  /// Update hover state
  void updateHover(bool isHovering) {
    if (_isHovering != isHovering) {
      _isHovering = isHovering;
      notifyListeners();
    }
  }

  /// Update error state
  void updateError(bool hasError, [String? errorText]) {
    if (_hasError != hasError || _errorText != errorText) {
      _hasError = hasError;
      _errorText = errorText;
      notifyListeners();
    }
  }

  /// Clear the mobile number value
  void clearValue() {
    _value = '';
    _hasError = false;
    _errorText = null;
    notifyListeners();
  }

  /// Validate the mobile number
  bool validate({
    required bool isRequired,
    bool Function(String)? customValidator,
    String? customErrorText,
  }) {
    if (customValidator != null) {
      final isValid = customValidator(_value);
      updateError(!isValid, isValid ? null : (customErrorText ?? 'Invalid mobile number'));
      return isValid;
    } else if (isRequired && _value.isEmpty) {
      updateError(true, 'Mobile number is required');
      return false;
    } else {
      // Enhanced mobile number validation with country-specific rules
      if (_value.isNotEmpty) {
        // Remove all non-digit characters
        final digitsOnly = _value.replaceAll(RegExp(r'\D'), '');
        
        // Country-specific validation
        switch (_selectedCountryCode) {
          case '+91': // India
            if (digitsOnly.length != 10) {
              updateError(true, 'Indian mobile number must be exactly 10 digits');
              return false;
            }
            // Indian mobile numbers must start with 6, 7, 8, or 9
            if (!digitsOnly.startsWith(RegExp(r'[6-9]'))) {
              updateError(true, 'Indian mobile number must start with 6, 7, 8, or 9');
              return false;
            }
            break;
            
          case '+1': // US/Canada
            if (digitsOnly.length != 10) {
              updateError(true, 'US/Canada mobile number must be exactly 10 digits');
              return false;
            }
            // US numbers cannot start with 0 or 1
            if (digitsOnly.startsWith(RegExp(r'[01]'))) {
              updateError(true, 'US mobile number cannot start with 0 or 1');
              return false;
            }
            break;
            
          case '+44': // UK
            if (digitsOnly.length < 10 || digitsOnly.length > 11) {
              updateError(true, 'UK mobile number must be 10-11 digits');
              return false;
            }
            break;
            
          case '+61': // Australia
            if (digitsOnly.length != 9) {
              updateError(true, 'Australian mobile number must be exactly 9 digits');
              return false;
            }
            // Australian mobile numbers start with 4
            if (!digitsOnly.startsWith('4')) {
              updateError(true, 'Australian mobile number must start with 4');
              return false;
            }
            break;
            
          case '+86': // China
            if (digitsOnly.length != 11) {
              updateError(true, 'Chinese mobile number must be exactly 11 digits');
              return false;
            }
            // Chinese mobile numbers start with 1
            if (!digitsOnly.startsWith('1')) {
              updateError(true, 'Chinese mobile number must start with 1');
              return false;
            }
            break;
            
          default:
            // Generic validation for other countries
            if (digitsOnly.length < 6 || digitsOnly.length > 15) {
              updateError(true, 'Mobile number must be 6-15 digits');
              return false;
            }
        }
      }

      updateError(false);
      return true;
    }
  }

  /// Format the mobile number
  String formatNumber(String value, {bool autoFormat = false, String? formatPattern}) {
    if (!autoFormat || value.isEmpty) {
      return value;
    }

    // Remove all non-digit characters
    final digitsOnly = value.replaceAll(RegExp(r'\D'), '');

    // Apply custom format pattern if provided
    if (formatPattern != null) {
      String formattedNumber = formatPattern;
      int digitIndex = 0;

      for (int i = 0; i < formattedNumber.length && digitIndex < digitsOnly.length; i++) {
        if (formattedNumber[i] == 'X' || formattedNumber[i] == 'x') {
          formattedNumber = formattedNumber.replaceRange(i, i + 1, digitsOnly[digitIndex]);
          digitIndex++;
        }
      }

      // If we haven't used all digits, truncate
      if (digitIndex < digitsOnly.length) {
        return digitsOnly;
      }

      // If we haven't filled all placeholders, remove the remaining ones
      formattedNumber = formattedNumber.replaceAll(RegExp(r'[Xx]'), '');

      return formattedNumber;
    }

    // Default US format: (XXX) XXX-XXXX
    if (digitsOnly.length <= 3) {
      return digitsOnly;
    } else if (digitsOnly.length <= 6) {
      return '(${digitsOnly.substring(0, 3)}) ${digitsOnly.substring(3)}';
    } else {
      final int endIndex = digitsOnly.length > 10 ? 10 : digitsOnly.length;
      return '(${digitsOnly.substring(0, 3)}) ${digitsOnly.substring(3, 6)}-${digitsOnly.substring(6, endIndex)}';
    }
  }

  /// Handle paste operation
  Future<void> handlePaste() async {
    final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
    if (data != null && data.text != null) {
      // Extract digits only from the pasted text
      final digitsOnly = data.text!.replaceAll(RegExp(r'\D'), '');
      updateValue(digitsOnly);
    }
  }

  /// Handle copy operation
  Future<void> handleCopy() async {
    final fullNumber = '$_selectedCountryCode$_value';
    await Clipboard.setData(ClipboardData(text: fullNumber));
  }

  /// Update dropdown state
  void updateDropdownState(bool isOpen) {
    if (_isDropdownOpen != isOpen) {
      _isDropdownOpen = isOpen;
      notifyListeners();
    }
  }

  /// Filter country codes based on search query
  void filterCountryCodes(String query, List<String>? availableCountryCodes) {
    _searchQuery = query;
    final List<String> allCodes = availableCountryCodes ?? _commonCountryCodes.keys.toList();
    
    if (query.isEmpty) {
      _filteredCountryCodes = allCodes;
    } else {
      _filteredCountryCodes = allCodes.where((code) {
        final countryName = getCountryName(code).toLowerCase();
        final searchQuery = query.toLowerCase();
        
        // Search by country code or country name
        return code.toLowerCase().contains(searchQuery) ||
               countryName.contains(searchQuery);
      }).toList();
    }
    notifyListeners();
  }

  /// Get country name for a country code
  String getCountryName(String countryCode) {
    return _commonCountryCodes.containsKey(countryCode)
        ? _commonCountryCodes[countryCode]!['name']
        : 'Unknown';
  }

  /// Get flag for a country code
  String getFlag(String countryCode) {
    return _commonCountryCodes.containsKey(countryCode)
        ? _commonCountryCodes[countryCode]!['flag']
        : '🏳️';
  }

  /// Reset the provider state
  void reset() {
    _value = '';
    _selectedCountryCode = '+91';
    _hasFocus = false;
    _hasError = false;
    _errorText = null;
    _isHovering = false;
    _isDropdownOpen = false;
    _filteredCountryCodes = _commonCountryCodes.keys.toList();
    _searchQuery = '';
    notifyListeners();
  }
}
