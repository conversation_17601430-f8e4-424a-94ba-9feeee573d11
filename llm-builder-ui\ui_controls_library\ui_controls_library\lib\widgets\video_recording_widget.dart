import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

/// A widget that provides video recording functionality.
///
/// This widget allows users to record videos using the device's camera.
/// It supports various configuration options such as camera direction,
/// video quality, recording duration limits, and UI customization.
class VideoRecordingWidget extends StatefulWidget {
  /// Whether to use the front camera
  final bool useFrontCamera;

  /// The resolution preset for the video
  final ResolutionPreset resolutionPreset;

  /// Maximum recording duration in seconds (null for unlimited)
  final int? maxDuration;

  /// Whether to show a timer during recording
  final bool showTimer;

  /// Whether to show recording controls
  final bool showControls;

  /// Custom text for the record button
  final String? recordButtonText;

  /// Custom text for the stop button
  final String? stopButtonText;

  /// Custom text for the switch camera button
  final String? switchCameraButtonText;

  /// Color of the record button
  final Color recordButtonColor;

  /// Color of the stop button
  final Color stopButtonColor;

  /// Whether to show a preview after recording
  final bool showPreview;

  /// Whether to enable audio recording
  final bool enableAudio;

  /// Callback when a video is recorded
  final Function(File videoFile)? onVideoRecorded;

  /// JSON configuration for the widget
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON configuration
  final bool useJsonConfig;

  /// JSON callbacks
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  const VideoRecordingWidget({
    super.key,
    this.useFrontCamera = false,
    this.resolutionPreset = ResolutionPreset.medium,
    this.maxDuration,
    this.showTimer = true,
    this.showControls = true,
    this.recordButtonText,
    this.stopButtonText,
    this.switchCameraButtonText,
    this.recordButtonColor = Colors.red,
    this.stopButtonColor = Colors.red,
    this.showPreview = true,
    this.enableAudio = true,
    this.onVideoRecorded,
    this.jsonConfig,
    this.useJsonConfig = false,
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
  });

  /// Helper method to parse a color from a string or map
  static Color _parseColor(dynamic colorValue, [Color defaultColor = Colors.red]) {
    if (colorValue == null) return defaultColor;

    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'transparent': return Colors.transparent;
          default: return defaultColor;
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a = colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return defaultColor;
  }

  /// Helper method to convert a color to a string
  static String _colorToString(Color color) {
    final r = color.toString().split('(0x')[1].substring(2, 4);
    final g = color.toString().split('(0x')[1].substring(4, 6);
    final b = color.toString().split('(0x')[1].substring(6, 8);
    return '#$r$g$b';
  }

  /// Helper method to parse a ResolutionPreset from a string
  static ResolutionPreset _parseResolutionPreset(dynamic presetValue) {
    if (presetValue == null) return ResolutionPreset.medium;

    if (presetValue is String) {
      switch (presetValue.toLowerCase()) {
        case 'low': return ResolutionPreset.low;
        case 'medium': return ResolutionPreset.medium;
        case 'high': return ResolutionPreset.high;
        case 'veryhigh':
        case 'very_high':
        case 'very high': return ResolutionPreset.veryHigh;
        case 'ultrahigh':
        case 'ultra_high':
        case 'ultra high': return ResolutionPreset.ultraHigh;
        case 'max': return ResolutionPreset.max;
        default: return ResolutionPreset.medium;
      }
    }
    return ResolutionPreset.medium;
  }

  /// Helper method to convert a ResolutionPreset to a string
  static String _resolutionPresetToString(ResolutionPreset preset) {
    switch (preset) {
      case ResolutionPreset.low: return 'low';
      case ResolutionPreset.medium: return 'medium';
      case ResolutionPreset.high: return 'high';
      case ResolutionPreset.veryHigh: return 'veryHigh';
      case ResolutionPreset.ultraHigh: return 'ultraHigh';
      case ResolutionPreset.max: return 'max';
    }
  }

  /// Creates a VideoRecordingWidget from a JSON map
  ///
  /// This factory constructor allows for creating a fully configured video recording widget
  /// from a JSON object, making it easy to load configurations from external sources.
  factory VideoRecordingWidget.fromJson(dynamic jsonData) {
    // Handle string JSON input
    Map<String, dynamic> json;
    if (jsonData is String) {
      json = jsonDecode(jsonData) as Map<String, dynamic>;
    } else if (jsonData is Map<String, dynamic>) {
      json = jsonData;
    } else {
      throw ArgumentError('Invalid JSON data: $jsonData');
    }

    // Parse basic properties
    final useFrontCamera = json['useFrontCamera'] as bool? ?? false;
    final resolutionPreset = _parseResolutionPreset(json['resolutionPreset']);

    // Parse duration properties
    final maxDuration = json['maxDuration'] as int?;

    // Parse UI properties
    final showTimer = json['showTimer'] as bool? ?? true;
    final showControls = json['showControls'] as bool? ?? true;
    final recordButtonText = json['recordButtonText'] as String?;
    final stopButtonText = json['stopButtonText'] as String?;
    final switchCameraButtonText = json['switchCameraButtonText'] as String?;

    // Parse color properties
    final recordButtonColor = json['recordButtonColor'] != null
        ? _parseColor(json['recordButtonColor'])
        : Colors.red;
    final stopButtonColor = json['stopButtonColor'] != null
        ? _parseColor(json['stopButtonColor'])
        : Colors.red;

    // Parse behavior properties
    final showPreview = json['showPreview'] as bool? ?? true;
    final enableAudio = json['enableAudio'] as bool? ?? true;

    // Parse JSON configuration
    final jsonConfig = json['jsonConfig'] as Map<String, dynamic>?;
    final useJsonConfig = json['useJsonConfig'] as bool? ?? false;
    final jsonCallbacks = json['jsonCallbacks'] as Map<String, dynamic>?;
    final useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    // Create and return the widget
    return VideoRecordingWidget(
      useFrontCamera: useFrontCamera,
      resolutionPreset: resolutionPreset,
      maxDuration: maxDuration,
      showTimer: showTimer,
      showControls: showControls,
      recordButtonText: recordButtonText,
      stopButtonText: stopButtonText,
      switchCameraButtonText: switchCameraButtonText,
      recordButtonColor: recordButtonColor,
      stopButtonColor: stopButtonColor,
      showPreview: showPreview,
      enableAudio: enableAudio,
      jsonConfig: jsonConfig,
      useJsonConfig: useJsonConfig,
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
    );
  }

  /// Converts the VideoRecordingWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    return {
      'useFrontCamera': useFrontCamera,
      'resolutionPreset': _resolutionPresetToString(resolutionPreset),
      'maxDuration': maxDuration,
      'showTimer': showTimer,
      'showControls': showControls,
      'recordButtonText': recordButtonText,
      'stopButtonText': stopButtonText,
      'switchCameraButtonText': switchCameraButtonText,
      'recordButtonColor': _colorToString(recordButtonColor),
      'stopButtonColor': _colorToString(stopButtonColor),
      'showPreview': showPreview,
      'enableAudio': enableAudio,
      'useJsonConfig': useJsonConfig,
      'useJsonCallbacks': useJsonCallbacks,
    };
  }

  @override
  State<VideoRecordingWidget> createState() => _VideoRecordingWidgetState();
}

class _VideoRecordingWidgetState extends State<VideoRecordingWidget> {
  CameraController? _cameraController;
  List<CameraDescription>? _cameras;
  bool _isCameraInitialized = false;
  bool _isRecording = false;
  bool _isPaused = false;
  bool _isPermissionDenied = false;
  String? _errorMessage;
  int _selectedCameraIndex = 0;
  Timer? _recordingTimer;
  int _recordingSeconds = 0;
  File? _recordedVideo;
  bool _isPreviewMode = false;

  @override
  void initState() {
    super.initState();

    // Apply JSON configuration if available
    if (widget.useJsonConfig && widget.jsonConfig != null) {
      final jsonWidget = VideoRecordingWidget.fromJson(widget.jsonConfig!);
      _initializeCamera(jsonWidget);
    } else {
      _initializeCamera();
    }
  }

  @override
  void dispose() {
    _recordingTimer?.cancel();
    _cameraController?.dispose();
    super.dispose();
  }

  Future<void> _initializeCamera([VideoRecordingWidget? config]) async {
    final cfg = config ?? widget;

    try {
      // Request camera and microphone permissions
      final cameraStatus = await Permission.camera.request();
      final microphoneStatus = cfg.enableAudio
          ? await Permission.microphone.request()
          : PermissionStatus.granted;

      if (cameraStatus.isDenied || microphoneStatus.isDenied) {
        setState(() {
          _isPermissionDenied = true;
          _errorMessage = 'Camera or microphone permission denied';
        });
        return;
      }

      // Get available cameras
      _cameras = await availableCameras();

      if (_cameras == null || _cameras!.isEmpty) {
        setState(() {
          _errorMessage = 'No cameras available';
        });
        return;
      }

      // Set initial camera based on preference
      if (cfg.useFrontCamera) {
        _selectedCameraIndex = _cameras!.indexWhere(
          (camera) => camera.lensDirection == CameraLensDirection.front
        );
        if (_selectedCameraIndex == -1) {
          _selectedCameraIndex = 0; // Default to first camera if front not available
        }
      }

      // Initialize the camera controller
      await _initializeCameraController(cfg);
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to initialize camera: $e';
      });
    }
  }

  Future<void> _initializeCameraController([VideoRecordingWidget? config]) async {
    final cfg = config ?? widget;

    if (_cameras == null || _cameras!.isEmpty) return;

    // Create a new camera controller
    final controller = CameraController(
      _cameras![_selectedCameraIndex],
      cfg.resolutionPreset,
      enableAudio: cfg.enableAudio,
    );

    // Initialize the controller
    try {
      await controller.initialize();

      setState(() {
        _cameraController = controller;
        _isCameraInitialized = true;
        _errorMessage = null;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to initialize camera controller: $e';
      });
    }
  }

  Future<void> _switchCamera([VideoRecordingWidget? config]) async {
    final cfg = config ?? widget;

    if (_cameras == null || _cameras!.isEmpty || _cameras!.length < 2) return;

    // Stop recording if in progress
    if (_isRecording) {
      await _stopRecording(cfg);
    }

    // Dispose current controller
    await _cameraController?.dispose();

    // Switch to next camera
    _selectedCameraIndex = (_selectedCameraIndex + 1) % _cameras!.length;

    // Re-initialize with new camera
    await _initializeCameraController(cfg);
  }

  Future<void> _startRecording([VideoRecordingWidget? config]) async {
    final cfg = config ?? widget;

    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    try {
      // Start video recording
      await _cameraController!.startVideoRecording();

      setState(() {
        _isRecording = true;
        _isPaused = false;
        _recordingSeconds = 0;
      });

      // Start timer
      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _recordingSeconds++;
        });

        // Check if max duration reached
        if (cfg.maxDuration != null && _recordingSeconds >= cfg.maxDuration!) {
          _stopRecording(cfg);
        }
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to start recording: $e';
      });
    }
  }

  Future<void> _pauseRecording([VideoRecordingWidget? config]) async {
    // We don't need to use the config parameter in this method,
    // but we keep it for consistency with other methods

    if (_cameraController == null || !_isRecording || _isPaused) {
      return;
    }

    try {
      await _cameraController!.pauseVideoRecording();

      setState(() {
        _isPaused = true;
      });

      _recordingTimer?.cancel();
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to pause recording: $e';
      });
    }
  }

  Future<void> _resumeRecording([VideoRecordingWidget? config]) async {
    final cfg = config ?? widget;

    if (_cameraController == null || !_isRecording || !_isPaused) {
      return;
    }

    try {
      await _cameraController!.resumeVideoRecording();

      setState(() {
        _isPaused = false;
      });

      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _recordingSeconds++;
        });

        // Check if max duration reached
        if (cfg.maxDuration != null && _recordingSeconds >= cfg.maxDuration!) {
          _stopRecording(cfg);
        }
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to resume recording: $e';
      });
    }
  }

  Future<void> _stopRecording([VideoRecordingWidget? config]) async {
    final cfg = config ?? widget;

    if (_cameraController == null || !_isRecording) {
      return;
    }

    try {
      _recordingTimer?.cancel();

      // Stop recording and get the file
      final videoFile = await _cameraController!.stopVideoRecording();

      // Convert XFile to File
      final file = File(videoFile.path);

      setState(() {
        _isRecording = false;
        _isPaused = false;
        _recordedVideo = file;
        _isPreviewMode = cfg.showPreview;
      });

      // Call the callback if provided
      if (widget.onVideoRecorded != null) {
        widget.onVideoRecorded!(file);
      }

      // Handle JSON callbacks if available
      if (cfg.useJsonCallbacks && cfg.jsonCallbacks != null &&
          cfg.jsonCallbacks!.containsKey('onVideoRecorded')) {
        // In a real implementation, you would execute the callback here
        // For this example, we'll just log that it would be called
        debugPrint('JSON callback would be called with: ${file.path}');
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to stop recording: $e';
        _isRecording = false;
        _isPaused = false;
      });
    }
  }

  void _resetRecording() {
    setState(() {
      _recordedVideo = null;
      _isPreviewMode = false;
    });
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Widget _buildCameraPreview() {
    if (!_isCameraInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    return AspectRatio(
      aspectRatio: _cameraController!.value.aspectRatio,
      child: CameraPreview(_cameraController!),
    );
  }

  Widget _buildVideoPreview([VideoRecordingWidget? config]) {
    // We don't need to use the config parameter in this method,
    // but we keep it for consistency with other methods

    if (_recordedVideo == null) {
      return const Center(child: Text('No video recorded'));
    }

    // In a real implementation, you would use a video player package
    // For this example, we'll just show a placeholder
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          color: Colors.black,
          child: const Center(
            child: Icon(Icons.video_file, size: 100, color: Colors.white54),
          ),
        ),
        Positioned(
          bottom: 20,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                onPressed: _resetRecording,
                icon: const Icon(Icons.replay),
                label: const Text('Record Again'),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: () {
                  // In a real implementation, you would play the video
                  // For this example, we'll just log the path
                  // Video path: ${_recordedVideo!.path}
                },
                icon: const Icon(Icons.play_arrow),
                label: const Text('Play'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildControls([VideoRecordingWidget? config]) {
    final cfg = config ?? widget;

    if (!cfg.showControls) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Switch camera button (only if multiple cameras available)
          if (_cameras != null && _cameras!.length > 1)
            IconButton(
              icon: const Icon(Icons.switch_camera),
              onPressed: _isRecording ? null : _switchCamera,
              tooltip: cfg.switchCameraButtonText ?? 'Switch Camera',
            ),

          // Record/stop button
          FloatingActionButton.extended(
            onPressed: _isRecording ? _stopRecording : _startRecording,
            backgroundColor: _isRecording ? cfg.stopButtonColor : cfg.recordButtonColor,
            icon: Icon(
              _isRecording ? Icons.stop : Icons.videocam,
              color: Colors.white,
            ),
            label: Text(
              _isRecording
                ? (cfg.stopButtonText ?? 'Stop')
                : (cfg.recordButtonText ?? 'Record'),
              style: const TextStyle(color: Colors.white),
            ),
          ),

          // Pause/resume button (only shown when recording)
          if (_isRecording)
            IconButton(
              icon: Icon(_isPaused ? Icons.play_arrow : Icons.pause),
              onPressed: _isPaused ? _resumeRecording : _pauseRecording,
              tooltip: _isPaused ? 'Resume' : 'Pause',
            )
          else
            // Placeholder to maintain layout
            const SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildTimer([VideoRecordingWidget? config]) {
    final cfg = config ?? widget;

    if (!cfg.showTimer || !_isRecording) {
      return const SizedBox.shrink();
    }

    return Positioned(
      top: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: [
            const Icon(Icons.fiber_manual_record, color: Colors.red, size: 16),
            const SizedBox(width: 4),
            Text(
              _formatDuration(_recordingSeconds),
              style: const TextStyle(color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionDeniedMessage() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.videocam_off, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          const Text(
            'Camera or microphone permission denied',
            style: TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () async {
              // Open app settings
              await openAppSettings();
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorMessage() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            _errorMessage ?? 'An unknown error occurred',
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Apply JSON configuration if available
    if (widget.useJsonConfig && widget.jsonConfig != null) {
      // Create a new widget with the JSON configuration applied
      final jsonWidget = VideoRecordingWidget.fromJson(widget.jsonConfig!);

      // Use the JSON widget's properties for rendering, but keep the current state
      return _buildWidgetWithConfig(context, jsonWidget);
    }

    return _buildWidgetWithConfig(context, widget);
  }

  /// Builds the widget with the given configuration
  Widget _buildWidgetWithConfig(BuildContext context, VideoRecordingWidget config) {
    // Show error message if there's an error
    if (_errorMessage != null) {
      return _buildErrorMessage();
    }

    // Show permission denied message if permissions are denied
    if (_isPermissionDenied) {
      return _buildPermissionDeniedMessage();
    }

    // Show video preview if in preview mode
    if (_isPreviewMode) {
      return _buildVideoPreview(config);
    }

    // Show camera preview and controls
    return Column(
      children: [
        Expanded(
          child: Stack(
            fit: StackFit.expand,
            children: [
              _buildCameraPreview(),
              _buildTimer(config),
            ],
          ),
        ),
        _buildControls(config),
      ],
    );
  }
}