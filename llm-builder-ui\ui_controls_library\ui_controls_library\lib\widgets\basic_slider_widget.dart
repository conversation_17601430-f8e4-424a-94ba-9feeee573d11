import 'package:flutter/material.dart';
 
class SliderWidget extends StatefulWidget {
  final double min;
  final double max;
  final int? divisions;
  final double? initial;
  final Color activeColor;
  final Color? inactiveColor;
  final Color? thumbColor;
  final double? trackHeight;
  final TextStyle? labelStyle;
  final ValueChanged<double>? onChanged;
  final double? testValue; // For testing purposes only
  final bool enabled; // New property to control enabled/disabled state
  final String? label; // New property for label text
 
  const SliderWidget({
    super.key,
    required this.min,
    required this.max,
    this.divisions,
    this.initial,
    required this.activeColor,
    this.inactiveColor,
    this.thumbColor,
    this.trackHeight,
    this.labelStyle,
    this.onChanged,
    this.testValue,
    this.enabled = true,
    this.label,
  });
 
  /// Creates a SliderWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the SliderWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "min": 0,
  ///   "max": 100,
  ///   "divisions": 5,
  ///   "initial": 50,
  ///   "activeColor": "red",
  ///   "inactiveColor": "grey",
  ///   "thumbColor": "red",
  ///   "trackHeight": 4.0,
  ///   "enabled": true,
  ///   "label": "Age"
  /// }
  /// ```
  factory SliderWidget.fromJson(Map<String, dynamic> json) {
    return SliderWidget(
      min: (json['min'] as num?)?.toDouble() ?? 0.0,
      max: (json['max'] as num?)?.toDouble() ?? 100.0,
      divisions: json['divisions'] as int?,
      initial: (json['initial'] as num?)?.toDouble(),
      activeColor: _colorFromJson(json['activeColor']) ?? Color(0xFF0058FF),
      inactiveColor: _colorFromJson(json['inactiveColor']),
      thumbColor: _colorFromJson(json['thumbColor']),
      trackHeight: (json['trackHeight'] as num?)?.toDouble(),
      enabled: json['enabled'] ?? true,
      label: json['label'],
      labelStyle:
          json['labelStyle'] != null
              ? TextStyle(
                fontSize:
                    (json['labelStyle']['fontSize'] as num?)?.toDouble() ??
                    16.0,
                fontWeight:
                    json['labelStyle']['bold'] == true
                        ? FontWeight.bold
                        : FontWeight.normal,
                fontStyle:
                    json['labelStyle']['italic'] == true
                        ? FontStyle.italic
                        : FontStyle.normal,
                color: _colorFromJson(json['labelStyle']['color']),
              )
              : null,
      onChanged: (value) {
        // This would be handled by the app in a real implementation
      },
    );
  }
 
  /// Converts the SliderWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    return {
      'min': min,
      'max': max,
      if (divisions != null) 'divisions': divisions,
      if (initial != null) 'initial': initial,
      'activeColor': _colorToJson(activeColor),
      if (inactiveColor != null) 'inactiveColor': _colorToJson(inactiveColor!),
      if (thumbColor != null) 'thumbColor': _colorToJson(thumbColor!),
      if (trackHeight != null) 'trackHeight': trackHeight,
      'enabled': enabled,
      if (label != null) 'label': label,
      if (labelStyle != null)
        'labelStyle': {
          'fontSize': labelStyle!.fontSize,
          'bold': labelStyle!.fontWeight == FontWeight.bold,
          'italic': labelStyle!.fontStyle == FontStyle.italic,
          if (labelStyle!.color != null)
            'color': _colorToJson(labelStyle!.color!),
        },
    };
  }
 
  /// Converts a JSON color value to a Flutter Color
  ///
  /// Accepts hex strings (e.g., "#FF0000"), color names (e.g., "red"),
  /// or integer values (e.g., 0xFFFF0000)
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;
 
    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);
 
        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }
 
        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }
 
        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          // Silently handle the error and return null
          return null;
        }
      }
 
      // Handle named colors - return MaterialColor for standard colors
      // to ensure round-trip conversion works correctly
      switch (colorValue.toLowerCase()) {
        case 'red':
          return Colors.red;
        case 'blue':
          return Color(0xFF0058FF);
        case 'green':
          return Colors.green;
        case 'yellow':
          return Colors.yellow;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'pink':
          return Colors.pink;
        case 'brown':
          return Colors.brown;
        case 'grey':
        case 'gray':
          return Colors.grey;
        case 'black':
          return Colors.black;
        case 'white':
          return Colors.white;
        case 'amber':
          return Colors.amber;
        case 'cyan':
          return Colors.cyan;
        case 'indigo':
          return Colors.indigo;
        case 'lime':
          return Colors.lime;
        case 'teal':
          return Colors.teal;
        default:
          return null;
      }
    } else if (colorValue is int) {
      // Handle integer color values
      return Color(colorValue);
    }
 
    return null;
  }
 
  /// Converts a Flutter Color to a JSON representation
  ///
  /// Returns a hex string (e.g., "#FF0000") or a color name for standard colors
  static String _colorToJson(Color color) {
    // Handle standard colors by name for better readability and test compatibility
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';
    if (color == Colors.teal) return 'teal';
 
    // For MaterialColor, preserve the original color name if possible
    if (color is MaterialColor) {
      // We'll keep the original MaterialColor in the round-trip conversion
      // by using a special format that our fromJson method can recognize
      if (color == Colors.red) return 'red';
      if (color == Colors.blue) return 'blue';
      if (color == Colors.green) return 'green';
      if (color == Colors.yellow) return 'yellow';
      if (color == Colors.orange) return 'orange';
      if (color == Colors.purple) return 'purple';
      if (color == Colors.pink) return 'pink';
      if (color == Colors.brown) return 'brown';
      if (color == Colors.grey) return 'grey';
      if (color == Colors.amber) return 'amber';
      if (color == Colors.cyan) return 'cyan';
      if (color == Colors.indigo) return 'indigo';
      if (color == Colors.lime) return 'lime';
      if (color == Colors.teal) return 'teal';
 
      // If it's a MaterialColor but not one of the standard ones,
      // fall back to the hex representation of the primary value
      color = color.shade500;
    }
 
    // Convert to RGB format and create a hex string for other colors
    final r = (color.r * 255).round().toRadixString(16).padLeft(2, '0');
    final g = (color.g * 255).round().toRadixString(16).padLeft(2, '0');
    final b = (color.b * 255).round().toRadixString(16).padLeft(2, '0');
 
    return '#$r$g$b';
  }
 
  @override
  State<SliderWidget> createState() => _SliderWidgetState();
}
 
class _SliderWidgetState extends State<SliderWidget> {
  late double _value;
  bool _isSliding = false;
 
  @override
  void initState() {
    super.initState();
    _initializeValue();
  }
 
  @override
  void didUpdateWidget(SliderWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
 
    // Check if any of the bounds or initial value changed
    if (oldWidget.min != widget.min ||
        oldWidget.max != widget.max ||
        oldWidget.initial != widget.initial) {
      _initializeValue();
    }
  }
 
  void _initializeValue() {
    // Use test value if provided (for testing purposes)
    if (widget.testValue != null) {
      _value = widget.testValue!;
      return;
    }
 
    // Safely initialize the value
    double initialValue = widget.initial ?? widget.min;
 
    // Ensure the initial value is within bounds
    if (initialValue < widget.min) {
      _value = widget.min; // Fallback to min if initial is too low
    } else if (initialValue > widget.max) {
      _value = widget.max; // Fallback to max if initial is too high
    } else {
      _value = initialValue; // Set the initial value if within bounds
    }
  }
 
  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
 
    if (screenWidth > 1920) {
      return 16.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 14.0; // Large
    } else if (screenWidth >= 1280) {
      return 12.0; // Medium
    } else {
      return 12.0; // Default for very small screens
    }
  }
 
  double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
 
    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }
 
  @override
  Widget build(BuildContext context) {
    return
   LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.label != null)
              Text(
                widget.label!,
                style:  TextStyle(
                  fontSize: _getResponsiveFontSize(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: 'Inter',
                ),
              ),
              SizedBox(height: _getResponsiveBoxsize(context)),
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                trackHeight: widget.trackHeight ?? 8.0, // Set track height to 8 pixels by default
                thumbShape: CustomSliderThumbCircle(
                  thumbRadius: 12,
                  thumbColor: widget.activeColor,
                  min: widget.min,
                  max: widget.max,
                  textStyle:  TextStyle(
                    fontSize: 12,
                    fontFamily: 'Inter',
                    color: Colors.white,
                    fontWeight: FontWeight.normal,
                  ),
                ),
                overlayShape: SliderComponentShape.noOverlay,
                activeTrackColor: widget.activeColor,
                inactiveTrackColor: Colors.grey.shade300,
               // trackShape: const RectangularSliderTrackShape(),
                thumbColor: Colors.transparent,
                showValueIndicator: ShowValueIndicator.never,
              ),
              child: Slider(
                padding: EdgeInsets.only(left:0), 
                value: _value,
                min: widget.min,
                max: widget.max,
                divisions: widget.divisions,
                onChanged: widget.enabled
                    ? (value) {
                        setState(() {
                          _value = value;
                        });
                      }
                    : null,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.min.round().toString(),
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize(context),
                    fontFamily: 'Inter',
                    color: Colors.black,
                  ),
                ),
                Text(
                  widget.max.round().toString(),
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize(context),
                    fontFamily: 'Inter',
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
 
  }
  
double _getResponsiveBoxsize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 8.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 8.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 6.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 4.0; // Small (768-1024px)
  } else {
    return 4.0; // Default for very small screens
  }
}

}
 
/// Custom slider thumb shape to implement the three states
class CustomSliderThumbShape extends SliderComponentShape {
  final double enabledThumbRadius;
  final bool isSliding;
  final bool isEnabled;
  final Color activeColor;
 
  const CustomSliderThumbShape({
    required this.enabledThumbRadius,
    required this.isSliding,
    required this.isEnabled,
    required this.activeColor,
  });
 
  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(enabledThumbRadius);
  }
 
  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;
 
    // Define colors based on state
    Color thumbColor;
    Color borderColor;
    double borderWidth;
 
    if (!isEnabled) {
      // Disabled state: Gray thumb with gray border
      thumbColor = Colors.grey.shade400;
      borderColor = Colors.grey.shade400;
      borderWidth = 2.0;
    } else if (isSliding) {
      // Sliding state: Filled red thumb
      thumbColor = activeColor;
      borderColor = activeColor;
      borderWidth = 0.0;
    } else {
      // Default state: White thumb with red border
      thumbColor = Colors.white;
      borderColor = activeColor;
      borderWidth = 2.0;
    }
 
    // Draw the thumb
    final paint =
        Paint()
          ..color = thumbColor
          ..style = PaintingStyle.fill;
 
    // Draw filled circle
    canvas.drawCircle(center, enabledThumbRadius, paint);
 
    // Draw border if needed
    if (borderWidth > 0) {
      final borderPaint =
          Paint()
            ..color = borderColor
            ..style = PaintingStyle.stroke
            ..strokeWidth = borderWidth;
 
      canvas.drawCircle(
        center,
        enabledThumbRadius - borderWidth / 2,
        borderPaint,
      );
    }
  }
}

class CustomSliderThumbCircle extends SliderComponentShape {
  final double thumbRadius;
  final Color thumbColor;
  final double min;
  final double max;
  final TextStyle textStyle;
 
  CustomSliderThumbCircle({
    required this.thumbRadius,
    required this.thumbColor,
    required this.min,
    required this.max,
    required this.textStyle,
  });
 
  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(thumbRadius);
  }
 
  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final canvas = context.canvas;
 
    // Simple scaling based on activation animation
    // activationAnimation goes from 0.0 (normal) to 1.0 (pressed/dragging)
    final double scale = 1.0 + (activationAnimation.value * 0.1); // Scale up to 1.1 when active
    final double currentRadius = thumbRadius * scale;
 
    // Draw white background circle
    final backgroundPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, currentRadius, backgroundPaint);
 
    // Draw blue border
    final borderPaint = Paint()
      ..color = thumbColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    canvas.drawCircle(center, currentRadius - 1.0, borderPaint);
 
    // Draw text (value inside thumb) with blue color to match border
    final textSpan = TextSpan(
      text: '${(min + (value * (max - min))).round()}',
      style: textStyle.copyWith(
        color: thumbColor,
        fontSize: textStyle.fontSize! * scale, // Scale text with thumb
      ),
    );
 
    final tp = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: textDirection,
    );
 
    tp.layout();
    tp.paint(
      canvas,
      center - Offset(tp.width / 2, tp.height / 2),
    );
  }
}
