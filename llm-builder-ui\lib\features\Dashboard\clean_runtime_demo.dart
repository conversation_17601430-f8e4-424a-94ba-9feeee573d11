import 'package:flutter/material.dart';
import 'package:ui_controls_library/ui_controls_library.dart' as ui_controls;
/// Clean Runtime Demo - Only Complete Widget Tree JSON Approach
/// Three steps: Widget → JSON → Widget
/// Three sizes: Small, Medium, Large
class CleanRuntimeDemo extends StatefulWidget {
  const CleanRuntimeDemo({super.key});

  @override
  State<CleanRuntimeDemo> createState() => _CleanRuntimeDemoState();
}

class _CleanRuntimeDemoState extends State<CleanRuntimeDemo> {
  // Current size selection
  String _selectedSize = 'Medium';
  String _selectedMonth = 'July';

  // Step 1: Original Widget
  Widget? _originalWidget;
  
  // Step 2: JSON String
  String _jsonOutput = 'Click "Generate JSON" to see the complete widget tree structure';
  
  // Step 3: Recreated Widget
  Widget? _recreatedWidget;
  
  // Statistics
  Map<String, dynamic> _stats = {};

  @override
  void initState() {
    super.initState();
    // Don't create widget here - context not fully available for MediaQuery
  }

  void _createOriginalWidget() {
    final selectedDate = DateTime(2025, 7, 21);
    
    setState(() {
      switch (_selectedSize) {
        case 'Small':
          _originalWidget = ui_controls.CompleteRuntimeCalendar.createCalendar(
            context: context,
            size: ui_controls.CalendarSize.small,
            selectedDate: selectedDate,
            selectedColor: Colors.blue,
            todayColor: Colors.orange.shade100,
            selectedMonth: _selectedMonth,
            onMonthChanged: (String month) {
              setState(() {
                _selectedMonth = month;
                _createOriginalWidget(); // Recreate widget with new month
              });
            },
          );
          break;
        case 'Medium':
          _originalWidget = ui_controls.CompleteRuntimeCalendar.createCalendar(
            context: context,
            size: ui_controls.CalendarSize.medium,
            selectedDate: selectedDate,
            selectedColor: Colors.blue,
            todayColor: Colors.orange.shade100,
            selectedMonth: _selectedMonth,
            onMonthChanged: (String month) {
              setState(() {
                _selectedMonth = month;
                _createOriginalWidget(); // Recreate widget with new month
              });
            },
          );
          break;
        case 'Large':
          _originalWidget = ui_controls.CompleteRuntimeCalendar.createCalendar(
            context: context,
            size: ui_controls.CalendarSize.large,
            selectedDate: selectedDate,
            selectedColor: Colors.blue,
            todayColor: Colors.orange.shade100,
            selectedMonth: _selectedMonth,
            onMonthChanged: (String month) {
              setState(() {
                _selectedMonth = month;
                _createOriginalWidget(); // Recreate widget with new month
              });
            },
          );
          break;
      }
      
      // Clear JSON and recreated widget when size changes
      _jsonOutput = 'Click "Generate JSON" to see the complete widget tree structure';
      _recreatedWidget = null;
      _stats = {};
    });
  }

  void _generateJson() {
    if (_originalWidget != null) {
      try {
        final startTime = DateTime.now().microsecondsSinceEpoch;
        final jsonString = ui_controls.CompleteRuntimeCalendar.widgetToJson(_originalWidget!);
        final endTime = DateTime.now().microsecondsSinceEpoch;
        
        setState(() {
          _jsonOutput = jsonString;
          _stats = {
            'serializationTime': endTime - startTime,
            'jsonSize': jsonString.length,
            'jsonLines': jsonString.split('\n').length,
          };
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ JSON generated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        setState(() {
          _jsonOutput = 'JSON GENERATION FAILED!\n\nError: $e';
          _stats = {};
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ JSON generation failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _recreateWidget() {
    if (_jsonOutput.isNotEmpty && !_jsonOutput.startsWith('Click') && !_jsonOutput.startsWith('JSON GENERATION FAILED')) {
      try {
        final startTime = DateTime.now().microsecondsSinceEpoch;
        final recreatedWidget = ui_controls.CompleteRuntimeCalendar.jsonToWidget(_jsonOutput);
        final endTime = DateTime.now().microsecondsSinceEpoch;
        
        if (recreatedWidget != null) {
          setState(() {
            _recreatedWidget = recreatedWidget;
            _stats['deserializationTime'] = endTime - startTime;
          });
          
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ Widget recreated successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          throw Exception('Deserialization returned null widget');
        }
      } catch (e) {
        setState(() {
          _recreatedWidget = Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              border: Border.all(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'WIDGET RECREATION FAILED!\n\nError: $e',
              style: const TextStyle(color: Colors.red),
            ),
          );
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Widget recreation failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('⚠️ Generate JSON first!'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _performCompleteDemo() {
    _generateJson();
    // Wait a bit then recreate widget
    Future.delayed(const Duration(milliseconds: 500), () {
      _recreateWidget();
    });
  }

  void _clearAll() {
    setState(() {
      _jsonOutput = 'Click "Generate JSON" to see the complete widget tree structure';
      _recreatedWidget = null;
      _stats = {};
    });
  }

  @override
  Widget build(BuildContext context) {
    // Create widget on first build when context is available
    if (_originalWidget == null) {
      _createOriginalWidget();
    }

    return Scaffold(
     
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Size selection
            Row(
              children: [
                const Text(
                  'Calendar Size:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: 16),
                DropdownButton<String>(
                  value: _selectedSize,
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedSize = newValue;
                      });
                      _createOriginalWidget();
                    }
                  },
                  items: ['Small', 'Medium', 'Large']
                      .map<DropdownMenuItem<String>>((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Control buttons
            Wrap(
              spacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _generateJson,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.transparent),
                  child: const Text('1. Generate JSON'),
                ),
                ElevatedButton(
                  onPressed: _recreateWidget,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.transparent),
                  child: const Text('2. Recreate Widget'),
                ),
                ElevatedButton(
                  onPressed: _performCompleteDemo,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.transparent),
                  child: const Text('Complete Demo'),
                ),
                ElevatedButton(
                  onPressed: _clearAll,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.transparent),
                  child: const Text('Clear'),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Statistics
            if (_stats.isNotEmpty) _buildStatistics(),
            
            const SizedBox(height: 24),
            
            // Three-step demonstration
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Step 1: Original Widget
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                       Text(
                        '1️⃣ Original Widget ($_selectedSize)',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      if (_originalWidget != null)
                        Container(
                          decoration: BoxDecoration(
                            // border: Border.all(color: Colors.transparent, width: 2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: _originalWidget!,
                        ),
                    ],
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Step 2: JSON
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '2️⃣ Complete Widget Tree JSON',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        height: 300,
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: SingleChildScrollView(
                          child: SelectableText(
                            _jsonOutput,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 10,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Step 3: Recreated Widget
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '3️⃣ Recreated from JSON',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      if (_recreatedWidget != null)
                        Container(
                          decoration: const BoxDecoration(
                            // border: Border.all(color: Colors.transparent, width: 2),
                            // borderRadius: BorderRadius.circular(8),
                          ),
                          child: _recreatedWidget!,
                        )
                      else
                        Container(
                          height: 200,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.widgets, size: 48, color: Colors.grey),
                                SizedBox(height: 8),
                                Text(
                                  'Click "Recreate Widget"\nto see the magic!',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatistics() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '📊 Performance Statistics',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text('Serialization Time: ${_stats['serializationTime'] ?? 'N/A'} μs'),
          Text('Deserialization Time: ${_stats['deserializationTime'] ?? 'N/A'} μs'),
          Text('JSON Size: ${_stats['jsonSize'] ?? 'N/A'} characters'),
          Text('JSON Lines: ${_stats['jsonLines'] ?? 'N/A'} lines'),
        ],
      ),
    );
  }
}
