import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class CameraScreen extends StatefulWidget {
  final bool useFrontCamera;
  final double imageQuality;
  final String? resolution;
  final String? buttonText;

  const CameraScreen({
    super.key,
    this.useFrontCamera = false,
    this.imageQuality = 0.8,
    this.resolution,
    this.buttonText,
  });

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen> {
  final ImagePicker _picker = ImagePicker();
  bool _isLoading = false;
  String? _errorMessage;
  bool _mounted = true;

  @override
  void initState() {
    super.initState();
    // Open camera immediately when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _openCamera();
    });
  }

  @override
  void dispose() {
    _mounted = false;
    super.dispose();
  }

  Future<void> _openCamera() async {
    if (!_mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Parse resolution if provided
      int? maxWidth;
      int? maxHeight;

      if (widget.resolution != null) {
        final resolutionParts = widget.resolution!.split('x');
        if (resolutionParts.length == 2) {
          maxWidth = int.tryParse(resolutionParts[0]);
          maxHeight = int.tryParse(resolutionParts[1]);
        }
      }

      // Convert quality from 0.0-1.0 to 0-100 scale
      final int quality = (widget.imageQuality * 100).round();

      // Set camera device based on preference
      final CameraDevice preferredCamera = widget.useFrontCamera ?
          CameraDevice.front : CameraDevice.rear;

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: maxWidth?.toDouble(),
        maxHeight: maxHeight?.toDouble(),
        imageQuality: quality,
        preferredCameraDevice: preferredCamera,
      );

      if (!_mounted) return;

      if (image != null) {
        if (kIsWeb) {
          // Handle web platform
          final bytes = await image.readAsBytes();
          if (!_mounted) return;
          if (context.mounted) {
            Navigator.of(context).pop<Uint8List>(bytes);
          }
        } else {
          // Handle mobile platforms
          if (!_mounted) return;
          if (context.mounted) {
            Navigator.of(context).pop<File>(File(image.path));
          }
        }
      } else {
        // User canceled
        if (!_mounted) return;
        if (context.mounted) {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (!_mounted) return;

      setState(() {
        _errorMessage = 'Camera permission denied. Please enable camera access in your device settings.';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Center(
          child: _isLoading
              ? const CircularProgressIndicator(color: Colors.white)
              : _errorMessage != null
                  ? Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error_outline, color: Colors.red, size: 48),
                        const SizedBox(height: 16),
                        Text(
                          _errorMessage!,
                          style: const TextStyle(color: Colors.white),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Go Back'),
                        ),
                      ],
                    )
                  : const SizedBox(),
        ),
      ),
    );
  }
}
