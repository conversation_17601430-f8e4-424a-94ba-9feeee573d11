import 'dart:developer';

import 'package:builder_app/features/login/presentation/bloc/app_config/config_event.dart';
import 'package:builder_app/features/login/presentation/bloc/app_config/config_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:builder_app/core/utils/shared_prefs_helper.dart';

class AppConfigBloc extends Bloc<ConfigEvent, ConfigState> {
  static const String _languageCodeKey = 'language_code';
  static const String _countryCodeKey = 'country_code';

  // Default locale
  Locale _locale = const Locale('en', 'US');

  // Getter for current locale
  Locale get locale => _locale;

  // Getter for supported locales
  List<Locale> get supportedLocales => _supportedLocales;
  void setLocali() {
    add(AppConfigEvent());
  }

  AppConfigBloc() : super(AppConfigInitialState()) {
    on<AppConfigEvent>((event, emit) => _setLocali(event, emit));
    on<CheckLoginStatusEvent>((event, emit) async {
      emit(AppConfigLoadingState());
      final token = await SharedPrefsHelper().getString('access_token');
      if (token != null && token.isNotEmpty) {
        emit(UserLoggedInState());
      } else {
        emit(UserLoggedOutState());
      }
    });
    on<LogoutEvent>((event, emit) async {
      emit(AppConfigLoadingState());
      try {
        // Clear stored tokens and user data
        await SharedPrefsHelper().remove('access_token');
        await SharedPrefsHelper().remove('refresh_token');
        await SharedPrefsHelper().remove('user');
        log('User logged out successfully');
        emit(UserLoggedOutState());
      } catch (e) {
        log('Error during logout: $e');
        emit(AppConfigFailedState(message: 'Logout failed: $e'));
      }
    });
  }
  // Load saved locale from SharedPreferences
  Future<void> _loadSavedLocale() async {
    try {
      final languageCode = await SharedPrefsHelper().getString(_languageCodeKey);
      final countryCode = await SharedPrefsHelper().getString(_countryCodeKey);

      if (languageCode != null && countryCode != null) {
        _locale = Locale(languageCode, countryCode);
        log('Loaded saved locale: $languageCode-$countryCode');
      } else {
        log('No saved locale found, using default: en-US');
      }
    } catch (e) {
      log('Error loading saved locale: $e');
    }
  }

  void _setLocali(AppConfigEvent event, Emitter<ConfigState> emit) async {
    await _loadSavedLocale();
    emit(AppConfigLoadingState());

    if (!_isSupportedLocale(locale)) {
      log('Unsupported locale: ${locale.languageCode}-${locale.countryCode}');
      return;
    }

    try {
      await SharedPrefsHelper().setString(_languageCodeKey, locale.languageCode);
      await SharedPrefsHelper().setString(_countryCodeKey, locale.countryCode ?? '');

      _locale = locale;
      log('Set locale to: ${locale.languageCode}-${locale.countryCode}');
      emit(AppConfigSuccessState());
    } catch (e) {
      log('Error saving locale: $e');
      emit(AppConfigFailedState(message: "$e"));
    }
  }

  // Check if locale is supported
  bool _isSupportedLocale(Locale locale) {
    return _supportedLocales.any(
      (supportedLocale) =>
          supportedLocale.languageCode == locale.languageCode &&
          supportedLocale.countryCode == locale.countryCode,
    );
  }

  // Available locales
  final List<Locale> _supportedLocales = const [
    Locale('en', 'US'), // English
    Locale('es', 'ES'), // Spanish
    Locale('fr', 'FR'), // French
    Locale('de', 'DE'), // German
    Locale('zh', 'CN'), // Chinese
    Locale('ja', 'JP'), // Japanese
    Locale('ko', 'KR'), // Korean
    Locale('ru', 'RU'), // Russian
    Locale('ar', 'SA'), // Arabic
    Locale('hi', 'IN'), // Hindi
    Locale('te', 'IN'), // Telugu
  ];

  // Public method to check login status
  void checkLoginStatus() {
    add(CheckLoginStatusEvent());
  }
}
