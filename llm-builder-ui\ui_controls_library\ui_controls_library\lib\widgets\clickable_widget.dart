import 'package:flutter/material.dart';

/// A highly configurable clickable widget that can wrap any content.
///
/// This widget provides a variety of customization options for creating
/// interactive elements with different appearances and behaviors.
class ClickableWidget extends StatefulWidget {
  /// The child widget to make clickable.
  final Widget child;

  /// The callback when the widget is tapped.
  final VoidCallback? onTap;

  /// The callback when the widget is double-tapped.
  final VoidCallback? onDoubleTap;

  /// The callback when the widget is long-pressed.
  final VoidCallback? onLongPress;

  /// The background color of the clickable area.
  final Color backgroundColor;

  /// The border color of the clickable area. If null, no border is drawn.
  final Color? borderColor;

  /// The width of the border. Only applicable if borderColor is not null.
  final double borderWidth;

  /// The border radius of the clickable area.
  final double borderRadius;

  /// The padding inside the clickable area.
  final EdgeInsetsGeometry padding;

  /// The margin around the clickable area.
  final EdgeInsetsGeometry margin;

  /// The elevation of the clickable area (shadow).
  final double elevation;

  /// Whether the widget is disabled.
  final bool isDisabled;

  /// The color of the splash effect when tapped.
  final Color? splashColor;

  /// The color of the highlight effect when pressed.
  final Color? highlightColor;

  /// Whether to show a ripple effect when tapped.
  final bool hasRippleEffect;

  /// The tooltip text to show on hover.
  final String? tooltip;

  /// The cursor to show when hovering over the widget.
  final MouseCursor cursor;

  /// Whether to show a hover effect.
  final bool hasHoverEffect;

  /// The color to use for the hover effect.
  final Color? hoverColor;

  /// The width of the clickable area. If null, it will size to fit the child.
  final double? width;

  /// The height of the clickable area. If null, it will size to fit the child.
  final double? height;

  /// Whether the clickable area should expand to fill its parent's width.
  final bool isFullWidth;

  /// Whether the clickable area should expand to fill its parent's height.
  final bool isFullHeight;

  /// Whether the widget has a gradient background.
  final bool hasGradient;

  /// The gradient colors if hasGradient is true.
  final List<Color>? gradientColors;

  /// The gradient begin alignment.
  final Alignment gradientBegin;

  /// The gradient end alignment.
  final Alignment gradientEnd;

  /// Whether the widget has a shadow.
  final bool hasShadow;

  /// The shadow color.
  final Color shadowColor;

  /// The shadow offset.
  final Offset shadowOffset;

  /// The shadow blur radius.
  final double shadowBlurRadius;

  /// The shadow spread radius.
  final double shadowSpreadRadius;

  /// The alignment of the child within the clickable area.
  final Alignment alignment;

  /// Whether to show a feedback effect when tapped.
  final bool hasFeedback;

  /// The type of feedback to show when tapped.
  final FeedbackType feedbackType;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Focus node for manual focus control
  ///
  /// This allows for programmatic control of the widget's focus state.
  /// If null, the widget will create and manage its own focus node.
  final FocusNode? focusNode;

  /// Whether the widget should be focused automatically when it appears
  ///
  /// If true, the widget will request focus when it is first built.
  final bool autofocus;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Creates a clickable widget.
  const ClickableWidget({
    super.key,
    required this.child,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.backgroundColor = Colors.transparent,
    this.borderColor,
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.padding = EdgeInsets.zero,
    this.margin = EdgeInsets.zero,
    this.elevation = 0.0,
    this.isDisabled = false,
    this.splashColor,
    this.highlightColor,
    this.hasRippleEffect = true,
    this.tooltip,
    this.cursor = SystemMouseCursors.click,
    this.hasHoverEffect = true,
    this.hoverColor,
    this.width,
    this.height,
    this.isFullWidth = false,
    this.isFullHeight = false,
    this.hasGradient = false,
    this.gradientColors,
    this.gradientBegin = Alignment.centerLeft,
    this.gradientEnd = Alignment.centerRight,
    this.hasShadow = false,
    this.shadowColor = Colors.black,
    this.shadowOffset = const Offset(0, 2),
    this.shadowBlurRadius = 4.0,
    this.shadowSpreadRadius = 0.0,
    this.alignment = Alignment.center,
    this.hasFeedback = true,
    this.feedbackType = FeedbackType.light,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.semanticsLabel,
  });

  /// Creates a ClickableWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the ClickableWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "backgroundColor": "blue",
  ///   "borderRadius": 8.0,
  ///   "hasBorder": true,
  ///   "borderColor": "white",
  ///   "hasRippleEffect": true,
  ///   "tooltip": "Click me"
  /// }
  /// ```
  ///
  /// Helper method to parse a color from a JSON value
  static Color? _parseColor(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle named colors
      switch (colorValue.toLowerCase()) {
        case 'red': return Colors.red;
        case 'blue': return Colors.blue;
        case 'green': return Colors.green;
        case 'yellow': return Colors.yellow;
        case 'orange': return Colors.orange;
        case 'purple': return Colors.purple;
        case 'pink': return Colors.pink;
        case 'brown': return Colors.brown;
        case 'grey':
        case 'gray': return Colors.grey;
        case 'black': return Colors.black;
        case 'white': return Colors.white;
        case 'transparent': return Colors.transparent;
        default:
          // Handle hex colors
          if (colorValue.startsWith('#')) {
            try {
              final hexColor = colorValue.substring(1);
              final hexValue = int.parse('0xFF${hexColor.padRight(8, 'F').substring(0, 8)}');
              return Color(hexValue);
            } catch (e) {
              return null;
            }
          }
          return null;
      }
    } else if (colorValue is int) {
      return Color(colorValue);
    }

    return null;
  }

  /// Helper method to parse a font weight from a JSON value
  static FontWeight _parseFontWeight(dynamic fontWeightValue) {
    if (fontWeightValue == null) return FontWeight.normal;

    if (fontWeightValue is String) {
      switch (fontWeightValue.toLowerCase()) {
        case 'bold': return FontWeight.bold;
        case 'normal': return FontWeight.normal;
        case 'light': return FontWeight.w300;
        case 'thin': return FontWeight.w100;
        case 'medium': return FontWeight.w500;
        case 'semibold': return FontWeight.w600;
        case 'black': return FontWeight.w900;
        default: return FontWeight.normal;
      }
    } else if (fontWeightValue is int) {
      switch (fontWeightValue) {
        case 100: return FontWeight.w100;
        case 200: return FontWeight.w200;
        case 300: return FontWeight.w300;
        case 400: return FontWeight.w400;
        case 500: return FontWeight.w500;
        case 600: return FontWeight.w600;
        case 700: return FontWeight.w700;
        case 800: return FontWeight.w800;
        case 900: return FontWeight.w900;
        default: return FontWeight.normal;
      }
    }

    return FontWeight.normal;
  }

  /// Helper method to parse an icon data from a JSON value
  static IconData? _parseIconData(String? iconName) {
    if (iconName == null) return null;

    switch (iconName.toLowerCase()) {
      case 'touch_app': return Icons.touch_app;
      case 'click': return Icons.mouse;
      case 'tap': return Icons.touch_app;
      case 'finger': return Icons.fingerprint;
      case 'hand': return Icons.back_hand;
      case 'arrow': return Icons.arrow_forward;
      case 'check': return Icons.check;
      case 'close': return Icons.close;
      case 'add': return Icons.add;
      case 'remove': return Icons.remove;
      case 'edit': return Icons.edit;
      case 'delete': return Icons.delete;
      case 'save': return Icons.save;
      case 'settings': return Icons.settings;
      case 'info': return Icons.info;
      case 'warning': return Icons.warning;
      case 'error': return Icons.error;
      case 'help': return Icons.help;
      case 'search': return Icons.search;
      case 'home': return Icons.home;
      case 'menu': return Icons.menu;
      case 'more': return Icons.more_vert;
      case 'share': return Icons.share;
      case 'favorite': return Icons.favorite;
      case 'star': return Icons.star;
      case 'person': return Icons.person;
      case 'account': return Icons.account_circle;
      case 'lock': return Icons.lock;
      case 'unlock': return Icons.lock_open;
      case 'calendar': return Icons.calendar_today;
      case 'time': return Icons.access_time;
      case 'location': return Icons.location_on;
      case 'phone': return Icons.phone;
      case 'email': return Icons.email;
      case 'message': return Icons.message;
      case 'chat': return Icons.chat;
      case 'notification': return Icons.notifications;
      case 'camera': return Icons.camera_alt;
      case 'photo': return Icons.photo;
      case 'video': return Icons.videocam;
      case 'music': return Icons.music_note;
      case 'play': return Icons.play_arrow;
      case 'pause': return Icons.pause;
      case 'stop': return Icons.stop;
      case 'skip': return Icons.skip_next;
      case 'previous': return Icons.skip_previous;
      case 'volume': return Icons.volume_up;
      case 'mute': return Icons.volume_off;
      case 'download': return Icons.download;
      case 'upload': return Icons.upload;
      case 'refresh': return Icons.refresh;
      case 'sync': return Icons.sync;
      case 'cloud': return Icons.cloud;
      case 'wifi': return Icons.wifi;
      case 'bluetooth': return Icons.bluetooth;
      case 'battery': return Icons.battery_full;
      case 'power': return Icons.power_settings_new;
      default: return Icons.touch_app;
    }
  }

  /// Helper method to parse padding from a JSON value
  static EdgeInsetsGeometry _parsePadding(dynamic paddingValue) {
    if (paddingValue == null) return EdgeInsets.zero;

    if (paddingValue is num) {
      return EdgeInsets.all(paddingValue.toDouble());
    } else if (paddingValue is Map<String, dynamic>) {
      final left = (paddingValue['left'] as num?)?.toDouble() ?? 0.0;
      final top = (paddingValue['top'] as num?)?.toDouble() ?? 0.0;
      final right = (paddingValue['right'] as num?)?.toDouble() ?? 0.0;
      final bottom = (paddingValue['bottom'] as num?)?.toDouble() ?? 0.0;

      if (paddingValue.containsKey('horizontal') || paddingValue.containsKey('vertical')) {
        final horizontal = (paddingValue['horizontal'] as num?)?.toDouble() ?? 0.0;
        final vertical = (paddingValue['vertical'] as num?)?.toDouble() ?? 0.0;
        return EdgeInsets.symmetric(horizontal: horizontal, vertical: vertical);
      }

      return EdgeInsets.fromLTRB(left, top, right, bottom);
    } else if (paddingValue is String) {
      switch (paddingValue.toLowerCase()) {
        case 'none':
        case 'zero': return EdgeInsets.zero;
        case 'small': return const EdgeInsets.all(4.0);
        case 'medium': return const EdgeInsets.all(8.0);
        case 'large': return const EdgeInsets.all(16.0);
        case 'xlarge': return const EdgeInsets.all(24.0);
        default: return EdgeInsets.zero;
      }
    }

    return EdgeInsets.zero;
  }

  /// Helper method to parse a cursor from a JSON value
  static MouseCursor _parseCursor(dynamic cursorValue) {
    if (cursorValue == null) return SystemMouseCursors.click;

    if (cursorValue is String) {
      switch (cursorValue.toLowerCase()) {
        case 'click': return SystemMouseCursors.click;
        case 'basic': return SystemMouseCursors.basic;
        case 'text': return SystemMouseCursors.text;
        case 'forbidden': return SystemMouseCursors.forbidden;
        case 'wait': return SystemMouseCursors.wait;
        case 'progress': return SystemMouseCursors.progress;
        case 'help': return SystemMouseCursors.help;
        case 'grab': return SystemMouseCursors.grab;
        case 'grabbing': return SystemMouseCursors.grabbing;
        case 'move': return SystemMouseCursors.move;
        case 'none': return SystemMouseCursors.none;
        case 'resize': return SystemMouseCursors.resizeUpDown;
        case 'resize_up_down': return SystemMouseCursors.resizeUpDown;
        case 'resize_left_right': return SystemMouseCursors.resizeLeftRight;
        case 'resize_up_left_down_right': return SystemMouseCursors.resizeUpLeftDownRight;
        case 'resize_up_right_down_left': return SystemMouseCursors.resizeUpRightDownLeft;
        default: return SystemMouseCursors.click;
      }
    }

    return SystemMouseCursors.click;
  }

  /// Helper method to parse gradient colors from a JSON value
  static List<Color>? _parseGradientColors(dynamic gradientColorsValue) {
    if (gradientColorsValue == null) return null;

    if (gradientColorsValue is List) {
      final colors = <Color>[];
      for (final colorValue in gradientColorsValue) {
        final color = _parseColor(colorValue);
        if (color != null) {
          colors.add(color);
        }
      }
      return colors.isNotEmpty ? colors : null;
    }

    return null;
  }

  /// Helper method to parse an alignment from a JSON value
  static Alignment? _parseAlignment(dynamic alignmentValue) {
    if (alignmentValue == null) return null;

    if (alignmentValue is String) {
      switch (alignmentValue.toLowerCase()) {
        case 'center': return Alignment.center;
        case 'top_left': return Alignment.topLeft;
        case 'top_center': return Alignment.topCenter;
        case 'top_right': return Alignment.topRight;
        case 'center_left': return Alignment.centerLeft;
        case 'center_right': return Alignment.centerRight;
        case 'bottom_left': return Alignment.bottomLeft;
        case 'bottom_center': return Alignment.bottomCenter;
        case 'bottom_right': return Alignment.bottomRight;
        default: return null;
      }
    } else if (alignmentValue is Map<String, dynamic>) {
      final x = (alignmentValue['x'] as num?)?.toDouble() ?? 0.0;
      final y = (alignmentValue['y'] as num?)?.toDouble() ?? 0.0;
      return Alignment(x, y);
    }

    return null;
  }

  /// Helper method to parse an offset from a JSON value
  static Offset? _parseOffset(dynamic offsetValue) {
    if (offsetValue == null) return null;

    if (offsetValue is Map<String, dynamic>) {
      final dx = (offsetValue['dx'] as num?)?.toDouble() ?? 0.0;
      final dy = (offsetValue['dy'] as num?)?.toDouble() ?? 0.0;
      return Offset(dx, dy);
    }

    return null;
  }

  /// Helper method to parse a feedback type from a JSON value
  static FeedbackType _parseFeedbackType(dynamic feedbackTypeValue) {
    if (feedbackTypeValue == null) return FeedbackType.light;

    if (feedbackTypeValue is String) {
      switch (feedbackTypeValue.toLowerCase()) {
        case 'light': return FeedbackType.light;
        case 'medium': return FeedbackType.medium;
        case 'heavy': return FeedbackType.heavy;
        case 'selection': return FeedbackType.selection;
        default: return FeedbackType.light;
      }
    }

    return FeedbackType.light;
  }
  /// Converts the ClickableWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'backgroundColor': _colorToString(backgroundColor),
      'borderRadius': borderRadius,
      'hasRippleEffect': hasRippleEffect,
      'isDisabled': isDisabled,
      'hasHoverEffect': hasHoverEffect,
      'isFullWidth': isFullWidth,
      'isFullHeight': isFullHeight,
      'hasGradient': hasGradient,
      'hasShadow': hasShadow,
      'hasFeedback': hasFeedback,
      'feedbackType': _feedbackTypeToString(feedbackType),
      'autofocus': autofocus,
      'borderWidth': borderWidth,
      'elevation': elevation,
      'shadowBlurRadius': shadowBlurRadius,
      'shadowSpreadRadius': shadowSpreadRadius,
    };

    // Add optional properties
    if (borderColor != null) json['borderColor'] = _colorToString(borderColor!);
    if (splashColor != null) json['splashColor'] = _colorToString(splashColor!);
    if (highlightColor != null) json['highlightColor'] = _colorToString(highlightColor!);
    if (hoverColor != null) json['hoverColor'] = _colorToString(hoverColor!);
    if (tooltip != null) json['tooltip'] = tooltip;
    if (semanticsLabel != null) json['semanticsLabel'] = semanticsLabel;
    if (width != null) json['width'] = width;
    if (height != null) json['height'] = height;
    if (shadowColor != Colors.black) json['shadowColor'] = _colorToString(shadowColor);

    // Add cursor
    if (cursor != SystemMouseCursors.click) {
      if (cursor == SystemMouseCursors.basic) {
        json['cursor'] = 'basic';
      } else if (cursor == SystemMouseCursors.text) json['cursor'] = 'text';
      else if (cursor == SystemMouseCursors.forbidden) json['cursor'] = 'forbidden';
      else if (cursor == SystemMouseCursors.wait) json['cursor'] = 'wait';
      else if (cursor == SystemMouseCursors.progress) json['cursor'] = 'progress';
      else if (cursor == SystemMouseCursors.help) json['cursor'] = 'help';
      else if (cursor == SystemMouseCursors.grab) json['cursor'] = 'grab';
      else if (cursor == SystemMouseCursors.grabbing) json['cursor'] = 'grabbing';
      else if (cursor == SystemMouseCursors.move) json['cursor'] = 'move';
      else if (cursor == SystemMouseCursors.none) json['cursor'] = 'none';
      else if (cursor == SystemMouseCursors.resizeUpDown) json['cursor'] = 'resize_up_down';
      else if (cursor == SystemMouseCursors.resizeLeftRight) json['cursor'] = 'resize_left_right';
      else if (cursor == SystemMouseCursors.resizeUpLeftDownRight) json['cursor'] = 'resize_up_left_down_right';
      else if (cursor == SystemMouseCursors.resizeUpRightDownLeft) json['cursor'] = 'resize_up_right_down_left';
    }

    // Add alignment
    if (alignment != Alignment.center) {
      if (alignment == Alignment.topLeft) {
        json['alignment'] = 'top_left';
      } else if (alignment == Alignment.topCenter) json['alignment'] = 'top_center';
      else if (alignment == Alignment.topRight) json['alignment'] = 'top_right';
      else if (alignment == Alignment.centerLeft) json['alignment'] = 'center_left';
      else if (alignment == Alignment.centerRight) json['alignment'] = 'center_right';
      else if (alignment == Alignment.bottomLeft) json['alignment'] = 'bottom_left';
      else if (alignment == Alignment.bottomCenter) json['alignment'] = 'bottom_center';
      else if (alignment == Alignment.bottomRight) json['alignment'] = 'bottom_right';
      else json['alignment'] = {'x': alignment.x, 'y': alignment.y};
    }

    // Add gradient properties
    if (hasGradient && gradientColors != null && gradientColors!.length >= 2) {
      json['gradientColors'] = gradientColors!.map((color) => _colorToString(color)).toList();

      if (gradientBegin != Alignment.centerLeft) {
        if (gradientBegin == Alignment.topLeft) {
          json['gradientBegin'] = 'top_left';
        } else if (gradientBegin == Alignment.topCenter) json['gradientBegin'] = 'top_center';
        else if (gradientBegin == Alignment.topRight) json['gradientBegin'] = 'top_right';
        else if (gradientBegin == Alignment.centerRight) json['gradientBegin'] = 'center_right';
        else if (gradientBegin == Alignment.bottomRight) json['gradientBegin'] = 'bottom_right';
        else if (gradientBegin == Alignment.bottomCenter) json['gradientBegin'] = 'bottom_center';
        else if (gradientBegin == Alignment.bottomLeft) json['gradientBegin'] = 'bottom_left';
        else if (gradientBegin == Alignment.center) json['gradientBegin'] = 'center';
        else json['gradientBegin'] = {'x': gradientBegin.x, 'y': gradientBegin.y};
      }

      if (gradientEnd != Alignment.centerRight) {
        if (gradientEnd == Alignment.topLeft) {
          json['gradientEnd'] = 'top_left';
        } else if (gradientEnd == Alignment.topCenter) json['gradientEnd'] = 'top_center';
        else if (gradientEnd == Alignment.topRight) json['gradientEnd'] = 'top_right';
        else if (gradientEnd == Alignment.centerLeft) json['gradientEnd'] = 'center_left';
        else if (gradientEnd == Alignment.bottomRight) json['gradientEnd'] = 'bottom_right';
        else if (gradientEnd == Alignment.bottomCenter) json['gradientEnd'] = 'bottom_center';
        else if (gradientEnd == Alignment.bottomLeft) json['gradientEnd'] = 'bottom_left';
        else if (gradientEnd == Alignment.center) json['gradientEnd'] = 'center';
        else json['gradientEnd'] = {'x': gradientEnd.x, 'y': gradientEnd.y};
      }
    }

    // Add shadow offset
    if (shadowOffset != const Offset(0, 2)) {
      json['shadowOffset'] = {'dx': shadowOffset.dx, 'dy': shadowOffset.dy};
    }

    // Add padding and margin
    if (padding != EdgeInsets.zero) {
      if (padding is EdgeInsets) {
        final EdgeInsets p = padding as EdgeInsets;
        if (p.left == p.top && p.left == p.right && p.left == p.bottom) {
          json['padding'] = p.left;
        } else {
          json['padding'] = {
            'left': p.left,
            'top': p.top,
            'right': p.right,
            'bottom': p.bottom,
          };
        }
      }
    }

    if (margin != EdgeInsets.zero) {
      if (margin is EdgeInsets) {
        final EdgeInsets m = margin as EdgeInsets;
        if (m.left == m.top && m.left == m.right && m.left == m.bottom) {
          json['margin'] = m.left;
        } else {
          json['margin'] = {
            'left': m.left,
            'top': m.top,
            'right': m.right,
            'bottom': m.bottom,
          };
        }
      }
    }

    // Add callback flags
    if (onTap != null) json['onTap'] = true;
    if (onDoubleTap != null) json['onDoubleTap'] = true;
    if (onLongPress != null) json['onLongPress'] = true;
    if (onFocus != null) json['onFocus'] = true;

    // Add child widget
    if (child is Text) {
      final Text textChild = child as Text;
      final Map<String, dynamic> childJson = {
        'type': 'text',
        'text': textChild.data,
      };

      if (textChild.style != null) {
        if (textChild.style!.color != null) {
          childJson['textColor'] = _colorToString(textChild.style!.color!);
        }

        if (textChild.style!.fontSize != null) {
          childJson['fontSize'] = textChild.style!.fontSize;
        }

        if (textChild.style!.fontWeight != null) {
          childJson['fontWeight'] = _fontWeightToString(textChild.style!.fontWeight!);
        }
      }

      json['child'] = childJson;
    } else if (child is Icon) {
      final Icon iconChild = child as Icon;
      final Map<String, dynamic> childJson = {
        'type': 'icon',
      };

      // Try to determine icon name
      final String? iconName = _getIconName(iconChild.icon);
      if (iconName != null) {
        childJson['icon'] = iconName;
      }

      if (iconChild.color != null) {
        childJson['iconColor'] = _colorToString(iconChild.color!);
      }

      if (iconChild.size != null) {
        childJson['iconSize'] = iconChild.size;
      }

      json['child'] = childJson;
    } else if (child is Container) {
      final Container containerChild = child as Container;
      final Map<String, dynamic> childJson = {
        'type': 'container',
      };

      if (containerChild.constraints?.minWidth == containerChild.constraints?.maxWidth) {
        childJson['width'] = containerChild.constraints?.maxWidth;
      }

      if (containerChild.constraints?.minHeight == containerChild.constraints?.maxHeight) {
        childJson['height'] = containerChild.constraints?.maxHeight;
      }

      if (containerChild.decoration is BoxDecoration) {
        final BoxDecoration decoration = containerChild.decoration as BoxDecoration;
        if (decoration.color != null) {
          childJson['color'] = _colorToString(decoration.color!);
        }
      }

      if (containerChild.child is Text) {
        childJson['text'] = (containerChild.child as Text).data;
      }

      json['child'] = childJson;
    }

    return json;
  }

  /// Helper method to convert a Color to a string
  static String _colorToString(Color color) {
    // Handle standard colors by name for better readability
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.transparent) return 'transparent';

    // Convert to hex string
    final int colorValue = color.toARGB32();
    final r = ((colorValue >> 16) & 0xFF).toRadixString(16).padLeft(2, '0');
    final g = ((colorValue >> 8) & 0xFF).toRadixString(16).padLeft(2, '0');
    final b = (colorValue & 0xFF).toRadixString(16).padLeft(2, '0');
    return '#$r$g$b';
  }

  /// Helper method to convert a FontWeight to a string
  static String _fontWeightToString(FontWeight weight) {
    if (weight == FontWeight.bold) return 'bold';
    if (weight == FontWeight.normal) return 'normal';
    if (weight == FontWeight.w100) return 'thin';
    if (weight == FontWeight.w300) return 'light';
    if (weight == FontWeight.w500) return 'medium';
    if (weight == FontWeight.w600) return 'semibold';
    if (weight == FontWeight.w900) return 'black';

    return 'normal';
  }

  /// Helper method to convert a FeedbackType to a string
  static String _feedbackTypeToString(FeedbackType type) {
    switch (type) {
      case FeedbackType.light: return 'light';
      case FeedbackType.medium: return 'medium';
      case FeedbackType.heavy: return 'heavy';
      case FeedbackType.selection: return 'selection';
    }
  }

  /// Helper method to get a string name from an IconData
  static String? _getIconName(IconData? icon) {
    if (icon == null) return null;

    if (icon == Icons.touch_app) return 'touch_app';
    if (icon == Icons.mouse) return 'click';
    if (icon == Icons.fingerprint) return 'finger';
    if (icon == Icons.back_hand) return 'hand';
    if (icon == Icons.arrow_forward) return 'arrow';
    if (icon == Icons.check) return 'check';
    if (icon == Icons.close) return 'close';
    if (icon == Icons.add) return 'add';
    if (icon == Icons.remove) return 'remove';
    if (icon == Icons.edit) return 'edit';
    if (icon == Icons.delete) return 'delete';
    if (icon == Icons.save) return 'save';
    if (icon == Icons.settings) return 'settings';
    if (icon == Icons.info) return 'info';
    if (icon == Icons.warning) return 'warning';
    if (icon == Icons.error) return 'error';
    if (icon == Icons.help) return 'help';
    if (icon == Icons.search) return 'search';
    if (icon == Icons.home) return 'home';
    if (icon == Icons.menu) return 'menu';
    if (icon == Icons.more_vert) return 'more';
    if (icon == Icons.share) return 'share';
    if (icon == Icons.favorite) return 'favorite';
    if (icon == Icons.star) return 'star';
    if (icon == Icons.person) return 'person';
    if (icon == Icons.account_circle) return 'account';
    if (icon == Icons.lock) return 'lock';
    if (icon == Icons.lock_open) return 'unlock';
    if (icon == Icons.calendar_today) return 'calendar';
    if (icon == Icons.access_time) return 'time';
    if (icon == Icons.location_on) return 'location';
    if (icon == Icons.phone) return 'phone';
    if (icon == Icons.email) return 'email';
    if (icon == Icons.message) return 'message';
    if (icon == Icons.chat) return 'chat';
    if (icon == Icons.notifications) return 'notification';
    if (icon == Icons.camera_alt) return 'camera';
    if (icon == Icons.photo) return 'photo';
    if (icon == Icons.videocam) return 'video';
    if (icon == Icons.music_note) return 'music';
    if (icon == Icons.play_arrow) return 'play';
    if (icon == Icons.pause) return 'pause';
    if (icon == Icons.stop) return 'stop';
    if (icon == Icons.skip_next) return 'skip';
    if (icon == Icons.skip_previous) return 'previous';
    if (icon == Icons.volume_up) return 'volume';
    if (icon == Icons.volume_off) return 'mute';
    if (icon == Icons.download) return 'download';
    if (icon == Icons.upload) return 'upload';
    if (icon == Icons.refresh) return 'refresh';
    if (icon == Icons.sync) return 'sync';
    if (icon == Icons.cloud) return 'cloud';
    if (icon == Icons.wifi) return 'wifi';
    if (icon == Icons.bluetooth) return 'bluetooth';
    if (icon == Icons.battery_full) return 'battery';
    if (icon == Icons.power_settings_new) return 'power';

    return null;
  }

  factory ClickableWidget.fromJson(Map<String, dynamic> json) {
    // Parse child widget
    Widget childWidget;
    if (json['child'] != null && json['child'] is Map<String, dynamic>) {
      final childJson = json['child'] as Map<String, dynamic>;
      if (childJson['type'] == 'text') {
        childWidget = Text(
          childJson['text'] as String? ?? 'Click me',
          style: TextStyle(
            color: _parseColor(childJson['textColor']),
            fontSize: (childJson['fontSize'] as num?)?.toDouble() ?? 16.0,
            fontWeight: _parseFontWeight(childJson['fontWeight']),
          ),
        );
      } else if (childJson['type'] == 'icon') {
        childWidget = Icon(
          _parseIconData(childJson['icon'] as String?) ?? Icons.touch_app,
          color: _parseColor(childJson['iconColor']),
          size: (childJson['iconSize'] as num?)?.toDouble() ?? 24.0,
        );
      } else if (childJson['type'] == 'container') {
        childWidget = Container(
          width: (childJson['width'] as num?)?.toDouble(),
          height: (childJson['height'] as num?)?.toDouble(),
          color: _parseColor(childJson['color']),
          child: childJson['text'] != null
              ? Text(childJson['text'] as String)
              : null,
        );
      } else {
        // Default to a simple text widget
        childWidget = const Text('Click me');
      }
    } else {
      // Default to a simple text widget
      childWidget = const Text('Click me');
    }

    return ClickableWidget(
      onTap: json['onTap'] == true ? () {
        debugPrint('ClickableWidget tapped');
      } : null,
      onDoubleTap: json['onDoubleTap'] == true ? () {
        debugPrint('ClickableWidget double-tapped');
      } : null,
      onLongPress: json['onLongPress'] == true ? () {
        debugPrint('ClickableWidget long-pressed');
      } : null,
      backgroundColor: _parseColor(json['backgroundColor']) ?? Colors.transparent,
      borderColor: json['borderColor'] != null ? _parseColor(json['borderColor']) : null,
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      padding: _parsePadding(json['padding']),
      margin: _parsePadding(json['margin']),
      elevation: (json['elevation'] as num?)?.toDouble() ?? 0.0,
      isDisabled: json['isDisabled'] as bool? ?? false,
      splashColor: json['splashColor'] != null ? _parseColor(json['splashColor']) : null,
      highlightColor: json['highlightColor'] != null ? _parseColor(json['highlightColor']) : null,
      hasRippleEffect: json['hasRippleEffect'] as bool? ?? true,
      tooltip: json['tooltip'] as String?,
      cursor: _parseCursor(json['cursor']),
      hasHoverEffect: json['hasHoverEffect'] as bool? ?? true,
      hoverColor: json['hoverColor'] != null ? _parseColor(json['hoverColor']) : null,
      width: (json['width'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      isFullWidth: json['isFullWidth'] as bool? ?? false,
      isFullHeight: json['isFullHeight'] as bool? ?? false,
      hasGradient: json['hasGradient'] as bool? ?? false,
      gradientColors: _parseGradientColors(json['gradientColors']),
      gradientBegin: _parseAlignment(json['gradientBegin']) ?? Alignment.centerLeft,
      gradientEnd: _parseAlignment(json['gradientEnd']) ?? Alignment.centerRight,
      hasShadow: json['hasShadow'] as bool? ?? false,
      shadowColor: _parseColor(json['shadowColor']) ?? Colors.black,
      shadowOffset: _parseOffset(json['shadowOffset']) ?? const Offset(0, 2),
      shadowBlurRadius: (json['shadowBlurRadius'] as num?)?.toDouble() ?? 4.0,
      shadowSpreadRadius: (json['shadowSpreadRadius'] as num?)?.toDouble() ?? 0.0,
      alignment: _parseAlignment(json['alignment']) ?? Alignment.center,
      hasFeedback: json['hasFeedback'] as bool? ?? true,
      feedbackType: _parseFeedbackType(json['feedbackType']),
      onFocus: json['onFocus'] == true ? (isFocused) {
        debugPrint('ClickableWidget focus changed: $isFocused');
      } : null,
      focusNode: null, // Cannot be created from JSON
      autofocus: json['autofocus'] as bool? ?? false,
      semanticsLabel: json['semanticsLabel'] as String?,
      child: childWidget,
    );
  }

  @override
  State<ClickableWidget> createState() => _ClickableWidgetState();
}

/// The type of feedback to show when tapped.
enum FeedbackType {
  /// Light feedback (e.g., a subtle click sound).
  light,

  /// Medium feedback (e.g., a more noticeable click sound).
  medium,

  /// Heavy feedback (e.g., a vibration).
  heavy,

  /// Selection feedback (e.g., a selection sound).
  selection,
}

class _ClickableWidgetState extends State<ClickableWidget> {
  bool _isHovered = false;
  bool _isFocused = false;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);

    // Request focus if autofocus is enabled
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    // Clean up focus node
    if (widget.focusNode == null) {
      // Only dispose the focus node if we created it
      _focusNode.removeListener(_handleFocusChange);
      _focusNode.dispose();
    }

    super.dispose();
  }

  // Handle focus changes
  void _handleFocusChange() {
    if (_focusNode.hasFocus != _isFocused) {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });

      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine the background decoration based on configuration
    BoxDecoration decoration;
    if (widget.hasGradient && widget.gradientColors != null && widget.gradientColors!.length >= 2) {
      // Gradient background
      decoration = BoxDecoration(
        gradient: LinearGradient(
          colors: widget.gradientColors!,
          begin: widget.gradientBegin,
          end: widget.gradientEnd,
        ),
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.borderColor != null
            ? Border.all(
                color: widget.borderColor!,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: widget.shadowColor,
                  offset: widget.shadowOffset,
                  blurRadius: widget.shadowBlurRadius,
                  spreadRadius: widget.shadowSpreadRadius,
                ),
              ]
            : null,
      );
    } else {
      // Solid background
      decoration = BoxDecoration(
        color: _isHovered && widget.hasHoverEffect && !widget.isDisabled
            ? (widget.hoverColor ?? widget.backgroundColor.withAlpha((255 * 0.8).round()))
            : widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.borderColor != null
            ? Border.all(
                color: widget.borderColor!,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: widget.shadowColor,
                  offset: widget.shadowOffset,
                  blurRadius: widget.shadowBlurRadius,
                  spreadRadius: widget.shadowSpreadRadius,
                ),
              ]
            : null,
      );
    }

    // Create the clickable content
    Widget clickableContent = Container(
      width: widget.isFullWidth ? double.infinity : widget.width,
      height: widget.isFullHeight ? double.infinity : widget.height,
      padding: widget.padding,
      alignment: widget.alignment,
      child: widget.child,
    );

    // Create the interactive widget
    Widget interactiveWidget = MouseRegion(
      cursor: widget.isDisabled ? SystemMouseCursors.forbidden : widget.cursor,
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: Material(
        color: Colors.transparent,
        elevation: widget.isDisabled ? 0 : widget.elevation,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        child: InkWell(
          onTap: widget.isDisabled
              ? null
              : () {
                  // Request focus when tapped
                  _focusNode.requestFocus();

                  if (widget.onTap != null) {
                    if (widget.hasFeedback) {
                      _provideFeedback();
                    }
                    widget.onTap!();
                  }
                },
          onDoubleTap: widget.isDisabled || widget.onDoubleTap == null
              ? null
              : () {
                  if (widget.hasFeedback) {
                    _provideFeedback();
                  }
                  widget.onDoubleTap!();
                },
          onLongPress: widget.isDisabled || widget.onLongPress == null
              ? null
              : () {
                  if (widget.hasFeedback) {
                    _provideFeedback();
                  }
                  widget.onLongPress!();
                },
          focusNode: _focusNode,
          focusColor: _isFocused ? (widget.hoverColor ?? widget.backgroundColor.withAlpha((255 * 0.8).round())) : null,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          splashColor: widget.hasRippleEffect
              ? (widget.splashColor ?? Theme.of(context).primaryColor.withAlpha((255 * 0.3).round()))
              : Colors.transparent,
          highlightColor: widget.hasRippleEffect
              ? (widget.highlightColor ?? Theme.of(context).primaryColor.withAlpha((255 * 0.1).round()))
              : Colors.transparent,
          child: Ink(
            decoration: decoration,
            child: clickableContent,
          ),
        ),
      ),
    );

    // Add tooltip if needed
    if (widget.tooltip != null) {
      interactiveWidget = Tooltip(
        message: widget.tooltip!,
        child: interactiveWidget,
      );
    }

    // Add semantics if needed
    if (widget.semanticsLabel != null) {
      interactiveWidget = Semantics(
        label: widget.semanticsLabel,
        button: true,
        enabled: !widget.isDisabled,
        child: interactiveWidget,
      );
    }

    // Add margin if needed
    if (widget.margin != EdgeInsets.zero) {
      interactiveWidget = Padding(
        padding: widget.margin,
        child: interactiveWidget,
      );
    }

    return interactiveWidget;
  }

  void _provideFeedback() {
    switch (widget.feedbackType) {
      case FeedbackType.light:
        // Light feedback
        break;
      case FeedbackType.medium:
        // Medium feedback
        break;
      case FeedbackType.heavy:
        // Heavy feedback
        break;
      case FeedbackType.selection:
        // Selection feedback
        break;
    }
  }
}
