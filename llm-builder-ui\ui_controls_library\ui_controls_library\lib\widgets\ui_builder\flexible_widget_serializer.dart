import 'package:flutter/material.dart';
import 'dart:convert';
// import 'package:pie_chart/pie_chart.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../date_range_widget.dart';




/// Runtime Property Discovery Serializer
/// Can serialize any Flutter widget dynamically without custom toJson/fromJson methods
class FlexibleWidgetSerializer {
  
  /// Custom serializers for specific widget types
  static final Map<Type, Function> _customSerializers = {
    
    // Container serialization
    Container: (Container w) => {
      'type': 'Container',
      'width': w.constraints?.maxWidth != double.infinity ? w.constraints?.maxWidth : null,
      'height': w.constraints?.maxHeight != double.infinity ? w.constraints?.maxHeight : null,
      // Only serialize decoration if it exists, otherwise serialize color
      'decoration': w.decoration != null ? _serializeBoxDecoration(w.decoration as BoxDecoration?) : null,
      'color': w.decoration == null && w.color != null ? w.color!.value : null,
      'padding': w.padding != null ? _serializeEdgeInsets(w.padding!) : null,
      'margin': w.margin != null ? _serializeEdgeInsets(w.margin!) : null,
      'child': w.child != null ? serialize(w.child!) : null,
    },
    
    // Text serialization
    Text: (Text w) => {
      'type': 'Text',
      'data': w.data ?? '',
      'style': w.style != null ? {
        'fontSize': w.style!.fontSize,
        'color': w.style!.color?.value,
        'fontWeight': w.style!.fontWeight?.index,
        'fontFamily': w.style!.fontFamily,
      } : null,
      'textAlign': w.textAlign?.index,
      'maxLines': w.maxLines,
      'overflow': w.overflow?.index,
    },
    
    // Column serialization
    Column: (Column w) => {
      'type': 'Column',
      'mainAxisAlignment': w.mainAxisAlignment.index,
      'crossAxisAlignment': w.crossAxisAlignment.index,
      'mainAxisSize': w.mainAxisSize.index,
      'children': w.children.map((child) => serialize(child)).toList(),
    },
    
    // Row serialization
    Row: (Row w) => {
      'type': 'Row',
      'mainAxisAlignment': w.mainAxisAlignment.index,
      'crossAxisAlignment': w.crossAxisAlignment.index,
      'mainAxisSize': w.mainAxisSize.index,
      'children': w.children.map((child) => serialize(child)).toList(),
    },
    
   

    // IconButton serialization
    IconButton: (IconButton w) => {
      'type': 'IconButton',
      'iconSize': w.iconSize,
      'color': w.color?.value,
      'icon': w.icon is Icon ? {
        'type': 'Icon',
        'iconData': (w.icon as Icon).icon?.codePoint,
        'size': (w.icon as Icon).size,
        'color': (w.icon as Icon).color?.value,
      } : null,
      'tooltip': w.tooltip,
    },

    // Icon serialization
    Icon: (Icon w) => {
      'type': 'Icon',
      'iconData': w.icon?.codePoint,
      'size': w.size,
      'color': w.color?.value,
    },

    // Center serialization
    Center: (Center w) => {
      'type': 'Center',
      'child': w.child != null ? serialize(w.child!) : null,
    },

    // Expanded serialization
    Expanded: (Expanded w) => {
      'type': 'Expanded',
      'flex': w.flex,
      'child': serialize(w.child),
    },
    
    // SizedBox serialization
    SizedBox: (SizedBox w) => {
      'type': 'SizedBox',
      'width': w.width,
      'height': w.height,
      'child': w.child != null ? serialize(w.child!) : null,
    },
    
    // Padding serialization
    Padding: (Padding w) => {
      'type': 'Padding',
      'padding': _serializeEdgeInsets(w.padding),
      'child': w.child != null ? serialize(w.child!) : null,
    },

    // Card serialization
    Card: (Card w) => {
      'type': 'Card',
      'elevation': w.elevation,
      'shape': w.shape != null ? _serializeShapeBorder(w.shape!) : null,
      'child': w.child != null ? serialize(w.child!) : null,
    },

    // LayoutBuilder serialization
    LayoutBuilder: (LayoutBuilder w) => {
      'type': 'LayoutBuilder',
      // Note: We can't serialize the builder function, so we'll create a placeholder
      'placeholder': 'LayoutBuilder - requires runtime context',
    },

    // AspectRatio serialization
    AspectRatio: (AspectRatio w) => {
      'type': 'AspectRatio',
      'aspectRatio': w.aspectRatio,
      'child': w.child != null ? serialize(w.child!) : null,
    },

    // SfCircularChart serialization (from syncfusion_flutter_charts package)
    SfCircularChart: (SfCircularChart w) => {
      'type': 'SfCircularChart',
      'title': w.title.text,
      'legend': {
        'isVisible': w.legend.isVisible,
        'overflowMode': w.legend.overflowMode.index,
      },
      'tooltipBehavior': {
        'enable': w.tooltipBehavior?.enable ?? false,
      },
      'series': w.series.map((series) => {
        'type': series.runtimeType.toString(),
        'radius': series is DoughnutSeries ? series.radius : (series is PieSeries ? series.radius : null),
        'innerRadius': series is DoughnutSeries ? series.innerRadius : null,
        'dataSource': series is DoughnutSeries 
          ? (series).dataSource?.map((data) => {
              'category': (data as dynamic).category,
              'value': (data as dynamic).value,
            }).toList()
          : series is PieSeries 
            ? (series).dataSource?.map((data) => {
                'category': (data as dynamic).category,
                'value': (data as dynamic).value,
              }).toList()
            : null,
      }).toList(),
      'seriesType': w.series.isNotEmpty ? w.series.first.runtimeType.toString() : 'DoughnutSeries',
      'chartType': w.series.isNotEmpty ? (w.series.first is PieSeries ? 'disc' : (w.series.first is DoughnutSeries ? 'ring' : 'ring')) : 'ring',
      'annotations': w.annotations?.map((annotation) => {
        'widget': annotation.widget is Text ? {
          'type': 'Text',
          'data': (annotation.widget as Text).data,
          'style': (annotation.widget as Text).style != null ? {
            'fontSize': (annotation.widget as Text).style!.fontSize,
            'fontWeight': (annotation.widget as Text).style!.fontWeight?.index,
          } : null,
        } : null,
      }).toList(),
    },

    // SfCartesianChart serialization (for bubble charts)
    SfCartesianChart: (SfCartesianChart w) => {
      'type': 'SfCartesianChart',
      'legend': {
        'isVisible': w.legend.isVisible,
      },
      'tooltipBehavior': {
        'enable': w.tooltipBehavior?.enable ?? false,
      },
      'series': w.series.map((series) => {
        'type': series.runtimeType.toString(),
        'dataSource': series is BubbleSeries 
          ? (series).dataSource?.map((data) => {
              'x': (data as dynamic).x,
              'y': (data as dynamic).y,
              'size': (data as dynamic).size,
            }).toList()
          : null,
      }).toList(),
      'seriesType': w.series.isNotEmpty ? w.series.first.runtimeType.toString() : 'BubbleSeries',
    },

    // DateRangeWidget serialization
    DateRangeWidget: (DateRangeWidget w) => {
      'type': 'DateRangeWidget',
      'initialStartDate': w.initialStartDate?.millisecondsSinceEpoch,
      'initialEndDate': w.initialEndDate?.millisecondsSinceEpoch,
      'allowDateSelection': w.allowDateSelection,
      'format': w.format.toString().split('.').last,
      'formatPattern': w.formatPattern,
      'showWeekday': w.showWeekday,
      'showYear': w.showYear,
      'showMonth': w.showMonth,
      'showDay': w.showDay,
      'locale': w.locale,
      'textColor': w.textColor.value,
      'backgroundColor': w.backgroundColor.value,
      'fontSize': w.fontSize,
      'fontWeight': w.fontWeight.index,
      'fontFamily': w.fontFamily,
      'textAlign': w.textAlign.index,
      'hasBorder': w.hasBorder,
      'borderRadius': w.borderRadius,
      'borderColor': w.borderColor.value,
      'borderWidth': w.borderWidth,
      'hasShadow': w.hasShadow,
      'elevation': w.elevation,
      'isCompact': w.isCompact,
      'label': w.label,
      'prefix': w.prefix,
      'suffix': w.suffix,
      'showCalendarIcon': w.showCalendarIcon,
      'calendarIcon': w.calendarIcon.codePoint,
      'calendarIconColor': w.calendarIconColor?.value,
      'isDarkTheme': w.isDarkTheme,
      'enabled': w.enabled,
      'readOnly': w.readOnly,
      'minDate': w.minDate?.millisecondsSinceEpoch,
      'maxDate': w.maxDate?.millisecondsSinceEpoch,
      'hoverColor': w.hoverColor?.value,
      'focusColor': w.focusColor?.value,
      'autofocus': w.autofocus,
      'useJsonCallbacks': w.useJsonCallbacks,
      'jsonConfig': w.jsonConfig,
    },
  };
  
  /// Main serialization method - discovers widget type at runtime
  static Map<String, dynamic> serialize(Widget widget) {
    final serializer = _customSerializers[widget.runtimeType];
    
    Map<String, dynamic> base = {
      'runtimeType': widget.runtimeType.toString(),
      'key': widget.key?.toString(),
      'hashCode': widget.hashCode,
    };
    
    if (serializer != null) {
      try {
        final serialized = serializer(widget);
        base.addAll(serialized);
      } catch (e) {
        base['serializationError'] = e.toString();
        base.addAll(_extractBasicProperties(widget));
      }
    } else {
      base['unsupported'] = true;
      base.addAll(_extractBasicProperties(widget));
    }
    
    return base;
  }
  
  /// Fallback property extraction for unsupported widgets
  static Map<String, dynamic> _extractBasicProperties(Widget widget) {
    return {
      'runtimeType': widget.runtimeType.toString(),
      'hashCode': widget.hashCode,
      'toString': widget.toString(),
    };
  }
  
  /// Helper: Serialize EdgeInsets
  static Map<String, dynamic> _serializeEdgeInsets(EdgeInsetsGeometry insets) {
    final resolved = insets.resolve(TextDirection.ltr);
    return {
      'left': resolved.left,
    'top': resolved.top,
    'right': resolved.right,
    'bottom': resolved.bottom,
    };
  }
  
  /// Helper: Serialize BoxDecoration
  static Map<String, dynamic>? _serializeBoxDecoration(BoxDecoration? decoration) {
    if (decoration == null) return null;
    
    return {
      'color': decoration.color?.value,
      'borderRadius': decoration.borderRadius?.toString(),
      'border': decoration.border?.toString(),
      'boxShadow': decoration.boxShadow?.map((shadow) => {
        'color': shadow.color.value,
        'offset': {'dx': shadow.offset.dx, 'dy': shadow.offset.dy},
        'blurRadius': shadow.blurRadius,
        'spreadRadius': shadow.spreadRadius,
      }).toList(),
    };
  }

  /// Helper: Serialize ShapeBorder
  static Map<String, dynamic>? _serializeShapeBorder(ShapeBorder shape) {
    if (shape is RoundedRectangleBorder) {
      return {
        'type': 'RoundedRectangleBorder',
        'borderRadius': shape.borderRadius.toString(),
        'side': shape.side != BorderSide.none ? {
          'color': shape.side.color.value,
          'width': shape.side.width,
        } : null,
      };
    }
    return {
      'type': shape.runtimeType.toString(),
      'data': shape.toString(),
    };
  }
  
  /// Convert to pretty JSON string
  static String toJsonString(Widget widget, {bool prettyPrint = false}) {
    final json = serialize(widget);
    if (prettyPrint) {
      const JsonEncoder encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(json);
    }
    return jsonEncode(json);
  }
  
  /// Deserialize JSON back to Widget (comprehensive support)
  static Widget? deserialize(Map<String, dynamic> json) {
    final type = json['type'];

    switch (type) {
     

      case 'Container':
        // Handle the Flutter constraint: cannot have both color and decoration
        BoxDecoration? decoration;
        Color? color;

        if (json['decoration'] != null) {
          decoration = _parseBoxDecoration(json['decoration']);
        } else if (json['color'] != null) {
          color = Color(json['color']);
        }

        return Container(
          width: json['width']?.toDouble(),
          height: json['height']?.toDouble(),
          color: color,
          decoration: decoration,
          padding: json['padding'] != null ? _parseEdgeInsets(json['padding']) : null,
          margin: json['margin'] != null ? _parseEdgeInsets(json['margin']) : null,
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'Column':
        return Column(
          mainAxisAlignment: _parseMainAxisAlignment(json['mainAxisAlignment']),
          crossAxisAlignment: _parseCrossAxisAlignment(json['crossAxisAlignment']),
          mainAxisSize: _parseMainAxisSize(json['mainAxisSize']),
          children: json['children'] != null
              ? (json['children'] as List).map((child) => deserialize(child) ?? const SizedBox()).toList()
              : [],
        );

      case 'Row':
        return Row(
          mainAxisAlignment: _parseMainAxisAlignment(json['mainAxisAlignment']),
          crossAxisAlignment: _parseCrossAxisAlignment(json['crossAxisAlignment']),
          mainAxisSize: _parseMainAxisSize(json['mainAxisSize']),
          children: json['children'] != null
              ? (json['children'] as List).map((child) => deserialize(child) ?? const SizedBox()).toList()
              : [],
        );

      case 'Expanded':
        return Expanded(
          flex: json['flex'] ?? 1,
          child: json['child'] != null ? deserialize(json['child']) ?? const SizedBox() : const SizedBox(),
        );

      case 'Text':
        final textData = json['data'];
        return Text(
          textData?.toString() ?? '', // Use empty string if data is null
          textAlign: _parseTextAlign(json['textAlign']),
          maxLines: json['maxLines'],
          overflow: _parseTextOverflow(json['overflow']),
          style: json['style'] != null ? _parseTextStyle(json['style']) : null,
        );

      case 'IconButton':
        return IconButton(
          onPressed: () {}, // Placeholder function
          icon: _parseIcon(json['icon']),
          iconSize: json['iconSize']?.toDouble(),
          color: json['color'] != null ? Color(json['color']) : null,
          tooltip: json['tooltip'],
        );

      case 'Icon':
        return _parseIcon(json);

      case 'Center':
        return Center(
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'Padding':
        return Padding(
          padding: _parseEdgeInsets(json['padding']) ?? EdgeInsets.zero,
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'SizedBox':
        return SizedBox(
          width: json['width']?.toDouble(),
          height: json['height']?.toDouble(),
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'Card':
        return Card(
          elevation: json['elevation']?.toDouble() ?? 1.0,
          shape: json['shape'] != null ? _parseShapeBorder(json['shape']) : null,
          child: json['child'] != null ? deserialize(json['child']) : null,
          
        );

      case 'SfCircularChart':
        // Deserialize SfCircularChart with dynamic chart type support
        final chartTypeStr = json['chartType'] as String?;
        final seriesType = json['seriesType'] as String?;
        
        // Default colors if colorList is null
        final defaultColors = [
          const Color(0xFF0D47A1), const Color(0xFF1565C0), const Color(0xFF1976D2), const Color(0xFF1E88E5),
          const Color(0xFF42A5F5), const Color(0xFF64B5F6), const Color(0xFF90CAF9), const Color(0xFFBBDEFB),
        ];

        final colors = defaultColors;

        // Get data from JSON series
        List<_ChartData> chartData = [];
        final seriesList = json['series'] as List?;
        if (seriesList != null && seriesList.isNotEmpty) {
          final firstSeries = seriesList.first as Map<String, dynamic>;
          final dataSource = firstSeries['dataSource'] as List?;
          if (dataSource != null) {
            chartData = dataSource.map((data) => 
              _ChartData(data['category'] as String, (data['value'] as num).toDouble())
            ).toList();
          }
        }
        
        // Fallback to default data if no data found
        if (chartData.isEmpty) {
          chartData = [
            _ChartData('No Data', 1),
          ];
        }

        // Get annotation data from JSON
        List<CircularChartAnnotation> annotations = [];
        final annotationsList = json['annotations'] as List?;
        if (annotationsList != null) {
          for (var annotation in annotationsList) {
            if (annotation != null && annotation['widget'] != null) {
              final widget = annotation['widget'];
              if (widget['type'] == 'Text') {
                annotations.add(
                  CircularChartAnnotation(
                    widget: Text(
                      widget['data'] ?? '',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontWeight: widget['style']?['fontWeight'] != null 
                          ? _parseFontWeight(widget['style']['fontWeight'])
                          : FontWeight.bold,
                        fontSize: widget['style']?['fontSize']?.toDouble() ?? 10,
                      ),
                    ),
                  ),
                );
              }
            }
          }
        }

        // Determine chart type from serialized data
        if (seriesType == 'DoughnutSeries' || chartTypeStr == 'ring') {
          // Build Donut Chart
          return SfCircularChart(
            legend: const Legend(
              isVisible: false,
              overflowMode: LegendItemOverflowMode.wrap,
            ),
            tooltipBehavior: TooltipBehavior(enable: false),
            series: <DoughnutSeries<_ChartData, String>>[
            DoughnutSeries<_ChartData, String>(
              animationDuration: 0,
              dataSource: chartData,
              xValueMapper: (data, _) => data.category,
              yValueMapper: (data, _) => data.value,
              pointColorMapper: (data, index) => colors[index % colors.length],
              dataLabelMapper: (data, _) => '${data.category}: ${data.value}%',
              dataLabelSettings: const DataLabelSettings(isVisible: false),
              radius: chartData.isNotEmpty ? '${_getRadiusFromSeries(json)}%' : '85%',
              innerRadius: chartData.isNotEmpty ? '${_getInnerRadiusFromSeries(json)}%' : '55%',
            )
            ],
            annotations: annotations,
          );
        } else if (seriesType == 'PieSeries' || chartTypeStr == 'disc') {
          // Build Pie Chart
          return SfCircularChart(
            legend: const Legend(
              isVisible: false,
              overflowMode: LegendItemOverflowMode.wrap,
            ),
            tooltipBehavior: TooltipBehavior(enable: false),
            series: <PieSeries<_ChartData, String>>[
              PieSeries<_ChartData, String>(
                animationDuration: 0,
                dataSource: chartData,
                xValueMapper: (data, _) => data.category,
                yValueMapper: (data, _) => data.value,
                pointColorMapper: (data, index) => colors[index % colors.length],
                dataLabelMapper: (data, _) => '${data.category}: ${data.value}%',
                dataLabelSettings: const DataLabelSettings(isVisible: false),
                radius: chartData.isNotEmpty ? '${_getRadiusFromSeries(json)}%' : '85%',
              )
            ],
          );
        } else {
          // Default to Donut Chart
          return SfCircularChart(
            legend: const Legend(
              isVisible: false,
              overflowMode: LegendItemOverflowMode.wrap,
            ),
            tooltipBehavior: TooltipBehavior(enable: false),
            series: <DoughnutSeries<_ChartData, String>>[
              DoughnutSeries<_ChartData, String>(
                dataSource: chartData,
                xValueMapper: (data, _) => data.category,
                yValueMapper: (data, _) => data.value,
                pointColorMapper: (data, index) => colors[index % colors.length],
                dataLabelMapper: (data, _) => '${data.category}: ${data.value}%',
                dataLabelSettings: const DataLabelSettings(isVisible: false),
                 radius: chartData.isNotEmpty ? '${_getRadiusFromSeries(json)}%' : '85%',
                innerRadius: chartData.isNotEmpty ? '${_getInnerRadiusFromSeries(json)}%' : '55%',
              )
            ],
          );
        }

      case 'SfCartesianChart':
        // Deserialize Bubble Chart with actual data from JSON
        final defaultColors = [
          const Color(0xFF0D47A1), const Color(0xFF1565C0), const Color(0xFF1976D2), const Color(0xFF1E88E5),
          const Color(0xFF42A5F5), const Color(0xFF64B5F6), const Color(0xFF90CAF9), const Color(0xFFBBDEFB),
        ];

        // Get bubble data from JSON series
        List<_BubbleChartData> bubbleData = [];
        final seriesList = json['series'] as List?;
        if (seriesList != null && seriesList.isNotEmpty) {
          final firstSeries = seriesList.first as Map<String, dynamic>;
          final dataSource = firstSeries['dataSource'] as List?;
          if (dataSource != null) {
            bubbleData = dataSource.map((data) => 
              _BubbleChartData(
                (data['x'] as num).toDouble(),
                (data['y'] as num).toDouble(),
                (data['size'] as num).toDouble()
              )
            ).toList();
          }
        }
        
        // Fallback to default data if no data found
        if (bubbleData.isEmpty) {
          bubbleData = [
            _BubbleChartData(1, 1, 1), // Minimal fallback
          ];
        }
        
        return SfCartesianChart(
          primaryXAxis: NumericAxis(),
          primaryYAxis: NumericAxis(),
          legend: const Legend(
            isVisible: false,
          ),
          tooltipBehavior: TooltipBehavior(enable: false),
          series: <BubbleSeries<_BubbleChartData, double>>[
            BubbleSeries<_BubbleChartData, double>(
              animationDuration: 0,
              dataSource: bubbleData,
              xValueMapper: (data, _) => data.x,
              yValueMapper: (data, _) => data.y,
              sizeValueMapper: (data, _) => data.size,
              pointColorMapper: (data, index) => defaultColors[index % defaultColors.length],
              dataLabelSettings: const DataLabelSettings(isVisible: false),
            )
          ],
        );
        
        // return PieChart(
        //   dataMap: dataMap?.map((key, value) => MapEntry(key, (value as num).toDouble())) ?? {'Default': 1.0},
        //   colorList: colorList?.map((colorValue) => Color(colorValue as int)).toList() ?? [Colors.blue],
        //   chartType: chartTypeIndex != null ? ChartType.values[chartTypeIndex] : ChartType.ring,
        //   chartRadius: json['chartRadius']?.toDouble() ?? 80.0,
        //   ringStrokeWidth: json['ringStrokeWidth']?.toDouble() ?? 16.0,
        //   centerWidget: json['centerWidget'] != null ? deserialize(json['centerWidget']) : null,
        //   legendOptions: LegendOptions(
        //     showLegends: json['legendOptions']?['showLegends'] ?? false,
        //   ),
        //   chartValuesOptions: ChartValuesOptions(
        //     showChartValues: json['chartValuesOptions']?['showChartValues'] ?? false,
        //   ),
        // );

      case 'AspectRatio':
        return AspectRatio(
          aspectRatio: json['aspectRatio']?.toDouble() ?? 1.0,
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'DateRangeWidget':
        return DateRangeWidget(
          initialStartDate: json['initialStartDate'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['initialStartDate'])
            : null,
          initialEndDate: json['initialEndDate'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['initialEndDate'])
            : null,
          allowDateSelection: json['allowDateSelection'] ?? true,
          format: _parseDateRangeFormat(json['format']),
          formatPattern: json['formatPattern'],
          showWeekday: json['showWeekday'] ?? false,
          showYear: json['showYear'] ?? true,
          showMonth: json['showMonth'] ?? true,
          showDay: json['showDay'] ?? true,
          locale: json['locale'] ?? 'en_US',
          textColor: json['textColor'] != null ? Color(json['textColor']) : Colors.black,
          backgroundColor: json['backgroundColor'] != null ? Color(json['backgroundColor']) : Colors.white,
          fontSize: json['fontSize']?.toDouble() ?? 16.0,
          fontWeight: _parseFontWeight(json['fontWeight']) ?? FontWeight.normal,
          fontFamily: json['fontFamily'],
          textAlign: _parseTextAlign(json['textAlign']) ?? TextAlign.start,
          hasBorder: json['hasBorder'] ?? true,
          borderRadius: json['borderRadius']?.toDouble() ?? 4.0,
          borderColor: json['borderColor'] != null ? Color(json['borderColor']) : const Color(0xFFCCCCCC),
          borderWidth: json['borderWidth']?.toDouble() ?? 1.0,
          hasShadow: json['hasShadow'] ?? false,
          elevation: json['elevation']?.toDouble() ?? 2.0,
          isCompact: json['isCompact'] ?? false,
          label: json['label'],
          prefix: json['prefix'],
          suffix: json['suffix'],
          showCalendarIcon: json['showCalendarIcon'] ?? true,
          calendarIcon: json['calendarIcon'] != null
            ? IconData(json['calendarIcon'], fontFamily: 'MaterialIcons')
            : Icons.calendar_today,
          calendarIconColor: json['calendarIconColor'] != null ? Color(json['calendarIconColor']) : null,
          isDarkTheme: json['isDarkTheme'] ?? false,
          enabled: json['enabled'] ?? true,
          readOnly: json['readOnly'] ?? false,
          minDate: json['minDate'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['minDate'])
            : null,
          maxDate: json['maxDate'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['maxDate'])
            : null,
          hoverColor: json['hoverColor'] != null ? Color(json['hoverColor']) : null,
          focusColor: json['focusColor'] != null ? Color(json['focusColor']) : null,
          autofocus: json['autofocus'] ?? false,
          useJsonCallbacks: json['useJsonCallbacks'] ?? false,
          jsonConfig: json['jsonConfig'],
        );

      default:
        // Unsupported widget type - show placeholder
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            'Unsupported: $type',
            style: const TextStyle(fontSize: 10, color: Colors.grey),
          ),
        );
    }
  }

  /// Deserialize from JSON string
  static Widget? deserializeFromString(String jsonString) {
    try {
      final json = jsonDecode(jsonString);
      return deserialize(json);
    } catch (e) {
      return Container(
        child: Text('Deserialization error: $e'),
      );
    }
  }

  // Parser methods for deserialization
  static EdgeInsets? _parseEdgeInsets(dynamic data) {
    if (data is Map<String, dynamic>) {
      return EdgeInsets.only(
        left: data['left']?.toDouble() ?? 0,
        top: data['top']?.toDouble() ?? 0,
        right: data['right']?.toDouble() ?? 0,
        bottom: data['bottom']?.toDouble() ?? 0,
      );
    }
    return null;
  }

  static BoxDecoration? _parseBoxDecoration(dynamic data) {
    if (data is Map<String, dynamic>) {
      return BoxDecoration(
        color: data['color'] != null ? Color(data['color']) : null,
        borderRadius: data['borderRadius'] != null ? _parseBorderRadius(data['borderRadius']) : null,
        border: data['border'] != null ? _parseBorder(data['border']) : null,
        boxShadow: data['boxShadow'] != null ? _parseBoxShadows(data['boxShadow']) : null,
      );
    }
    return null;
  }

  static BorderRadius? _parseBorderRadius(dynamic data) {
    // For simplicity, return a default border radius
    // In a real implementation, you'd parse the actual BorderRadius data
    return BorderRadius.circular(8.0);
  }

  static Border? _parseBorder(dynamic data) {
    // For simplicity, return a default border
    // In a real implementation, you'd parse the actual Border data
    return Border.all(color: Colors.grey.shade300, width: 1.0);
  }

  static List<BoxShadow>? _parseBoxShadows(dynamic data) {
    if (data is List) {
      return data.map((shadow) {
        if (shadow is Map<String, dynamic>) {
          return BoxShadow(
            color: shadow['color'] != null ? Color(shadow['color']) : Colors.black,
            offset: shadow['offset'] != null
                ? Offset(shadow['offset']['dx']?.toDouble() ?? 0, shadow['offset']['dy']?.toDouble() ?? 0)
                : Offset.zero,
            blurRadius: shadow['blurRadius']?.toDouble() ?? 0,
            spreadRadius: shadow['spreadRadius']?.toDouble() ?? 0,
          );
        }
        return const BoxShadow();
      }).toList();
    }
    return null;
  }

  static MainAxisAlignment _parseMainAxisAlignment(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return MainAxisAlignment.start;
        case 1: return MainAxisAlignment.end;
        case 2: return MainAxisAlignment.center;
        case 3: return MainAxisAlignment.spaceBetween;
        case 4: return MainAxisAlignment.spaceAround;
        case 5: return MainAxisAlignment.spaceEvenly;
        default: return MainAxisAlignment.start;
      }
    }
    return MainAxisAlignment.start;
  }

  static CrossAxisAlignment _parseCrossAxisAlignment(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return CrossAxisAlignment.start;
        case 1: return CrossAxisAlignment.end;
        case 2: return CrossAxisAlignment.center;
        case 3: return CrossAxisAlignment.stretch;
        case 4: return CrossAxisAlignment.baseline;
        default: return CrossAxisAlignment.center;
      }
    }
    return CrossAxisAlignment.center;
  }

  static MainAxisSize _parseMainAxisSize(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return MainAxisSize.min;
        case 1: return MainAxisSize.max;
        default: return MainAxisSize.max;
      }
    }
    return MainAxisSize.max;
  }

  static TextAlign? _parseTextAlign(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return TextAlign.left;
        case 1: return TextAlign.right;
        case 2: return TextAlign.center;
        case 3: return TextAlign.justify;
        case 4: return TextAlign.start;
        case 5: return TextAlign.end;
        default: return null;
      }
    }
    return null;
  }

  static TextOverflow? _parseTextOverflow(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return TextOverflow.clip;
        case 1: return TextOverflow.fade;
        case 2: return TextOverflow.ellipsis;
        case 3: return TextOverflow.visible;
        default: return null;
      }
    }
    return null;
  }

  static TextStyle? _parseTextStyle(dynamic data) {
    if (data is Map<String, dynamic>) {
      return TextStyle(
        fontSize: data['fontSize']?.toDouble(),
        color: data['color'] != null ? Color(data['color']) : null,
        fontWeight: _parseFontWeight(data['fontWeight']),
        fontFamily: data['fontFamily'],
      );
    }
    return null;
  }

  static FontWeight? _parseFontWeight(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return FontWeight.w100;
        case 1: return FontWeight.w200;
        case 2: return FontWeight.w300;
        case 3: return FontWeight.w400;
        case 4: return FontWeight.w500;
        case 5: return FontWeight.w600;
        case 6: return FontWeight.w700;
        case 7: return FontWeight.w800;
        case 8: return FontWeight.w900;
        default: return null;
      }
    }
    return null;
  }

  static Icon _parseIcon(dynamic value) {
    if (value is Map<String, dynamic>) {
      final iconData = value['iconData'];
      if (iconData != null) {
        // Map common icon code points to their respective icons
        switch (iconData) {
          case 58820: // Icons.chevron_left
            return const Icon(Icons.chevron_left);
          case 58821: // Icons.chevron_right
            return const Icon(Icons.chevron_right);
          case 57415: // Icons.calendar_today
            return const Icon(Icons.calendar_today);
          default:
            // Try to create icon from code point
            return Icon(IconData(iconData, fontFamily: 'MaterialIcons'));
        }
      }
    }

    // Fallback for string-based icon detection
    if (value != null && value.toString().contains('chevron_left')) {
      return const Icon(Icons.chevron_left);
    } else if (value != null && value.toString().contains('chevron_right')) {
      return const Icon(Icons.chevron_right);
    }

    return const Icon(Icons.help_outline);
  }

static ShapeBorder? _parseShapeBorder(dynamic data) {
  if (data is Map<String, dynamic>) {
    final type = data['type'];

    if (type == 'RoundedRectangleBorder') {
      // Parse borderRadius: supports "BorderRadius.circular(x)" string or number
      double radius = 0.0;

      final dynamic radiusData = data['borderRadius'];
      if (radiusData is String && radiusData.contains('BorderRadius.circular')) {
        final match = RegExp(r'(\d+(\.\d+)?)').firstMatch(radiusData);
        if (match != null) {
          radius = double.tryParse(match.group(0) ?? '0') ?? 0.0;
        }
      } else if (radiusData is num) {
        radius = radiusData.toDouble();
      }

      // Parse side if available
      BorderSide side = BorderSide.none;
      if (data['side'] is Map<String, dynamic>) {
        final sideData = data['side'];
        final int colorValue = sideData['color'] ?? Colors.grey.value;
        final double width = (sideData['width'] ?? 1.0).toDouble();

        side = BorderSide(color: Color(colorValue), width: width);
      }

      return RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radius),
        side: side,
      );
    }
  }

  return null;
}

  static DateRangeFormat _parseDateRangeFormat(dynamic value) {
    if (value == null) return DateRangeFormat.standard;

    switch (value.toString().toLowerCase()) {
      case 'short':
        return DateRangeFormat.short;
      case 'long':
        return DateRangeFormat.long;
      case 'custom':
        return DateRangeFormat.custom;
      case 'iso':
        return DateRangeFormat.iso;
      case 'standard':
      default:
        return DateRangeFormat.standard;
    }
  }

  // Helper methods to extract radius from series data
  static int _getRadiusFromSeries(Map<String, dynamic> json) {
    final seriesList = json['series'] as List?;
    if (seriesList != null && seriesList.isNotEmpty) {
      final firstSeries = seriesList.first as Map<String, dynamic>;
      final radius = firstSeries['radius'] as String?;
      if (radius != null) {
        // Extract percentage value from string like "85%"
        final match = RegExp(r'(\d+)%').firstMatch(radius);
        if (match != null) {
          return int.tryParse(match.group(1) ?? '85') ?? 85;
        }
      }
    }
    return 85; // Default
  }

  static int _getInnerRadiusFromSeries(Map<String, dynamic> json) {
    final seriesList = json['series'] as List?;
    if (seriesList != null && seriesList.isNotEmpty) {
      final firstSeries = seriesList.first as Map<String, dynamic>;
      final innerRadius = firstSeries['innerRadius'] as String?;
      if (innerRadius != null) {
        // Extract percentage value from string like "55%"
        final match = RegExp(r'(\d+)%').firstMatch(innerRadius);
        if (match != null) {
          return int.tryParse(match.group(1) ?? '55') ?? 55;
        }
      }
    }
    return 55; // Default
  }

  /// Get serialization statistics
  static Map<String, dynamic> getSerializationStats(Widget widget) {
    final json = serialize(widget);
    
    int supportedWidgets = 0;
    int unsupportedWidgets = 0;
    Set<String> widgetTypes = {};
    
    void countWidgets(Map<String, dynamic> data) {
      if (data.containsKey('type')) {
        widgetTypes.add(data['type']);
        if (data.containsKey('unsupported') && data['unsupported'] == true) {
          unsupportedWidgets++;
        } else {
          supportedWidgets++;
        }
      }
      
      // Recursively count children
      if (data.containsKey('children') && data['children'] is List) {
        for (var child in data['children']) {
          if (child is Map<String, dynamic>) {
            countWidgets(child);
          }
        }
      }
      
      if (data.containsKey('child') && data['child'] is Map<String, dynamic>) {
        countWidgets(data['child']);
      }
    }
    
    countWidgets(json);
    
    return {
      'totalWidgets': supportedWidgets + unsupportedWidgets,
      'supportedWidgets': supportedWidgets,
      'unsupportedWidgets': unsupportedWidgets,
      'supportRate': supportedWidgets / (supportedWidgets + unsupportedWidgets) * 100,
      'widgetTypes': widgetTypes.toList(),
      'jsonSize': jsonEncode(json).length,
    };
  }

}
class _ChartData {
  final String category;
  final double value;

  _ChartData(this.category, this.value);
}

class _BubbleChartData {
  final double x;
  final double y;
  final double size;

  _BubbleChartData(this.x, this.y, this.size);
}
