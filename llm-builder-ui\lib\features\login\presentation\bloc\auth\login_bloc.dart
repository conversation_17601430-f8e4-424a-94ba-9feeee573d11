import 'dart:developer';

import 'package:builder_app/features/login/data/models/user_model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/usecases/login_user.dart';
import 'login_event.dart';
import 'login_state.dart';
import 'package:builder_app/core/utils/shared_prefs_helper.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final LoginUser loginUser;

  LoginBloc({required this.loginUser}) : super(LoginInitial()) {
    on<UserLoginEvent>((event, emit) async {
      emit(LoginLoading());
      try {
     UserModel? model=   await loginUser(LoginParams(email: event.email, password: event.password));
        log(model.user!.email.toString());
        // Store user details in local storage
        if (model.accessToken != null) {
          await SharedPrefsHelper().setString('access_token', model.accessToken!);
          print('Saved access_token:  [32m${model.accessToken} [0m');
        }
        if (model.refreshToken != null) {
          await SharedPrefsHelper().setString('refresh_token', model.refreshToken!);
        }
        if (model.user != null) {
          await SharedPrefsHelper().setString('user', userModelToJson(model));
        }
        emit(LoginSuccess(message: model.user!.firstName.toString()));
      } catch (e) {
        log(e.toString());
        emit(LoginFailure(e.toString()));
      }
    });
  }
} 