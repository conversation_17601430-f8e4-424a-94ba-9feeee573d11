import 'package:flutter/material.dart';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension ColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// A widget that displays a metric or key performance indicator (KPI).
///
/// This widget can be used to display various types of metrics with customizable
/// appearance and behavior. It supports showing current values, target values,
/// comparison values, trends, and visual indicators.
class MetricWidget extends StatefulWidget {
  /// The current value of the metric.
  final dynamic value;

  /// The target value for comparison (optional).
  final dynamic targetValue;

  /// The previous or comparison value (optional).
  final dynamic previousValue;

  /// The title or label for the metric.
  final String? title;

  /// The subtitle or description for the metric.
  final String? subtitle;

  /// The unit of measurement (e.g., "$", "%", "units").
  final String? unit;

  /// Whether to show the unit before the value.
  final bool unitOnLeft;

  /// Whether to show a trend indicator (up/down arrow).
  final bool showTrend;

  /// Whether to show the percentage change.
  final bool showPercentageChange;

  /// Whether to show the absolute change.
  final bool showAbsoluteChange;

  /// Whether to show the target value.
  final bool showTarget;

  /// Whether to show the previous value.
  final bool showPrevious;

  /// The color for positive trends or values above target.
  final Color positiveColor;

  /// The color for negative trends or values below target.
  final Color negativeColor;

  /// The color for neutral trends or values at target.
  final Color neutralColor;

  /// The background color of the widget.
  final Color backgroundColor;

  /// The text color for the value.
  final Color valueTextColor;

  /// The text color for the title.
  final Color titleTextColor;

  /// The text color for the subtitle.
  final Color subtitleTextColor;

  /// The font size for the value.
  final double valueFontSize;

  /// The font size for the title.
  final double titleFontSize;

  /// The font size for the subtitle.
  final double subtitleFontSize;

  /// The font weight for the value.
  final FontWeight valueFontWeight;

  /// The font weight for the title.
  final FontWeight titleFontWeight;

  /// The font weight for the subtitle.
  final FontWeight subtitleFontWeight;

  /// The icon to display with the metric.
  final IconData? icon;

  /// The color of the icon.
  final Color? iconColor;

  /// The size of the icon.
  final double iconSize;

  /// Whether to show a border around the widget.
  final bool hasBorder;

  /// The color of the border.
  final Color borderColor;

  /// The width of the border.
  final double borderWidth;

  /// The radius of the border corners.
  final double borderRadius;

  /// Whether to show a shadow under the widget.
  final bool hasShadow;

  /// The elevation of the shadow.
  final double elevation;

  /// The padding inside the widget.
  final EdgeInsetsGeometry padding;

  /// The margin around the widget.
  final EdgeInsetsGeometry margin;

  /// The width of the widget.
  final double? width;

  /// The height of the widget.
  final double? height;

  /// The height of the text field area.
  final double? textFieldHeight;

  /// The layout direction (horizontal or vertical).
  final Axis direction;

  /// Whether to use a compact layout.
  final bool isCompact;

  /// Whether to use a card-like appearance.
  final bool isCard;

  /// The number of decimal places to show for numeric values.
  final int decimalPlaces;

  /// Whether to use thousands separators for numeric values.
  final bool useThousandsSeparator;

  /// The thousands separator character.
  final String thousandsSeparator;

  /// The decimal separator character.
  final String decimalSeparator;

  /// Whether to show a progress indicator.
  final bool showProgress;

  /// The type of progress indicator to show.
  final ProgressType progressType;

  /// The color of the progress indicator.
  final Color progressColor;

  /// The background color of the progress indicator.
  final Color progressBackgroundColor;

  /// The thickness of the progress indicator.
  final double progressThickness;

  /// Whether the widget is interactive (clickable).
  final bool isInteractive;

  /// Callback function when the widget is tapped.
  final VoidCallback? onTap;

  /// Whether to animate changes in the value.
  final bool animateChanges;

  /// The duration of the animation.
  final Duration animationDuration;

  /// The curve of the animation.
  final Curve animationCurve;

  /// Whether to show a tooltip on hover.
  final bool showTooltip;

  /// The text to show in the tooltip.
  final String? tooltipText;

  /// Whether to automatically determine colors based on value comparison.
  final bool autoColor;

  /// Whether to format the value as currency.
  final bool isCurrency;

  /// Whether to format the value as percentage.
  final bool isPercentage;

  /// The currency symbol to use if [isCurrency] is true.
  final String currencySymbol;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is hovered
  final Color? hoverColor;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the widget is long-pressed
  final VoidCallback? onLongPress;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Metric-specific JSON configuration
  /// Whether to use JSON metric configuration
  final bool useJsonMetricConfig;

  /// Metric-specific JSON configuration
  final Map<String, dynamic>? metricConfig;

  /// Creates a metric widget.
  const MetricWidget({
    super.key,
    required this.value,
    this.targetValue,
    this.previousValue,
    this.title,
    this.subtitle,
    this.unit,
    this.unitOnLeft = false,
    this.showTrend = false,
    this.showPercentageChange = false,
    this.showAbsoluteChange = false,
    this.showTarget = false,
    this.showPrevious = false,
    this.positiveColor = Colors.green,
    this.negativeColor = Colors.red,
    this.neutralColor = Colors.grey,
    this.backgroundColor = Colors.transparent,
    this.valueTextColor = Colors.black,
    this.titleTextColor = Colors.black87,
    this.subtitleTextColor = Colors.black54,
    this.valueFontSize = 14.0,
    this.titleFontSize = 12.0,
    this.subtitleFontSize = 10.0,
    this.valueFontWeight = FontWeight.bold,
    this.titleFontWeight = FontWeight.w500,
    this.subtitleFontWeight = FontWeight.normal,
    this.icon,
    this.iconColor,
    this.iconSize = 24.0,
    this.hasBorder = true,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.borderRadius = 6.0,
    this.hasShadow = false,
    this.elevation = 0.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 8.0, vertical: 1.0),
    this.margin = const EdgeInsets.all(0.0),
    this.width,
    this.height,
    this.textFieldHeight,
    this.direction = Axis.vertical,
    this.isCompact = false,
    this.isCard = false,
    this.decimalPlaces = 2,
    this.useThousandsSeparator = true,
    this.thousandsSeparator = ',',
    this.decimalSeparator = '.',
    this.showProgress = false,
    this.progressType = ProgressType.linear,
    this.progressColor = const Color(0xFF0058FF),
    this.progressBackgroundColor = Colors.grey,
    this.progressThickness = 4.0,
    this.isInteractive = false,
    this.onTap,
    this.animateChanges = false,
    this.animationDuration = const Duration(milliseconds: 500),
    this.animationCurve = Curves.easeInOut,
    this.showTooltip = false,
    this.tooltipText,
    this.autoColor = true,
    this.isCurrency = false,
    this.isPercentage = false,
    this.currencySymbol = '\$',
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor = const Color(0xFF0058FF),
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Metric-specific JSON configuration
    this.useJsonMetricConfig = false,
    this.metricConfig,
  });

  /// Creates a MetricWidget from a JSON map
  ///
  /// This factory constructor allows for creating a MetricWidget from a JSON map,
  /// making it easy to configure the widget from API responses or configuration files.
  factory MetricWidget.fromJson(Map<String, dynamic> json) {
    // Parse progress type
    ProgressType progressType = ProgressType.linear;
    if (json.containsKey('progressType')) {
      final String type = json['progressType'].toString().toLowerCase();
      if (type == 'circular') {
        progressType = ProgressType.circular;
      } else if (type == 'radial') {
        progressType = ProgressType.radial;
      }
    }

    // Parse direction
    Axis direction = Axis.vertical;
    if (json.containsKey('direction')) {
      final String dir = json['direction'].toString().toLowerCase();
      if (dir == 'horizontal') {
        direction = Axis.horizontal;
      }
    }

    // Parse colors
    Color positiveColor = Colors.green;
    if (json.containsKey('positiveColor')) {
      positiveColor = _parseColor(json['positiveColor']);
    }

    Color negativeColor = Colors.red;
    if (json.containsKey('negativeColor')) {
      negativeColor = _parseColor(json['negativeColor']);
    }

    Color neutralColor = Colors.grey;
    if (json.containsKey('neutralColor')) {
      neutralColor = _parseColor(json['neutralColor']);
    }

    Color backgroundColor = Colors.transparent;
    if (json.containsKey('backgroundColor')) {
      backgroundColor = _parseColor(json['backgroundColor']);
    }

    Color valueTextColor = Colors.black;
    if (json.containsKey('valueTextColor')) {
      valueTextColor = _parseColor(json['valueTextColor']);
    }

    Color titleTextColor = Colors.black87;
    if (json.containsKey('titleTextColor')) {
      titleTextColor = _parseColor(json['titleTextColor']);
    }

    Color subtitleTextColor = Colors.black54;
    if (json.containsKey('subtitleTextColor')) {
      subtitleTextColor = _parseColor(json['subtitleTextColor']);
    }

    Color? iconColor;
    if (json.containsKey('iconColor')) {
      iconColor = _parseColor(json['iconColor']);
    }

    Color borderColor = Colors.grey.shade300;
    if (json.containsKey('borderColor')) {
      borderColor = _parseColor(json['borderColor']);
    }

    Color progressColor = Color(0xFF0058FF);
    if (json.containsKey('progressColor')) {
      progressColor = _parseColor(json['progressColor']);
    }

    Color progressBackgroundColor = Colors.grey;
    if (json.containsKey('progressBackgroundColor')) {
      progressBackgroundColor = _parseColor(json['progressBackgroundColor']);
    }

    Color? hoverColor;
    if (json.containsKey('hoverColor')) {
      hoverColor = _parseColor(json['hoverColor']);
    }

    Color? focusColor;
    if (json.containsKey('focusColor')) {
      focusColor = _parseColor(json['focusColor']);
    }

    // Parse font weights
    FontWeight valueFontWeight = FontWeight.bold;
    if (json.containsKey('valueFontWeight')) {
      valueFontWeight = _parseFontWeight(json['valueFontWeight']);
    }

    FontWeight titleFontWeight = FontWeight.w500;
    if (json.containsKey('titleFontWeight')) {
      titleFontWeight = _parseFontWeight(json['titleFontWeight']);
    }

    FontWeight subtitleFontWeight = FontWeight.normal;
    if (json.containsKey('subtitleFontWeight')) {
      subtitleFontWeight = _parseFontWeight(json['subtitleFontWeight']);
    }

    // Parse icon
    IconData? icon;
    if (json.containsKey('icon')) {
      icon = _parseIconData(json['icon']);
    }

    // Parse padding and margin
    EdgeInsetsGeometry padding = const EdgeInsets.symmetric(
      horizontal: 8.0,
      vertical: 1.0,
    );
    if (json.containsKey('padding')) {
      padding = _parseEdgeInsets(json['padding']);
    }

    EdgeInsetsGeometry margin = const EdgeInsets.all(0.0);
    if (json.containsKey('margin')) {
      margin = _parseEdgeInsets(json['margin']);
    }

    // Parse animation properties
    Duration animationDuration = const Duration(milliseconds: 500);
    if (json.containsKey('animationDuration')) {
      animationDuration = Duration(
        milliseconds: json['animationDuration'] as int? ?? 500,
      );
    }

    Curve animationCurve = Curves.easeInOut;
    if (json.containsKey('animationCurve')) {
      final String curve = json['animationCurve'].toString().toLowerCase();
      if (curve == 'linear') {
        animationCurve = Curves.linear;
      } else if (curve == 'decelerate') {
        animationCurve = Curves.decelerate;
      } else if (curve == 'ease') {
        animationCurve = Curves.ease;
      } else if (curve == 'easein') {
        animationCurve = Curves.easeIn;
      } else if (curve == 'easeout') {
        animationCurve = Curves.easeOut;
      } else if (curve == 'elasticin') {
        animationCurve = Curves.elasticIn;
      } else if (curve == 'elasticout') {
        animationCurve = Curves.elasticOut;
      } else if (curve == 'elasticinout') {
        animationCurve = Curves.elasticInOut;
      }
    }

    return MetricWidget(
      // Basic properties
      value: json['value'] != null ? (json['value'] as num).toDouble() : 0.0,
      targetValue:
          json['targetValue'] != null
              ? (json['targetValue'] as num).toDouble()
              : null,
      previousValue:
          json['previousValue'] != null
              ? (json['previousValue'] as num).toDouble()
              : null,
      title: json['title'] as String?,
      subtitle: json['subtitle'] as String?,
      unit: json['unit'] as String?,
      unitOnLeft: json['unitOnLeft'] as bool? ?? false,

      // Display properties
      showTrend: json['showTrend'] as bool? ?? false,
      showPercentageChange: json['showPercentageChange'] as bool? ?? false,
      showAbsoluteChange: json['showAbsoluteChange'] as bool? ?? false,
      showTarget: json['showTarget'] as bool? ?? false,
      showPrevious: json['showPrevious'] as bool? ?? false,
      direction: direction,
      isCompact: json['isCompact'] as bool? ?? false,
      isCard: json['isCard'] as bool? ?? false,

      // Formatting properties
      decimalPlaces: json['decimalPlaces'] as int? ?? 2,
      useThousandsSeparator: json['useThousandsSeparator'] as bool? ?? true,
      thousandsSeparator: json['thousandsSeparator'] as String? ?? ',',
      decimalSeparator: json['decimalSeparator'] as String? ?? '.',
      isCurrency: json['isCurrency'] as bool? ?? false,
      isPercentage: json['isPercentage'] as bool? ?? false,
      currencySymbol: json['currencySymbol'] as String? ?? '\$',

      // Style properties
      positiveColor: positiveColor,
      negativeColor: negativeColor,
      neutralColor: neutralColor,
      backgroundColor: backgroundColor,
      valueTextColor: valueTextColor,
      titleTextColor: titleTextColor,
      subtitleTextColor: subtitleTextColor,
      valueFontSize:
          json['valueFontSize'] != null
              ? (json['valueFontSize'] as num).toDouble()
              : 14.0,
      titleFontSize:
          json['titleFontSize'] != null
              ? (json['titleFontSize'] as num).toDouble()
              : 12.0,
      subtitleFontSize:
          json['subtitleFontSize'] != null
              ? (json['subtitleFontSize'] as num).toDouble()
              : 10.0,
      valueFontWeight: valueFontWeight,
      titleFontWeight: titleFontWeight,
      subtitleFontWeight: subtitleFontWeight,

      // Icon properties
      icon: icon,
      iconColor: iconColor,
      iconSize:
          json['iconSize'] != null
              ? (json['iconSize'] as num).toDouble()
              : 24.0,

      // Container properties
      hasBorder: json['hasBorder'] as bool? ?? false,
      borderColor: borderColor,
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 1.0,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 8.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 2.0,
      padding: padding,
      margin: margin,
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height:
          json['height'] != null ? (json['height'] as num).toDouble() : null,
      textFieldHeight:
          json['textFieldHeight'] != null
              ? (json['textFieldHeight'] as num).toDouble()
              : null,

      // Progress properties
      showProgress: json['showProgress'] as bool? ?? false,
      progressType: progressType,
      progressColor: progressColor,
      progressBackgroundColor: progressBackgroundColor,
      progressThickness:
          json['progressThickness'] != null
              ? (json['progressThickness'] as num).toDouble()
              : 4.0,

      // Interaction properties
      isInteractive: json['isInteractive'] as bool? ?? false,
      onTap: json.containsKey('onTap') ? () {} : null,

      // Animation properties
      animateChanges: json['animateChanges'] as bool? ?? false,
      animationDuration: animationDuration,
      animationCurve: animationCurve,

      // Tooltip properties
      showTooltip: json['showTooltip'] as bool? ?? false,
      tooltipText: json['tooltipText'] as String?,

      // Color properties
      autoColor: json['autoColor'] as bool? ?? true,

      // Advanced interaction properties
      onHover: json.containsKey('onHover') ? (_) {} : null,
      onFocus: json.containsKey('onFocus') ? (_) {} : null,
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: hoverColor,
      focusColor: focusColor,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onDoubleTap: json.containsKey('onDoubleTap') ? () {} : null,
      onLongPress: json.containsKey('onLongPress') ? () {} : null,

      // Accessibility properties
      semanticsLabel: json['semanticsLabel'] as String?,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,

      // JSON configuration properties
      jsonCallbacks:
          json.containsKey('callbacks')
              ? json['callbacks'] as Map<String, dynamic>
              : null,
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      callbackState:
          json.containsKey('callbackState')
              ? json['callbackState'] as Map<String, dynamic>
              : null,
      jsonConfig: json,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      useJsonMetricConfig: json['useJsonMetricConfig'] as bool? ?? false,
      metricConfig:
          json.containsKey('metricConfig')
              ? json['metricConfig'] as Map<String, dynamic>
              : null,
    );
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Color(0xFF0058FF);
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.transparent;
          default:
            return Colors.blue;
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a =
          colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return const Color(0xFF0058FF); // Default color
  }

  /// Parses edge insets from a map or number
  static EdgeInsetsGeometry _parseEdgeInsets(dynamic value) {
    if (value is num) {
      return EdgeInsets.all(value.toDouble());
    } else if (value is Map) {
      if (value.containsKey('all')) {
        return EdgeInsets.all((value['all'] as num).toDouble());
      } else if (value.containsKey('horizontal') ||
          value.containsKey('vertical')) {
        return EdgeInsets.symmetric(
          horizontal:
              value.containsKey('horizontal')
                  ? (value['horizontal'] as num).toDouble()
                  : 0.0,
          vertical:
              value.containsKey('vertical')
                  ? (value['vertical'] as num).toDouble()
                  : 0.0,
        );
      } else {
        return EdgeInsets.fromLTRB(
          value.containsKey('left') ? (value['left'] as num).toDouble() : 0.0,
          value.containsKey('top') ? (value['top'] as num).toDouble() : 0.0,
          value.containsKey('right') ? (value['right'] as num).toDouble() : 0.0,
          value.containsKey('bottom')
              ? (value['bottom'] as num).toDouble()
              : 0.0,
        );
      }
    }
    return const EdgeInsets.all(0.0); // Default padding
  }

  /// Parses font weight from a string or number
  static FontWeight _parseFontWeight(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'thin':
          return FontWeight.w100;
        case 'extralight':
          return FontWeight.w200;
        case 'light':
          return FontWeight.w300;
        case 'regular':
          return FontWeight.w400;
        case 'medium':
          return FontWeight.w500;
        case 'semibold':
          return FontWeight.w600;
        case 'bold':
          return FontWeight.w700;
        case 'extrabold':
          return FontWeight.w800;
        case 'black':
          return FontWeight.w900;
        default:
          return FontWeight.normal;
      }
    } else if (value is int) {
      switch (value) {
        case 100:
          return FontWeight.w100;
        case 200:
          return FontWeight.w200;
        case 300:
          return FontWeight.w300;
        case 400:
          return FontWeight.w400;
        case 500:
          return FontWeight.w500;
        case 600:
          return FontWeight.w600;
        case 700:
          return FontWeight.w700;
        case 800:
          return FontWeight.w800;
        case 900:
          return FontWeight.w900;
        default:
          return FontWeight.normal;
      }
    }
    return FontWeight.normal; // Default font weight
  }

  /// Parses icon data from a string
  static IconData? _parseIconData(String value) {
    switch (value.toLowerCase()) {
      case 'trending_up':
        return Icons.trending_up;
      case 'trending_down':
        return Icons.trending_down;
      case 'trending_flat':
        return Icons.trending_flat;
      case 'arrow_upward':
        return Icons.arrow_upward;
      case 'arrow_downward':
        return Icons.arrow_downward;
      case 'arrow_forward':
        return Icons.arrow_forward;
      case 'arrow_back':
        return Icons.arrow_back;
      case 'check':
        return Icons.check;
      case 'close':
        return Icons.close;
      case 'warning':
        return Icons.warning;
      case 'error':
        return Icons.error;
      case 'info':
        return Icons.info;
      case 'help':
        return Icons.help;
      case 'star':
        return Icons.star;
      case 'favorite':
        return Icons.favorite;
      case 'thumb_up':
        return Icons.thumb_up;
      case 'thumb_down':
        return Icons.thumb_down;
      case 'attach_money':
        return Icons.attach_money;
      case 'percent':
        return Icons.percent;
      case 'show_chart':
        return Icons.show_chart;
      case 'pie_chart':
        return Icons.pie_chart;
      case 'bar_chart':
        return Icons.bar_chart;
      case 'timeline':
        return Icons.timeline;
      case 'data_usage':
        return Icons.data_usage;
      case 'assessment':
        return Icons.assessment;
      case 'analytics':
        return Icons.analytics;
      case 'insights':
        return Icons.insights;
      case 'speed':
        return Icons.speed;
      case 'timer':
        return Icons.timer;
      case 'date_range':
        return Icons.date_range;
      case 'calendar_today':
        return Icons.calendar_today;
      case 'people':
        return Icons.people;
      case 'person':
        return Icons.person;
      case 'group':
        return Icons.group;
      case 'business':
        return Icons.business;
      case 'work':
        return Icons.work;
      case 'home':
        return Icons.home;
      case 'settings':
        return Icons.settings;
      case 'build':
        return Icons.build;
      case 'dashboard':
        return Icons.dashboard;
      case 'visibility':
        return Icons.visibility;
      case 'visibility_off':
        return Icons.visibility_off;
      case 'lock':
        return Icons.lock;
      case 'lock_open':
        return Icons.lock_open;
      case 'security':
        return Icons.security;
      case 'verified':
        return Icons.verified;
      case 'verified_user':
        return Icons.verified_user;
      case 'shopping_cart':
        return Icons.shopping_cart;
      case 'shopping_bag':
        return Icons.shopping_bag;
      case 'local_shipping':
        return Icons.local_shipping;
      case 'inventory':
        return Icons.inventory;
      case 'receipt':
        return Icons.receipt;
      case 'payment':
        return Icons.payment;
      case 'credit_card':
        return Icons.credit_card;
      case 'account_balance':
        return Icons.account_balance;
      case 'account_balance_wallet':
        return Icons.account_balance_wallet;
      case 'savings':
        return Icons.savings;
      case 'currency_exchange':
        return Icons.currency_exchange;
      default:
        return null;
    }
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Basic properties
      'value': value,
      'targetValue': targetValue,
      'previousValue': previousValue,
      'title': title,
      'subtitle': subtitle,
      'unit': unit,
      'unitOnLeft': unitOnLeft,

      // Display properties
      'showTrend': showTrend,
      'showPercentageChange': showPercentageChange,
      'showAbsoluteChange': showAbsoluteChange,
      'showTarget': showTarget,
      'showPrevious': showPrevious,
      'direction': direction.toString().split('.').last,
      'isCompact': isCompact,
      'isCard': isCard,

      // Formatting properties
      'decimalPlaces': decimalPlaces,
      'useThousandsSeparator': useThousandsSeparator,
      'thousandsSeparator': thousandsSeparator,
      'decimalSeparator': decimalSeparator,
      'isCurrency': isCurrency,
      'isPercentage': isPercentage,
      'currencySymbol': currencySymbol,

      // Style properties
      'positiveColor': '#${positiveColor.toHexString()}',
      'negativeColor': '#${negativeColor.toHexString()}',
      'neutralColor': '#${neutralColor.toHexString()}',
      'backgroundColor': '#${backgroundColor.toHexString()}',
      'valueTextColor': '#${valueTextColor.toHexString()}',
      'titleTextColor': '#${titleTextColor.toHexString()}',
      'subtitleTextColor': '#${subtitleTextColor.toHexString()}',
      'valueFontSize': valueFontSize,
      'titleFontSize': titleFontSize,
      'subtitleFontSize': subtitleFontSize,
      'valueFontWeight': valueFontWeight.index * 100 + 100,
      'titleFontWeight': titleFontWeight.index * 100 + 100,
      'subtitleFontWeight': subtitleFontWeight.index * 100 + 100,

      // Icon properties
      'iconSize': iconSize,
      'iconColor': iconColor != null ? '#${iconColor!.toHexString()}' : null,

      // Container properties
      'hasBorder': hasBorder,
      'borderColor': '#${borderColor.toHexString()}',
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'width': width,
      'height': height,

      // Progress properties
      'showProgress': showProgress,
      'progressType': progressType.toString().split('.').last,
      'progressColor': '#${progressColor.toHexString()}',
      'progressBackgroundColor': '#${progressBackgroundColor.toHexString()}',
      'progressThickness': progressThickness,

      // Interaction properties
      'isInteractive': isInteractive,

      // Animation properties
      'animateChanges': animateChanges,
      'animationDuration': animationDuration.inMilliseconds,

      // Tooltip properties
      'showTooltip': showTooltip,
      'tooltipText': tooltipText,

      // Color properties
      'autoColor': autoColor,

      // Advanced interaction properties
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
      'hoverColor': hoverColor != null ? '#${hoverColor!.toHexString()}' : null,
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration properties
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonMetricConfig': useJsonMetricConfig,
    };
  }

  @override
  State<MetricWidget> createState() => _MetricWidgetState();
}

/// The type of progress indicator to show.
enum ProgressType {
  /// A linear progress indicator.
  linear,

  /// A circular progress indicator.
  circular,

  /// A radial progress indicator.
  radial,
}

class _MetricWidgetState extends State<MetricWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _valueAnimation;
  late double _animatedValue;
  late double _previousAnimatedValue;

  // Interaction state
  bool _isFocused = false;
  bool _isHovered = false;
  late FocusNode _focusNode;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();
    _initializeAnimation();

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (_isFocused && widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    });
  }

  @override
  void didUpdateWidget(MetricWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle value animation
    if (oldWidget.value != widget.value && widget.animateChanges) {
      _updateAnimation();
    }

    // Update focus node if changed
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode = widget.focusNode ?? _focusNode;
      _focusNode.addListener(_onFocusChange);
    }

    // Update callback state if provided
    if (widget.callbackState != null &&
        widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  void _initializeAnimation() {
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );

    _animatedValue = _parseValue(widget.value);
    _previousAnimatedValue = _animatedValue;

    _valueAnimation = Tween<double>(
      begin: _previousAnimatedValue,
      end: _animatedValue,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: widget.animationCurve,
      ),
    );

    _valueAnimation.addListener(() {
      setState(() {
        _animatedValue = _valueAnimation.value;
      });
    });
  }

  void _updateAnimation() {
    _previousAnimatedValue = _animatedValue;
    _animatedValue = _parseValue(widget.value);

    _valueAnimation = Tween<double>(
      begin: _previousAnimatedValue,
      end: _animatedValue,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: widget.animationCurve,
      ),
    );

    _animationController.reset();
    _animationController.forward();
  }

  double _parseValue(dynamic value) {
    if (value is double) {
      return value;
    } else if (value is int) {
      return value.toDouble();
    } else if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  String _formatValue(dynamic value) {
    if (value == null) return '';

    double numericValue = _parseValue(value);
    String formattedValue;

    if (widget.isPercentage) {
      formattedValue = (numericValue * 100).toStringAsFixed(
        widget.decimalPlaces,
      );
      if (widget.useThousandsSeparator) {
        formattedValue = _addThousandsSeparator(formattedValue);
      }
      return '$formattedValue%';
    } else if (widget.isCurrency) {
      formattedValue = numericValue.toStringAsFixed(widget.decimalPlaces);
      if (widget.useThousandsSeparator) {
        formattedValue = _addThousandsSeparator(formattedValue);
      }
      return '${widget.currencySymbol}$formattedValue';
    } else {
      formattedValue = numericValue.toStringAsFixed(widget.decimalPlaces);
      if (widget.useThousandsSeparator) {
        formattedValue = _addThousandsSeparator(formattedValue);
      }

      if (widget.unit != null) {
        return widget.unitOnLeft
            ? '${widget.unit}$formattedValue'
            : '$formattedValue ${widget.unit}';
      }
      return formattedValue;
    }
  }

  String _addThousandsSeparator(String value) {
    final parts = value.split(widget.decimalSeparator);
    final integerPart = parts[0];
    final decimalPart = parts.length > 1 ? parts[1] : '';

    final regex = RegExp(r'\B(?=(\d{3})+(?!\d))');
    final formattedIntegerPart = integerPart.replaceAll(
      regex,
      widget.thousandsSeparator,
    );

    if (decimalPart.isNotEmpty) {
      return '$formattedIntegerPart${widget.decimalSeparator}$decimalPart';
    }
    return formattedIntegerPart;
  }

  double _calculatePercentageChange() {
    if (widget.previousValue == null) return 0.0;

    final previous = _parseValue(widget.previousValue);
    if (previous == 0) return 0.0;

    final current = _parseValue(widget.value);
    return (current - previous) / previous;
  }

  double _calculateAbsoluteChange() {
    if (widget.previousValue == null) return 0.0;

    final previous = _parseValue(widget.previousValue);
    final current = _parseValue(widget.value);
    return current - previous;
  }

  double _calculateProgress() {
    if (widget.targetValue == null) return 0.0;

    final target = _parseValue(widget.targetValue);
    if (target == 0) return 0.0;

    final current = _parseValue(widget.value);
    return current / target;
  }

  bool _isPositiveTrend() {
    if (widget.previousValue == null) return true;

    final previous = _parseValue(widget.previousValue);
    final current = _parseValue(widget.value);
    return current >= previous;
  }

  bool _isAtOrAboveTarget() {
    if (widget.targetValue == null) return true;

    final target = _parseValue(widget.targetValue);
    final current = _parseValue(widget.value);
    return current >= target;
  }

  Color _getTrendColor() {
    if (!widget.autoColor) return widget.neutralColor;

    if (widget.targetValue != null) {
      return _isAtOrAboveTarget() ? widget.positiveColor : widget.negativeColor;
    } else if (widget.previousValue != null) {
      return _isPositiveTrend() ? widget.positiveColor : widget.negativeColor;
    }

    return widget.neutralColor;
  }

  Widget _buildTrendIndicator() {
    if (!widget.showTrend || widget.previousValue == null)
      return const SizedBox();

    final isPositive = _isPositiveTrend();
    final icon = isPositive ? Icons.arrow_upward : Icons.arrow_downward;
    final color = isPositive ? widget.positiveColor : widget.negativeColor;

    return Icon(icon, color: color, size: widget.iconSize * 0.8);
  }

  Widget _buildPercentageChange() {
    if (!widget.showPercentageChange || widget.previousValue == null)
      return const SizedBox();

    final percentChange = _calculatePercentageChange() * 100;
    final isPositive = percentChange >= 0;
    final color = isPositive ? widget.positiveColor : widget.negativeColor;
    final sign = isPositive ? '+' : '';

    return Text(
      '$sign${percentChange.toStringAsFixed(1)}%',
      style: TextStyle(
        color: Color(0xFF0058FF),
        fontSize: widget.valueFontSize * 0.6,
        fontWeight: widget.valueFontWeight,
      ),
    );
  }

  Widget _buildAbsoluteChange() {
    if (!widget.showAbsoluteChange || widget.previousValue == null)
      return const SizedBox();

    final absChange = _calculateAbsoluteChange();
    final isPositive = absChange >= 0;
    final color = isPositive ? widget.positiveColor : widget.negativeColor;
    final sign = isPositive ? '+' : '';

    return Text(
      '$sign${_formatValue(absChange)}',
      style: TextStyle(
        color: color,
        fontSize: widget.valueFontSize * 0.6,
        fontWeight: widget.valueFontWeight,
      ),
    );
  }

  Widget _buildProgressIndicator() {
    if (!widget.showProgress || widget.targetValue == null)
      return const SizedBox();

    final progress = _calculateProgress();
    final clampedProgress = progress.clamp(0.0, 1.0);

    switch (widget.progressType) {
      case ProgressType.linear:
        return Padding(
          padding: const EdgeInsets.only(top: 4.0),
          child: LinearProgressIndicator(
            value: clampedProgress,
            backgroundColor: widget.progressBackgroundColor,
            color: widget.progressColor,
            minHeight: widget.progressThickness,
            borderRadius: BorderRadius.circular(widget.progressThickness / 2),
          ),
        );
      case ProgressType.circular:
        return Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: SizedBox(
            width: widget.iconSize * 2,
            height: widget.iconSize * 2,
            child: CircularProgressIndicator(
              value: clampedProgress,
              backgroundColor: widget.progressBackgroundColor,
              color: widget.progressColor,
              strokeWidth: widget.progressThickness,
            ),
          ),
        );
      case ProgressType.radial:
        // For simplicity, we'll use circular for radial as well
        return Padding(
          padding: const EdgeInsets.only(top: 4.0),
          child: SizedBox(
            width: widget.iconSize * 2,
            height: widget.iconSize * 2,
            child: CircularProgressIndicator(
              value: clampedProgress,
              backgroundColor: widget.progressBackgroundColor,
              color: widget.progressColor,
              strokeWidth: widget.progressThickness,
            ),
          ),
        );
    }
  }

  Widget _buildTargetValue() {
    if (!widget.showTarget || widget.targetValue == null)
      return const SizedBox();

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Target: ',
          style: TextStyle(
            color: widget.subtitleTextColor,
            fontSize: widget.subtitleFontSize,
            fontWeight: widget.subtitleFontWeight,
          ),
        ),
        Text(
          _formatValue(widget.targetValue),
          style: TextStyle(
            color: widget.subtitleTextColor,
            fontSize: widget.subtitleFontSize,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildPreviousValue() {
    if (!widget.showPrevious || widget.previousValue == null)
      return const SizedBox();

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Previous: ',
          style: TextStyle(
            color: widget.subtitleTextColor,
            fontSize: widget.subtitleFontSize,
            fontWeight: widget.subtitleFontWeight,
          ),
        ),
        Text(
          _formatValue(widget.previousValue),
          style: TextStyle(
            color: widget.subtitleTextColor,
            fontSize: widget.subtitleFontSize,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildMetricContent() {
    final valueRow = Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (widget.icon != null)
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: Icon(
              widget.icon,
              color: widget.iconColor ?? _getTrendColor(),
              size: widget.iconSize,
            ),
          ),
        Text(
          widget.animateChanges
              ? _formatValue(_animatedValue)
              : _formatValue(widget.value),
          style: TextStyle(
                    //fontSize: _getResponsiveValueFontSize(context),
                    fontSize: 20,
                    fontFamily: 'Inter',
                    color: Colors.black,
                    fontWeight: FontWeight.w700,
                  ),
        ),
        const SizedBox(width: 4),
        _buildTrendIndicator(),
      ],
    );

    final changeRow = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildPercentageChange(),
        if (widget.showPercentageChange && widget.showAbsoluteChange)
          const SizedBox(width: 8),
        _buildAbsoluteChange(),
      ],
    );

    final comparisonRow = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTargetValue(),
        if (widget.showTarget && widget.showPrevious) const SizedBox(height: 4),
        _buildPreviousValue(),
      ],
    );

    // Return content without title - title will be handled separately
    if (widget.isCompact) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          valueRow,
          if (widget.showPercentageChange || widget.showAbsoluteChange)
            Padding(padding: const EdgeInsets.only(top: 2.0), child: changeRow),
          _buildProgressIndicator(),
        ],
      );
    } else if (widget.direction == Axis.horizontal) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (widget.subtitle != null)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.subtitle!,
                  style: TextStyle(
                    color: widget.subtitleTextColor,
                    fontSize: widget.subtitleFontSize,
                    fontWeight: widget.subtitleFontWeight,
                  ),
                ),
              ],
            ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              valueRow,
              if (widget.showPercentageChange || widget.showAbsoluteChange)
                Padding(
                  padding: const EdgeInsets.only(top: 2.0),
                  child: changeRow,
                ),
            ],
          ),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (widget.subtitle != null)
            Text(
              widget.subtitle!,
              style: TextStyle(
                color: widget.subtitleTextColor,
                fontSize: widget.subtitleFontSize,
                fontWeight: widget.subtitleFontWeight,
              ),
            ),
          if (widget.subtitle != null) const SizedBox(height: 8),
          valueRow,
          if (widget.showPercentageChange || widget.showAbsoluteChange)
            Padding(padding: const EdgeInsets.only(top: 2.0), child: changeRow),
          if (widget.showTarget || widget.showPrevious)
            Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: comparisonRow,
            ),
          _buildProgressIndicator(),
        ],
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget content = _buildMetricContent();

    // Determine border color based on hover and focus state
    Color currentBorderColor = widget.borderColor;
    if (_isHovered && widget.hoverColor != null) {
      currentBorderColor = widget.hoverColor!;
    } else if (_isFocused && widget.focusColor != null) {
      currentBorderColor = widget.focusColor!;
    }

    // Apply container styling with clean input field appearance
    if (widget.isCard) {
      content = Card(
  elevation: 0,
  color: widget.backgroundColor,
  margin: EdgeInsets.zero, // 🚫 Remove default external spacing
  // shape: RoundedRectangleBorder(
  //   borderRadius: BorderRadius.circular(4.0),
  //   side: BorderSide(
  //     color: currentBorderColor,
  //     width: 1.0,
  //   ),
  // ),
  child: Container(
    width: double.infinity,
    height: _getResponsiveHeight(context),
    child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0.0, vertical: 0.0), // Internal padding
      child: content,
    ),
  ),
);

    } else {
      content = Container(
        width: double.infinity, // 100% width
        height: widget.textFieldHeight ?? widget.height ?? 48.0,
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        margin: EdgeInsets.zero,
        decoration: BoxDecoration(
          color: widget.backgroundColor,
          borderRadius: BorderRadius.circular(6.0),
          border: Border.all(
            color: currentBorderColor,
            width: 1.0,
          ),
        ),
        child: Align(
          alignment: Alignment.centerLeft,
          child: content,
        ),
      );
    }

    // Create the title widget outside the box
    Widget? titleWidget;
    if (widget.title != null) {
      titleWidget = Text(
            widget.title!,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: _getResponsiveFontSize(context),
            ),
             );
      
    }

    // Apply hover detection with state management
    content = MouseRegion(
      onEnter: (event) {
        setState(() {
          _isHovered = true;
        });
        if (widget.onHover != null) {
          widget.onHover!(true);
        }
      },
      onExit: (event) {
        setState(() {
          _isHovered = false;
        });
        if (widget.onHover != null) {
          widget.onHover!(false);
        }
      },
      child: content,
    );

    // Apply interactivity with advanced features
    if (widget.isInteractive) {
      content = InkWell(
        onTap:
            widget.onTap != null
                ? () {
                  // Execute onTap callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onTap')) {
                    _executeJsonCallback('onTap');
                  }

                  // Call standard callback
                  widget.onTap!();
                }
                : null,
        onDoubleTap:
            widget.onDoubleTap != null
                ? () {
                  // Execute onDoubleTap callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                    _executeJsonCallback('onDoubleTap');
                  }

                  // Call standard callback
                  widget.onDoubleTap!();
                }
                : null,
        onLongPress:
            widget.onLongPress != null
                ? () {
                  // Execute onLongPress callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onLongPress')) {
                    _executeJsonCallback('onLongPress');
                  }

                  // Call standard callback
                  widget.onLongPress!();
                }
                : null,
        borderRadius: BorderRadius.circular(6.0),
        enableFeedback: widget.enableFeedback,
        child: content,
      );
    }

    // Apply focus handling
    if (widget.autofocus || widget.onFocus != null) {
      content = Focus(
        focusNode: _focusNode,
        autofocus: widget.autofocus,
        onFocusChange: (focused) {
          setState(() {
            _isFocused = focused;
          });
          if (widget.onFocus != null) {
            widget.onFocus!(focused);
          }
        },
        child: content,
      );
    }

    // Apply tooltip
    if (widget.showTooltip && widget.tooltipText != null) {
      content = Tooltip(message: widget.tooltipText!, child: content);
    }

    // Apply accessibility
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      content = Semantics(
        label: widget.semanticsLabel,
        value: widget.value.toString(),
        excludeSemantics: false,
        child: content,
      );
    }

    // Combine title and content with proper alignment
    if (titleWidget != null) {
      return Container(
        width: double.infinity,
        margin: widget.margin,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [

            titleWidget,
            SizedBox(height:_getResponsiveBoxsize(context)),
            content,
          ],
        ),
      );
    }

    // Apply margin to content when there's no title
    return Container(margin: widget.margin, child: content);
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }
  
double _getResponsiveBoxsize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 8.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 8.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 6.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 4.0; // Small (768-1024px)
  } else {
    return 4.0; // Default for very small screens
  }
}
}

   double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 16.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 14.0; // Large
    } else if (screenWidth >= 1280) {
      return 12.0; // Medium
    } else {
      return 12.0; // Default for very small screens
    }
  }

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 32.0; // Small (768-1024px)
  } else {
    return 32.0; // Default for very small screens
  }
}

double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }
