import 'package:equatable/equatable.dart';

abstract class HomeEvent extends Equatable {
  const HomeEvent();

  @override
  List<Object?> get props => [];
}

class FetchDropdownA extends HomeEvent {}

class UpdateSelectedValueA extends HomeEvent {
  final String value;
  const UpdateSelectedValueA(this.value);
  @override
  List<Object?> get props => [value];
}

class DropdownAError extends HomeEvent {
  final String message;
  const DropdownAError(this.message);
  @override
  List<Object?> get props => [message];
}

