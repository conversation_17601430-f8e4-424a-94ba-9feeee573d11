import 'package:flutter/material.dart';
import 'package:ui_controls_library/ui_controls_library.dart' as ui_controls;



class RingPieChartDemoScreen extends StatefulWidget {
  const RingPieChartDemoScreen({super.key});

  @override 
  State<RingPieChartDemoScreen> createState() => _RingPieChartDemoScreenState();
}

class _RingPieChartDemoScreenState extends State<RingPieChartDemoScreen> {
  ui_controls.ChartSizeType selectedSize = ui_controls.ChartSizeType.small;
  ui_controls.SelectedChartType selectedChartType=ui_controls.SelectedChartType.ring;

  String propertyType = "Properties";

  ui_controls.ChartType get selectedChartTypeFromDropdown {
    switch (chosenValue) {
      case 'Donut Chart':
        return ui_controls.ChartType.ring;
      case 'Pie Chart':
        return ui_controls.ChartType.disc;
      case 'Bubble Chart':
        return ui_controls.ChartType.bubble;
      case 'Scatter Plot':
        return ui_controls.ChartType.scatter;
      default:
        return ui_controls.ChartType.ring; // Default to donut chart
    }
  }

  ui_controls.ChartSizeConfig get currentConfig {
    switch (selectedSize) {
      case ui_controls.ChartSizeType.small:
        return ui_controls.ChartSizeConfig(
          size: ui_controls.ChartSizeType.small,
          headingFontSize: 14,
          bodyFontSize: 12,
          labelFontSize: 10,
          chartRadius: 100,
          propertyType: propertyType,
          borderThikness: 0.5,
          borderRadius: 6,
          elevation: 1.0,
          chartType: selectedChartTypeFromDropdown
        );
      case ui_controls.ChartSizeType.medium:
        return ui_controls.ChartSizeConfig(
          size: ui_controls.ChartSizeType.medium,
          headingFontSize: 16,
          bodyFontSize: 14,
          labelFontSize: 12,
          chartRadius: 100,
          propertyType: propertyType,
          borderThikness: 1.0,
          borderRadius: 16,
          elevation: 1.5,
          chartType: selectedChartTypeFromDropdown
        );
      case ui_controls.ChartSizeType.large:
        return ui_controls.ChartSizeConfig(
          size: ui_controls.ChartSizeType.large,
          headingFontSize: 18,
          bodyFontSize: 16,
          labelFontSize: 14,
          chartRadius: 100,
          propertyType: propertyType,
          borderThikness: 2.0,
          borderRadius: 24,
          elevation: 2.0,
          chartType: selectedChartTypeFromDropdown
        );
    }
  }
   var chosenValue;
  List<String> chartTypeList = ["Donut Chart", "Pie Chart", "Bubble Chart", "Scatter Plot"];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Size Selection
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 200,
                              padding: const EdgeInsets.all(10),
                             
                              child: DropdownButton<String>(
                              elevation: 1,
                                underline: SizedBox(), // to remove underline
                              isExpanded: true,
                              hint: const Text("Select Chart Type"),
                              iconSize: 30,
                              icon: const Icon(Icons.arrow_drop_down_sharp,size: 15,),
                              value: chosenValue ,
                              style: TextStyle(fontSize: 16, fontWeight: FontWeight.normal),
                              items: chartTypeList.map<DropdownMenuItem<String>>((String value) {
                                return DropdownMenuItem<String>(
                                  value: value,
                                  child: Text(value),
                                );
                              }).toList(),
                                onChanged: (value) {
                                  setState((){
                                    chosenValue = value;
                                  });
                                },
                              alignment:AlignmentDirectional.centerStart ,
                             
                              isDense: true,
                             
                                selectedItemBuilder: (BuildContext context) {
                                  return chartTypeList.map<Widget>((String item) {
                                    //This widget is shown after you select an item
                                    return Container(
                                      alignment: Alignment.centerLeft,
                                      constraints: const BoxConstraints(minWidth: 100),
                                      child: Text(
                    item,
                    style: const TextStyle(
                        color: Colors.black, fontWeight: FontWeight.w600),
                                      ),
                                    );
                                  }).toList();
                                },
                              ),
                            ),
                    const Text('Chart Size:', style: TextStyle(fontWeight: FontWeight.w600)),
                  ],
                ),
const SizedBox(height: 8),
Wrap(
  spacing: 16.0,
  children: ui_controls.ChartSizeType.values.map((size) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Radio<ui_controls.ChartSizeType>(
          value: size,
          groupValue: selectedSize,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                selectedSize = value;
              });
            }
          },
        ),
        Text(size.name.toUpperCase()),
      ],
    );
  }).toList(),
),
          // Control Panel
           Expanded(
            // child: ui_controls.DateRangeWidget(),
            child: ui_controls.RingPieChartUIBuilder(
              config: currentConfig,
              dataMap: chartDataMap,
              colorList: chartColorList,
              onPressed: () {
                // Handle any button press if needed
                print('Ring Pie Chart button pressed');
              },
            ),
          ),
         
          
          // Chart Display
         
        ],
      ),
    );
  }

  // Sample data and colors to pass to the chart
  Map<String, double> get chartDataMap => {
    'Food': 20,
    'Rent': 15,
    'Transport': 10,
    'Savings': 12,
    'Others': 8,
    'Utilities': 10,
    'Insurance': 15,
    'Entertainment': 10,
  };

  List<Color> get chartColorList => [
    const Color(0xFF0D47A1), const Color(0xFF1565C0), const Color(0xFF1976D2), const Color(0xFF1E88E5),
    const Color(0xFF42A5F5), const Color(0xFF64B5F6), const Color(0xFF90CAF9), const Color(0xFFBBDEFB),
  ];
}
