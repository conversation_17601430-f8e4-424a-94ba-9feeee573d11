import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:url_launcher/url_launcher.dart';
import '../utils/callback_interpreter.dart';
import 'utils/file_widget_json_parser.dart';
import 'components/file_widget_components.dart';

/// Mobile-specific implementation of FileWidget
class FileWidgetMobile extends StatefulWidget {
  // Basic properties
  final bool isRequired;
  final bool allowMultiple;
  final List<String>? allowedExtensions;
  final FileType fileType;
  final int? maxFileSizeBytes;
  final int? maxFiles;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final bool hasBorder;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isCompact;
  final bool hasShadow;
  final double elevation;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Label properties
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final String buttonText;

  // Icon properties
  final bool showIcon;
  final IconData? icon;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool showFileName;
  final bool showFileSize;
  final bool showFileType;
  final bool showClearButton;
  final bool showPreview;
  final bool uploadImmediately;
  final bool showProgressBar;
  final bool allowDragDrop;

  // Layout properties
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  // Callbacks
  final Function(List<PlatformFile>)? onFilesSelected;
  final Function()? onClear;
  final Function(List<PlatformFile>)? onUpload;
  final Function(PlatformFile)? onViewFile;
  final Function(PlatformFile)? onOpenFile;
  final Function()? onCancelUpload;

  // Advanced interaction properties
  final void Function(bool)? onHover;
  final void Function(bool)? onFocus;
  final FocusNode? focusNode;
  final Color? hoverColor;
  final Color? focusColor;
  final bool enableFeedback;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;
  final Map<String, dynamic>? jsonConfig;
  final bool useJsonValidation;
  final bool useJsonStyling;
  final bool useJsonFormatting;

  // File-specific JSON configuration
  final bool useJsonFileHandling;
  final Map<String, dynamic>? fileHandlingConfig;

  const FileWidgetMobile({
    super.key,
    this.isRequired = false,
    this.allowMultiple = false,
    this.allowedExtensions,
    this.fileType = FileType.any,
    this.maxFileSizeBytes,
    this.maxFiles,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.buttonText = 'Choose File',
    this.showIcon = true,
    this.icon = Icons.upload_file,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.showFileName = true,
    this.showFileSize = true,
    this.showFileType = true,
    this.showClearButton = true,
    this.showPreview = false,
    this.uploadImmediately = false,
    this.showProgressBar = false,
    this.allowDragDrop = false,
    this.width = double.infinity,
    this.height = 0,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = const EdgeInsets.all(0),
    this.onFilesSelected,
    this.onClear,
    this.onUpload,
    this.onViewFile,
    this.onOpenFile,
    this.onCancelUpload,
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    this.useJsonFileHandling = false,
    this.fileHandlingConfig,
  });

  @override
  State<FileWidgetMobile> createState() => _FileWidgetMobileState();
}

class _FileWidgetMobileState extends State<FileWidgetMobile> {
  List<PlatformFile> _selectedFiles = [];
  String? _errorText;
  bool _isUploading = false;
  double _uploadProgress = 0.0;

  @override
  void initState() {
    super.initState();
    _errorText = widget.errorText;
  }

  Future<void> _pickFiles() async {
    if (widget.isDisabled || widget.isReadOnly) return;

    try {
      final result = await FilePicker.platform.pickFiles(
        type: widget.fileType,
        allowMultiple: widget.allowMultiple,
        allowedExtensions: widget.fileType == FileType.custom ? widget.allowedExtensions : null,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _selectedFiles = result.files;
          _errorText = null;
        });

        if (widget.onFilesSelected != null) {
          widget.onFilesSelected!(_selectedFiles);
        }

        if (widget.uploadImmediately) {
          _uploadFiles();
        }
      }
    } catch (e) {
      setState(() {
        _errorText = 'Error picking files: $e';
      });
    }
  }

  Future<void> _uploadFiles() async {
    if (_selectedFiles.isEmpty) return;

    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
    });

    try {
      // Simulate upload progress for mobile
      for (int i = 0; i <= 100; i += 10) {
        if (!mounted) return;
        await Future.delayed(const Duration(milliseconds: 100));
        setState(() {
          _uploadProgress = i / 100;
        });
      }

      setState(() {
        _isUploading = false;
      });

      if (widget.onUpload != null) {
        widget.onUpload!(_selectedFiles);
      }
    } catch (e) {
      setState(() {
        _isUploading = false;
        _errorText = 'Upload failed: $e';
      });
    }
  }

  void _clearFiles() {
    setState(() {
      _selectedFiles = [];
      _errorText = widget.errorText;
      _uploadProgress = 0.0;
      _isUploading = false;
    });

    if (widget.onClear != null) {
      widget.onClear!();
    }
  }

  Future<void> _openFile(PlatformFile file) async {
    if (file.path != null) {
      try {
        final Uri fileUri = Uri.file(file.path!);
        if (await canLaunchUrl(fileUri)) {
          await launchUrl(fileUri, mode: LaunchMode.externalApplication);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Cannot open file: ${file.name}')),
          );
        }
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error opening file: $e')),
        );
      }
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  IconData _getFileIcon(String? extension) {
    if (extension == null) return Icons.insert_drive_file;
    
    switch (extension.toLowerCase()) {
      case 'pdf': return Icons.picture_as_pdf;
      case 'doc':
      case 'docx': return Icons.description;
      case 'xls':
      case 'xlsx': return Icons.table_chart;
      case 'ppt':
      case 'pptx': return Icons.slideshow;
      case 'txt': return Icons.text_snippet;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp': return Icons.image;
      case 'mp4':
      case 'avi':
      case 'mov': return Icons.video_file;
      case 'mp3':
      case 'wav':
      case 'flac': return Icons.audio_file;
      case 'zip':
      case 'rar':
      case '7z': return Icons.folder_zip;
      default: return Icons.insert_drive_file;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      margin: widget.margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.label != null) ...[
            Text(
              widget.label!,
              style: TextStyle(
                fontSize: widget.fontSize,
                fontWeight: FontWeight.w500,
                color: widget.textColor,
              ),
            ),
            const SizedBox(height: 8),
          ],
          
          // File picker button or file display
          if (_selectedFiles.isEmpty)
            _buildPickerButton()
          else
            _buildFileList(),
          
          // Upload progress
          if (_isUploading) ...[
            const SizedBox(height: 8),
            LinearProgressIndicator(value: _uploadProgress),
            const SizedBox(height: 4),
            Text(
              'Uploading... ${(_uploadProgress * 100).toInt()}%',
              style: TextStyle(fontSize: widget.fontSize * 0.8),
            ),
          ],
          
          // Error text
          if (_errorText != null) ...[
            const SizedBox(height: 8),
            Text(
              _errorText!,
              style: TextStyle(
                color: Colors.red,
                fontSize: widget.fontSize * 0.8,
              ),
            ),
          ],
          
          // Helper text
          if (widget.helperText != null) ...[
            const SizedBox(height: 4),
            Text(
              widget.helperText!,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: widget.fontSize * 0.8,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPickerButton() {
    return Container(
      width: double.infinity,
      height: widget.height > 0 ? widget.height : 48,
      child: ElevatedButton.icon(
        onPressed: widget.isDisabled || widget.isReadOnly ? null : _pickFiles,
        icon: widget.showIcon && widget.icon != null 
            ? Icon(widget.icon, size: 20)
            : const SizedBox.shrink(),
        label: Text(
          widget.buttonText,
          style: TextStyle(fontSize: widget.fontSize),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: widget.backgroundColor,
          foregroundColor: widget.textColor,
          padding: widget.padding,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            side: widget.hasBorder 
                ? BorderSide(color: widget.borderColor, width: widget.borderWidth)
                : BorderSide.none,
          ),
          elevation: widget.hasShadow ? widget.elevation : 0,
        ),
      ),
    );
  }

  Widget _buildFileList() {
    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder 
            ? Border.all(color: widget.borderColor, width: widget.borderWidth)
            : null,
      ),
      child: Column(
        children: [
          ..._selectedFiles.map((file) => _buildFileItem(file)).toList(),
          if (widget.allowMultiple) ...[
            const Divider(height: 1),
            ListTile(
              leading: const Icon(Icons.add),
              title: const Text('Add more files'),
              onTap: _pickFiles,
              dense: true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFileItem(PlatformFile file) {
    return ListTile(
      leading: Icon(
        _getFileIcon(file.extension),
        color: Colors.blue,
      ),
      title: Text(
        file.name,
        style: TextStyle(fontSize: widget.fontSize),
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: widget.showFileSize 
          ? Text(_formatFileSize(file.size))
          : null,
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.onViewFile != null)
            IconButton(
              icon: const Icon(Icons.visibility),
              onPressed: () => widget.onViewFile!(file),
              iconSize: 20,
            ),
          IconButton(
            icon: const Icon(Icons.open_in_new),
            onPressed: () => _openFile(file),
            iconSize: 20,
          ),
          if (widget.showClearButton)
            IconButton(
              icon: const Icon(Icons.delete_outline),
              onPressed: () {
                setState(() {
                  _selectedFiles.remove(file);
                  if (_selectedFiles.isEmpty) {
                    _clearFiles();
                  }
                });
              },
              iconSize: 20,
            ),
        ],
      ),
    );
  }
}
