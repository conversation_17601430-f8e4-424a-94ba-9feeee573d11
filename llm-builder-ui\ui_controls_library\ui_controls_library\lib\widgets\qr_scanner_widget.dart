import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:file_picker/file_picker.dart';
import '../utils/callback_interpreter.dart';

/// Enum for the different states of the QR scanner widget
enum QrScannerState { defaultState, hover, scanning, scanned, scanCompleted }

/// Extension on Color to provide hex string conversion
extension QrScannerColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

class QrScannerWidget extends StatefulWidget {
  /// Current state of the widget
  final QrScannerState initialState;

  /// Whether to enable drag and drop functionality
  final bool enableDragDrop;

  /// Whether to enable file browsing
  final bool enableFileBrowse;

  /// Whether to enable QR scanning
  final bool enableQrScanning;

  /// Text to display in drag and drop area
  final String dragDropText;

  /// Text to display on browse files button
  final String browseFilesText;

  /// Text to display when camera is active
  final String clickToScanText;

  /// Background color for default state
  final Color defaultBackgroundColor;

  /// Border color for default state
  final Color defaultBorderColor;

  /// Background color for hover state
  final Color hoverBackgroundColor;

  /// Border color for hover state
  final Color hoverBorderColor;

  /// Button color
  final Color buttonColor;

  /// Button text color
  final Color buttonTextColor;

  /// Camera background color
  final Color cameraBackgroundColor;

  /// QR scanner frame color
  final Color scannerFrameColor;

  /// Icon color
  final Color iconColor;

  /// Text color
  final Color textColor;

  /// Border width
  final double borderWidth;

  /// Border radius
  final double borderRadius;

  /// Widget height
  final double height;

  /// Widget width
  final double? width;

  /// Padding inside the widget
  final EdgeInsets padding;

  /// Margin around the widget
  final EdgeInsets margin;

  /// Camera icon size
  final double cameraIconSize;

  /// Upload icon size
  final double uploadIconSize;

  /// Button height
  final double buttonHeight;

  /// Button width
  final double buttonWidth;

  /// Scanner frame size
  final double scannerFrameSize;

  /// Callback when a file is selected
  final Function(File)? onFileSelected;

  /// Callback when a QR code is scanned
  final Function(String)? onQrScanned;

  /// Callback when state changes
  final Function(QrScannerState)? onStateChanged;

  /// Callback when widget is hovered
  final Function(bool)? onHover;

  /// Callback when an error occurs
  final Function(String)? onError;

  /// Whether to show flash/torch button
  final bool showFlashButton;

  /// Whether to auto-close camera after scan
  final bool autoCloseAfterScan;

  /// Accepted file extensions
  final List<String>? allowedExtensions;

  /// Maximum file size in bytes
  final int? maxFileSize;

  /// Whether to vibrate on scan
  final bool vibrateOnScan;

  /// Whether to play sound on scan
  final bool playSoundOnScan;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;

  const QrScannerWidget({
    super.key,
    this.initialState = QrScannerState.defaultState,
    this.enableDragDrop = true,
    this.enableFileBrowse = true,
    this.enableQrScanning = true,
    this.dragDropText = 'Drag & drop',
    this.browseFilesText = 'Browse Files',
    this.clickToScanText = 'Click to scan',
    this.defaultBackgroundColor = Colors.white,
    this.defaultBorderColor = const Color(0xFFE0E0E0),
    this.hoverBackgroundColor = const Color(0xFFf0f5ff),
    this.hoverBorderColor = const Color(0xFF0058FF),
    this.buttonColor = const Color(0xFF0058FF),
    this.buttonTextColor = Colors.white,
    this.cameraBackgroundColor = const Color(0xFF2D2D2D),
    this.scannerFrameColor = Colors.white,
    this.iconColor = const Color(0xFFCCCCCC),
    this.textColor = const Color(0xFF666666),
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.height = 225.0,
    this.width,
    this.padding = const EdgeInsets.all(0.0),
    this.margin = EdgeInsets.zero,
    this.cameraIconSize = 30.0,
    this.uploadIconSize = 20.0,
    this.buttonHeight = 30.0,
    this.buttonWidth = 135.0,
    this.scannerFrameSize = 200.0,
    this.onFileSelected,
    this.onQrScanned,
    this.onStateChanged,
    this.onHover,
    this.onError,
    this.showFlashButton = false,
    this.autoCloseAfterScan = true,
    this.allowedExtensions,
    this.maxFileSize,
    this.vibrateOnScan = false,
    this.playSoundOnScan = false,
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
  });

  /// Creates a QrScannerWidget from a JSON map
  factory QrScannerWidget.fromJson(Map<String, dynamic> json) {
    // Parse initial state
    QrScannerState initialState = QrScannerState.defaultState;
    if (json.containsKey('initialState')) {
      final String stateStr = json['initialState'].toString().toLowerCase();
      switch (stateStr) {
        case 'hover':
          initialState = QrScannerState.hover;
          break;
        case 'scanning':
          initialState = QrScannerState.scanning;
          break;
        case 'scanned':
          initialState = QrScannerState.scanned;
          break;
        default:
          initialState = QrScannerState.defaultState;
      }
    }

    // Parse colors
    Color parseColor(dynamic colorValue, Color defaultColor) {
      if (colorValue is String) {
        if (colorValue.startsWith('#')) {
          String hex = colorValue.replaceFirst('#', '');
          if (hex.length == 6) hex = 'FF$hex';
          return Color(int.parse(hex, radix: 16));
        }
      }
      return defaultColor;
    }

    return QrScannerWidget(
      initialState: initialState,
      enableDragDrop: json['enableDragDrop'] as bool? ?? true,
      enableFileBrowse: json['enableFileBrowse'] as bool? ?? true,
      enableQrScanning: json['enableQrScanning'] as bool? ?? true,
      dragDropText: json['dragDropText'] as String? ?? 'Drag & drop',
      browseFilesText: json['browseFilesText'] as String? ?? 'Browse Files',
      clickToScanText: json['clickToScanText'] as String? ?? 'Click to scan',
      defaultBackgroundColor: parseColor(
        json['defaultBackgroundColor'],
        Colors.white,
      ),
      defaultBorderColor: parseColor(
        json['defaultBorderColor'],
        const Color(0xFFE0E0E0),
      ),
      hoverBackgroundColor: parseColor(
        json['hoverBackgroundColor'],
        const Color(0xFFf0f5ff),
      ),
      hoverBorderColor: parseColor(
        json['hoverBorderColor'],
        const Color(0xFF0058FF),
      ),
      buttonColor: parseColor(json['buttonColor'], const Color(0xFF0058FF)),
      buttonTextColor: parseColor(json['buttonTextColor'], Colors.white),
      cameraBackgroundColor: parseColor(
        json['cameraBackgroundColor'],
        const Color(0xFF2D2D2D),
      ),
      scannerFrameColor: parseColor(json['scannerFrameColor'], Colors.white),
      iconColor: parseColor(json['iconColor'], const Color(0xFF0058FF)),
      textColor: parseColor(json['textColor'], const Color(0xFF666666)),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      height: (json['height'] as num?)?.toDouble() ?? 200.0,
      width: (json['width'] as num?)?.toDouble(),
      cameraIconSize: (json['cameraIconSize'] as num?)?.toDouble() ?? 30.0,
      uploadIconSize: (json['uploadIconSize'] as num?)?.toDouble() ?? 20.0,
      buttonHeight: (json['buttonHeight'] as num?)?.toDouble() ?? 40.0,
      buttonWidth: (json['buttonWidth'] as num?)?.toDouble() ?? 130.0,
      scannerFrameSize: (json['scannerFrameSize'] as num?)?.toDouble() ?? 200.0,
      showFlashButton: json['showFlashButton'] as bool? ?? false,
      autoCloseAfterScan: json['autoCloseAfterScan'] as bool? ?? true,
      vibrateOnScan: json['vibrateOnScan'] as bool? ?? false,
      playSoundOnScan: json['playSoundOnScan'] as bool? ?? false,
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      jsonCallbacks: json['callbacks'] as Map<String, dynamic>?,
    );
  }

  @override
  State<QrScannerWidget> createState() => _QrScannerWidgetState();
}

class _QrScannerWidgetState extends State<QrScannerWidget>
    with SingleTickerProviderStateMixin {
  QrScannerState _currentState = QrScannerState.defaultState;
  bool _isHovered = false;
  bool _hasPermission = false;
  MobileScannerController? _controller;
  String? _scannedData;
  Uint8List? _scannedImage;
  bool _isFlashOn = false;
  late TextEditingController controller;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();
    controller = TextEditingController(text: _scannedData);
    _currentState = widget.initialState;
    _checkPermission();

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    controller.dispose();
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  Future<void> _checkPermission() async {
    final status = await Permission.camera.status;
    setState(() {
      _hasPermission = status.isGranted;
    });

    if (!status.isGranted) {
      final result = await Permission.camera.request();
      setState(() {
        _hasPermission = result.isGranted;
      });
    }
  }

  void _changeState(QrScannerState newState) {
    if (_currentState != newState) {
      setState(() {
        _currentState = newState;
      });

      // Call state change callback
      if (widget.onStateChanged != null) {
        widget.onStateChanged!(newState);
      }

      // Execute JSON callback if defined
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onStateChanged')) {
        _executeJsonCallback('onStateChanged', newState.toString());
      }
    }
  }

  void _handleHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });

    // Handle hover state transitions properly
    if (isHovered && _currentState == QrScannerState.defaultState) {
      _changeState(QrScannerState.hover);
    } else if (!isHovered && _currentState == QrScannerState.hover) {
      _changeState(QrScannerState.defaultState);
    }

    // Call hover callback
    if (widget.onHover != null) {
      widget.onHover!(isHovered);
    }

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onHover')) {
      _executeJsonCallback('onHover', isHovered);
    }
  }

  Future<void> _handleCameraPress() async {
    if (!widget.enableQrScanning) return;

    if (!_hasPermission) {
      await _checkPermission();
      if (!_hasPermission) {
        _handleError('Camera permission is required');
        return;
      }
    }

    _changeState(QrScannerState.scanning);

    // Initialize camera controller
    _controller = MobileScannerController(
      facing: CameraFacing.back,
      torchEnabled: _isFlashOn,
    );
  }

  void _handleCameraClose() {
    _controller?.dispose();
    _controller = null;
    _changeState(QrScannerState.defaultState);
  }

  Future<void> _handleFileBrowse() async {
    if (!widget.enableFileBrowse) return;

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions:
            widget.allowedExtensions ?? ['jpg', 'jpeg', 'png', 'pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);

        // Check file size if specified
        if (widget.maxFileSize != null) {
          final fileSize = await file.length();
          if (fileSize > widget.maxFileSize!) {
            _handleError('File size exceeds maximum allowed size');
            return;
          }
        }

        // Call file selected callback
        if (widget.onFileSelected != null) {
          widget.onFileSelected!(file);
        }

        // Execute JSON callback if defined
        if (widget.useJsonCallbacks &&
            widget.jsonCallbacks != null &&
            widget.jsonCallbacks!.containsKey('onFileSelected')) {
          _executeJsonCallback('onFileSelected', file.path);
        }
      }
    } catch (e) {
      _handleError('Error selecting file: $e');
    }
  }

  void _handleQrScanned(String data, Uint8List? image) {
    setState(() {
      _scannedData = data;
      _scannedImage = image;
    });

    _changeState(QrScannerState.scanned);

    // Call QR scanned callback
    if (widget.onQrScanned != null) {
      widget.onQrScanned!(data);
    }

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onQrScanned')) {
      _executeJsonCallback('onQrScanned', data);
    }

    // Auto-close camera if configured
    if (widget.autoCloseAfterScan) {
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          _handleCameraClose();
        }
      });
    }
  }

  void _handleError(String error) {
    // Call error callback
    if (widget.onError != null) {
      widget.onError!(error);
    }

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onError')) {
      _executeJsonCallback('onError', error);
    }
  }

  void _toggleFlash() {
    setState(() {
      _isFlashOn = !_isFlashOn;
      _controller?.toggleTorch();
    });
  }

  Widget _buildDottedBorder({
    required Widget child,
    required Color color,
    required double borderRadius,
  }) {
    return CustomPaint(
      painter: DottedBorderPainter(color: color, borderRadius: borderRadius),
      child: child,
    );
  }

  Widget _buildDefaultState() {
    final isHovered = _currentState == QrScannerState.hover;

    return Container(
      width: double.infinity,
      height: _getResponsiveHeight(context),
      margin: widget.margin,
      decoration: BoxDecoration(
        color: widget.defaultBackgroundColor,
        border: Border.all(
          color:
              isHovered ? widget.hoverBorderColor : widget.defaultBorderColor,
          width: 1,
        ),
        //borderRadius: BorderRadius.circular(widget.borderRadius),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Green dot indicator
          Container(
            margin: const EdgeInsets.only(left: 12),
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
            ),
          ),

          const SizedBox(width: 8),

          // Input field
          Expanded(
            child: TextField(
              controller: controller,

              decoration: InputDecoration(
                isCollapsed: true,
                hintText: 'Enter QR Code',
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                filled: false,
                contentPadding: EdgeInsets.zero,
              ),
              style: TextStyle(
                color: widget.textColor,
                fontSize: _getResponsiveValueFontSize(context),
              ),
              onChanged: (value) {
                // Handle manual QR code entry
                if (value.isNotEmpty) {
                  setState(() {
                    _scannedData = value;
                  });
                  //_changeState(QrScannerState.scanned);
                }
              },
            ),
          ),

          // Upload icon
          MouseRegion(
            onEnter: (_) {},
            onExit: (_) {},
            child: GestureDetector(
              onTap: _handleFileBrowse,
              child: Container(
                padding: const EdgeInsets.all(0),
                margin: const EdgeInsets.only(right: 12),
                child: Transform.scale(
                  scale: 1,
                  child: SvgPicture.asset(
                    _isHovered
                        ? 'assets/images/upload-hover.svg'
                        : 'assets/images/upload.svg',
                    package: 'ui_controls_library',
                    width: _getResponsiveIconSize(context),
                  ),
                ),
              ),
            ),
          ),

          // Camera icon
          MouseRegion(
            onEnter: (_) {},
            onExit: (_) {},
            child: GestureDetector(
              onTap: _handleCameraPress,
              child: Container(
                padding: const EdgeInsets.all(0),
                margin: const EdgeInsets.only(right: 8),
                // child: Icon(
                //   Icons.photo_camera_outlined,
                //   size: 20,
                //   color: isHovered ? widget.hoverBorderColor : widget.iconColor,
                // ),
                child: Transform.scale(
                  scale: 1,
                  child: SvgPicture.asset(
                    _isHovered
                        ? 'assets/images/camera-hover.svg'
                        : 'assets/images/camera.svg',
                    package: 'ui_controls_library',
                    width: _getResponsiveIconSize(context),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScanningState() {
    return Positioned.fill(
      // ensures full-screen overlay
      child: Container(
        color: Colors.black, // optional background for clarity
        child: Stack(
          children: [
            // Full-screen camera preview
            if (_controller != null)
              Positioned.fill(
                child: MobileScanner(
                  controller: _controller!,
                  onDetect: (capture) {
                    final List<Barcode> barcodes = capture.barcodes;
                    if (barcodes.isNotEmpty && barcodes[0].rawValue != null) {
                      final String code = barcodes[0].rawValue!;
                      _changeState(QrScannerState.scanCompleted);
                      Future.delayed(const Duration(seconds: 2), () {
                        _handleQrScanned(code, capture.image);
                      });
                    }
                  },
                  errorBuilder: (context, error, child) {
                    _handleError('Scanner error: $error');
                    return Container(
                      color: Colors.black,
                      child: const Center(
                        child: Text(
                          'Scanner Error',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    );
                  },
                ),
              ),

            // Scanner frame overlay
            Center(
              child: Container(
                width: 300,
                height: 300,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white, width: 3.0),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Stack(
                  children: [
                    ...List.generate(4, (index) {
                      return Positioned(
                        top: index < 2 ? -3 : null,
                        bottom: index >= 2 ? -3 : null,
                        left: index % 2 == 0 ? -3 : null,
                        right: index % 2 == 1 ? -3 : null,
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            border: Border(
                              top:
                                  index < 2
                                      ? const BorderSide(
                                        color: Colors.white,
                                        width: 4,
                                      )
                                      : BorderSide.none,
                              bottom:
                                  index >= 2
                                      ? const BorderSide(
                                        color: Colors.white,
                                        width: 4,
                                      )
                                      : BorderSide.none,
                              left:
                                  index % 2 == 0
                                      ? const BorderSide(
                                        color: Colors.white,
                                        width: 4,
                                      )
                                      : BorderSide.none,
                              right:
                                  index % 2 == 1
                                      ? const BorderSide(
                                        color: Colors.white,
                                        width: 4,
                                      )
                                      : BorderSide.none,
                            ),
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              ),
            ),

            // Instruction text
            const Positioned(
              bottom: 100,
              left: 20,
              right: 20,
              child: Center(
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.all(Radius.circular(20)),
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                    child: Text(
                      'Align QR code with the frame to scan',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ),

            // Close button
            Positioned(
              top: 15,
              right: 15,
              child: GestureDetector(
                onTap: _handleCameraClose,
                child: Container(
                  width: 36,
                  height: 36,
                  decoration: const BoxDecoration(
                    color: Colors.black54,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.close, color: Colors.white, size: 24),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScannedState() {
    return Container(
      width: double.infinity,
      height: _getResponsiveHeight(context),
      margin: widget.margin,
      decoration: BoxDecoration(
        color: widget.defaultBackgroundColor,
        border: Border.all(
          color:
              _isHovered ? widget.hoverBorderColor : widget.defaultBorderColor,
          width: 1,
        ),
        //borderRadius: BorderRadius.circular(widget.borderRadius),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Row(
        children: [
          // Green dot indicator
          Container(
            margin: const EdgeInsets.only(left: 12),
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
            ),
          ),

          const SizedBox(width: 8),

          // Scanned data display
          Expanded(
            child: Text(
              _scannedData ?? '',
              style: TextStyle(color: widget.textColor, fontSize: 14),
            ),
          ),

          // Success checkmark
          Container(
            padding: const EdgeInsets.all(8),
            child: Container(
              width: 24,
              height: 24,
              // decoration: BoxDecoration(
              //   color: Colors.pink.shade400,
              //   shape: BoxShape.circle,
              // ),
              child: const Icon(Icons.check, color: Colors.grey, size: 16),
            ),
          ),

          // Upload icon
          GestureDetector(
            onTap: _handleFileBrowse,
            child: Container(
              padding: const EdgeInsets.all(8),
              margin: const EdgeInsets.only(right: 8),
              child: Icon(
                Icons.upload_file_outlined,
                size: 20,
                color: widget.iconColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScanCompletedState() {
    return Stack(
      children: [
        // Full-screen camera preview (still showing)
        if (_controller != null)
          SizedBox(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            child: MobileScanner(
              controller: _controller!,
              onDetect: (capture) {
                // Don't process more scans in completed state
              },
              errorBuilder: (context, error, child) {
                return Container(
                  color: Colors.black,
                  child: Center(
                    child: Text(
                      'Scanner Error',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                );
              },
            ),
          ),

        // Scanner frame overlay (same as scanning)
        Center(
          child: Container(
            width: 300,
            height: 300,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.white, width: 3.0),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Stack(
              children: [
                // Corner frames
                ...List.generate(4, (index) {
                  return Positioned(
                    top: index < 2 ? -3 : null,
                    bottom: index >= 2 ? -3 : null,
                    left: index % 2 == 0 ? -3 : null,
                    right: index % 2 == 1 ? -3 : null,
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        border: Border(
                          top:
                              index < 2
                                  ? const BorderSide(
                                    color: Colors.white,
                                    width: 4,
                                  )
                                  : BorderSide.none,
                          bottom:
                              index >= 2
                                  ? const BorderSide(
                                    color: Colors.white,
                                    width: 4,
                                  )
                                  : BorderSide.none,
                          left:
                              index % 2 == 0
                                  ? const BorderSide(
                                    color: Colors.white,
                                    width: 4,
                                  )
                                  : BorderSide.none,
                          right:
                              index % 2 == 1
                                  ? const BorderSide(
                                    color: Colors.white,
                                    width: 4,
                                  )
                                  : BorderSide.none,
                        ),
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),
        ),

        // Success message and result
        Positioned(
          bottom: 80,
          left: 20,
          right: 20,
          child: Column(
            children: [
              // Success checkmark
              // Container(
              //   width: 50,
              //   height: 50,
              //   decoration: const BoxDecoration(
              //     color: Colors.green,
              //     shape: BoxShape.circle,
              //   ),
              //   child: const Icon(
              //     Icons.check,
              //     color: Colors.white,
              //     size: 30,
              //   ),
              // ),

              //const SizedBox(height: 16),

              // "Scan Completed" text
              const Text(
                'Scan Completed',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),

              const SizedBox(height: 12),

              // Scanned URL/result
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Text(
                  _scannedData ?? '',
                  style: const TextStyle(color: Colors.black, fontSize: 16),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),

        // Close button
        // Positioned(
        //   top: 15,
        //   right: 15,
        //   child: GestureDetector(
        //     onTap: _handleCameraClose,
        //     child: Container(
        //       width: 36,
        //       height: 36,
        //       decoration: BoxDecoration(
        //         color: Colors.black54,
        //         shape: BoxShape.circle,
        //       ),
        //       child: const Icon(
        //         Icons.close,
        //         color: Colors.white,
        //         size: 24,
        //       ),
        //     ),
        //   ),
        // ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget content;

    switch (_currentState) {
      case QrScannerState.defaultState:
      case QrScannerState.hover:
        content = _buildDefaultState();
        break;
      case QrScannerState.scanning:
        content = _buildScanningState();
        break;
      case QrScannerState.scanned:
        content = _buildScannedState();
        break;
      case QrScannerState.scanCompleted:
        content = _buildScanCompletedState();
        break;
    }

    // Add hover detection
    return MouseRegion(
      onEnter: (_) => _handleHover(true),
      onExit: (_) => _handleHover(false),
      child: content,
    );
  }
}

/// Custom painter for dotted border
class DottedBorderPainter extends CustomPainter {
  final Color color;
  final double borderRadius;

  DottedBorderPainter({required this.color, required this.borderRadius});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    const double dashWidth = 5.0;
    const double dashSpace = 5.0;

    final RRect rrect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Radius.circular(borderRadius),
    );

    final Path path = Path()..addRRect(rrect);

    _drawDashedPath(canvas, path, paint, dashWidth, dashSpace);
  }

  void _drawDashedPath(
    Canvas canvas,
    Path path,
    Paint paint,
    double dashWidth,
    double dashSpace,
  ) {
    // Simple dotted border implementation
    final Rect rect = Rect.fromLTWH(
      0,
      0,
      path.getBounds().width,
      path.getBounds().height,
    );

    // Draw top border
    _drawDashedLine(
      canvas,
      paint,
      Offset(rect.left, rect.top),
      Offset(rect.right, rect.top),
      dashWidth,
      dashSpace,
    );

    // Draw right border
    _drawDashedLine(
      canvas,
      paint,
      Offset(rect.right, rect.top),
      Offset(rect.right, rect.bottom),
      dashWidth,
      dashSpace,
    );

    // Draw bottom border
    _drawDashedLine(
      canvas,
      paint,
      Offset(rect.right, rect.bottom),
      Offset(rect.left, rect.bottom),
      dashWidth,
      dashSpace,
    );

    // Draw left border
    _drawDashedLine(
      canvas,
      paint,
      Offset(rect.left, rect.bottom),
      Offset(rect.left, rect.top),
      dashWidth,
      dashSpace,
    );
  }

  void _drawDashedLine(
    Canvas canvas,
    Paint paint,
    Offset start,
    Offset end,
    double dashWidth,
    double dashSpace,
  ) {
    final double totalDistance = (end - start).distance;
    final Offset direction = (end - start) / totalDistance;

    double currentDistance = 0.0;
    while (currentDistance < totalDistance) {
      final Offset dashStart = start + direction * currentDistance;
      final double remainingDistance = totalDistance - currentDistance;
      final double currentDashWidth =
          dashWidth > remainingDistance ? remainingDistance : dashWidth;
      final Offset dashEnd = dashStart + direction * currentDashWidth;

      canvas.drawLine(dashStart, dashEnd, paint);
      currentDistance += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(DottedBorderPainter oldDelegate) {
    return oldDelegate.color != color ||
        oldDelegate.borderRadius != borderRadius;
  }
}

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 32.0; // Small (768-1024px)
  } else {
    return 32.0; // Default for very small screens
  }
}

double _getResponsiveValueFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 14.0; // Default for very small screens
  }
}

double _getResponsiveIconSize(BuildContext context) {
  final double screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 1920) {
    return 22.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 18.0; // Large
  } else if (screenWidth >= 1280) {
    return 16.0; // Medium
  } else if (screenWidth >= 768) {
    return 14.0; // Small
  } else {
    return 14.0; // Extra Small (fallback for very small screens)
  }
}
