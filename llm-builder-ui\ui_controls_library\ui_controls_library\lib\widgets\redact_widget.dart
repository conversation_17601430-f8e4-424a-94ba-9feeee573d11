import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension RedactColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// Redaction styles for the RedactWidget
enum RedactStyle {
  /// Solid block (█████)
  block,

  /// Asterisks (*****)
  asterisks,

  /// Dots (•••••)
  dots,

  /// X characters (xxxxx)
  x,

  /// Dashes (-----)
  dashes,

  /// Blur effect
  blur,

  /// Custom character
  custom
}

/// A comprehensive redact widget that masks sensitive text with various redaction styles.
class RedactWidget extends StatefulWidget {
  // Content properties
  final String text;
  final List<String>? sensitiveWords;
  final bool redactAll;
  final bool caseSensitive;
  final bool redactNumbers;
  final bool redactEmails;
  final bool redactPhoneNumbers;
  final bool redactAddresses;
  final bool redactDates;
  final bool redactCreditCards;
  final bool redactSocialSecurity;
  final bool redactPassports;
  final bool redactLicenses;
  final bool redactIDs;
  final bool redactCustomPatterns;
  final List<RegExp>? customPatterns;

  // Redaction properties
  final RedactStyle redactStyle;
  final String customRedactChar;
  final double blurRadius;
  final bool preserveLength;
  final bool preserveCase;
  final bool preserveFirstChar;
  final bool preserveLastChar;
  final int preserveFirstNChars;
  final int preserveLastNChars;
  final bool showTooltip;
  final String? tooltipText;
  final bool revealOnTap;
  final bool revealOnLongPress;
  final bool revealOnHover;
  final bool revealOnDoubleTap;

  // Appearance properties
  final Color textColor;
  final Color redactedColor;
  final Color backgroundColor;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isItalic;
  final String? fontFamily;
  final bool hasShadow;
  final double elevation;
  final TextAlign textAlign;
  final bool selectable;

  // Layout properties
  final double? width;
  final double? height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double borderRadius;
  final bool hasBorder;
  final Color borderColor;
  final double borderWidth;

  // Behavior properties
  final bool isDisabled;
  final bool copyable;
  final bool showCopyButton;
  final String? copyButtonTooltip;
  final bool hasAnimation;
  final Duration animationDuration;
  final Curve animationCurve;

  // Callbacks
  final Function(String)? onTap;
  final Function(String)? onLongPress;
  final Function(String)? onCopy;
  final Function(bool)? onRevealChanged;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Redact-specific JSON configuration
  /// Whether to use JSON redact configuration
  final bool useJsonRedactConfig;

  /// Redact-specific JSON configuration
  final Map<String, dynamic>? redactConfig;

  const RedactWidget({
    super.key,
    required this.text,
    this.sensitiveWords,
    this.redactAll = false,
    this.caseSensitive = false,
    this.redactNumbers = false,
    this.redactEmails = false,
    this.redactPhoneNumbers = false,
    this.redactAddresses = false,
    this.redactDates = false,
    this.redactCreditCards = false,
    this.redactSocialSecurity = false,
    this.redactPassports = false,
    this.redactLicenses = false,
    this.redactIDs = false,
    this.redactCustomPatterns = false,
    this.customPatterns,
    this.redactStyle = RedactStyle.block,
    this.customRedactChar = '█',
    this.blurRadius = 5.0,
    this.preserveLength = true,
    this.preserveCase = false,
    this.preserveFirstChar = false,
    this.preserveLastChar = false,
    this.preserveFirstNChars = 0,
    this.preserveLastNChars = 0,
    this.showTooltip = false,
    this.tooltipText,
    this.revealOnTap = false,
    this.revealOnLongPress = false,
    this.revealOnHover = false,
    this.revealOnDoubleTap = false,
    this.textColor = Colors.black,
    this.redactedColor = Colors.black,
    this.backgroundColor = Colors.transparent,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isItalic = false,
    this.fontFamily,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.textAlign = TextAlign.start,
    this.selectable = true,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(8.0),
    this.margin = EdgeInsets.zero,
    this.borderRadius = 4.0,
    this.hasBorder = false,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.isDisabled = false,
    this.copyable = false,
    this.showCopyButton = false,
    this.copyButtonTooltip,
    this.hasAnimation = false,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    this.onTap,
    this.onLongPress,
    this.onCopy,
    this.onRevealChanged,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.focusColor,
    this.enableFeedback = true,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Redact-specific JSON configuration
    this.useJsonRedactConfig = false,
    this.redactConfig,
  });

  /// Creates a RedactWidget from a JSON map
  ///
  /// This factory constructor allows for creating a RedactWidget from a JSON map,
  /// making it easy to configure the widget from API responses or configuration files.
  factory RedactWidget.fromJson(Map<String, dynamic> json) {
    // Parse redact style
    RedactStyle redactStyle = RedactStyle.block;
    if (json.containsKey('redactStyle')) {
      final String styleStr = json['redactStyle'].toString().toLowerCase();
      if (styleStr == 'asterisks') {
        redactStyle = RedactStyle.asterisks;
      } else if (styleStr == 'dots') {
        redactStyle = RedactStyle.dots;
      } else if (styleStr == 'x') {
        redactStyle = RedactStyle.x;
      } else if (styleStr == 'dashes') {
        redactStyle = RedactStyle.dashes;
      } else if (styleStr == 'blur') {
        redactStyle = RedactStyle.blur;
      } else if (styleStr == 'custom') {
        redactStyle = RedactStyle.custom;
      }
    }

    // Parse text alignment
    TextAlign textAlign = TextAlign.start;
    if (json.containsKey('textAlign')) {
      final String alignStr = json['textAlign'].toString().toLowerCase();
      if (alignStr == 'center') {
        textAlign = TextAlign.center;
      } else if (alignStr == 'end') {
        textAlign = TextAlign.end;
      } else if (alignStr == 'justify') {
        textAlign = TextAlign.justify;
      } else if (alignStr == 'left') {
        textAlign = TextAlign.left;
      } else if (alignStr == 'right') {
        textAlign = TextAlign.right;
      }
    }

    // Parse font weight
    FontWeight fontWeight = FontWeight.normal;
    if (json.containsKey('fontWeight')) {
      if (json['fontWeight'] is String) {
        switch ((json['fontWeight'] as String).toLowerCase()) {
          case 'thin': fontWeight = FontWeight.w100; break;
          case 'extralight': fontWeight = FontWeight.w200; break;
          case 'light': fontWeight = FontWeight.w300; break;
          case 'regular': fontWeight = FontWeight.w400; break;
          case 'medium': fontWeight = FontWeight.w500; break;
          case 'semibold': fontWeight = FontWeight.w600; break;
          case 'bold': fontWeight = FontWeight.w700; break;
          case 'extrabold': fontWeight = FontWeight.w800; break;
          case 'black': fontWeight = FontWeight.w900; break;
        }
      } else if (json['fontWeight'] is int) {
        final int weight = json['fontWeight'] as int;
        switch (weight) {
          case 100: fontWeight = FontWeight.w100; break;
          case 200: fontWeight = FontWeight.w200; break;
          case 300: fontWeight = FontWeight.w300; break;
          case 400: fontWeight = FontWeight.w400; break;
          case 500: fontWeight = FontWeight.w500; break;
          case 600: fontWeight = FontWeight.w600; break;
          case 700: fontWeight = FontWeight.w700; break;
          case 800: fontWeight = FontWeight.w800; break;
          case 900: fontWeight = FontWeight.w900; break;
        }
      }
    }

    // Parse colors
    Color textColor = Colors.black;
    if (json.containsKey('textColor')) {
      textColor = _parseColor(json['textColor']);
    }

    Color redactedColor = Colors.black;
    if (json.containsKey('redactedColor')) {
      redactedColor = _parseColor(json['redactedColor']);
    }

    Color backgroundColor = Colors.transparent;
    if (json.containsKey('backgroundColor')) {
      backgroundColor = _parseColor(json['backgroundColor']);
    }

    Color borderColor = Colors.grey;
    if (json.containsKey('borderColor')) {
      borderColor = _parseColor(json['borderColor']);
    }

    Color? focusColor;
    if (json.containsKey('focusColor')) {
      focusColor = _parseColor(json['focusColor']);
    }

    // Parse padding and margin
    EdgeInsetsGeometry padding = const EdgeInsets.all(8.0);
    if (json.containsKey('padding')) {
      padding = _parseEdgeInsets(json['padding']);
    }

    EdgeInsetsGeometry margin = EdgeInsets.zero;
    if (json.containsKey('margin')) {
      margin = _parseEdgeInsets(json['margin']);
    }

    // Parse animation curve
    Curve animationCurve = Curves.easeInOut;
    if (json.containsKey('animationCurve')) {
      final String curveStr = json['animationCurve'].toString().toLowerCase();
      if (curveStr == 'linear') {
        animationCurve = Curves.linear;
      } else if (curveStr == 'decelerate') {
        animationCurve = Curves.decelerate;
      } else if (curveStr == 'ease') {
        animationCurve = Curves.ease;
      } else if (curveStr == 'easein') {
        animationCurve = Curves.easeIn;
      } else if (curveStr == 'easeout') {
        animationCurve = Curves.easeOut;
      } else if (curveStr == 'elasticin') {
        animationCurve = Curves.elasticIn;
      } else if (curveStr == 'elasticout') {
        animationCurve = Curves.elasticOut;
      } else if (curveStr == 'elasticinout') {
        animationCurve = Curves.elasticInOut;
      }
    }

    // Parse animation duration
    Duration animationDuration = const Duration(milliseconds: 300);
    if (json.containsKey('animationDuration')) {
      animationDuration = Duration(milliseconds: json['animationDuration'] as int? ?? 300);
    }

    // Parse sensitive words
    List<String>? sensitiveWords;
    if (json.containsKey('sensitiveWords') && json['sensitiveWords'] is List) {
      sensitiveWords = (json['sensitiveWords'] as List)
          .map((word) => word.toString())
          .toList();
    }

    // Parse custom patterns
    List<RegExp>? customPatterns;
    if (json.containsKey('customPatterns') && json['customPatterns'] is List) {
      customPatterns = (json['customPatterns'] as List)
          .map((pattern) => RegExp(pattern.toString()))
          .toList();
    }

    return RedactWidget(
      // Content properties
      text: json['text'] as String? ?? '',
      sensitiveWords: sensitiveWords,
      redactAll: json['redactAll'] as bool? ?? false,
      caseSensitive: json['caseSensitive'] as bool? ?? false,
      redactNumbers: json['redactNumbers'] as bool? ?? false,
      redactEmails: json['redactEmails'] as bool? ?? false,
      redactPhoneNumbers: json['redactPhoneNumbers'] as bool? ?? false,
      redactAddresses: json['redactAddresses'] as bool? ?? false,
      redactDates: json['redactDates'] as bool? ?? false,
      redactCreditCards: json['redactCreditCards'] as bool? ?? false,
      redactSocialSecurity: json['redactSocialSecurity'] as bool? ?? false,
      redactPassports: json['redactPassports'] as bool? ?? false,
      redactLicenses: json['redactLicenses'] as bool? ?? false,
      redactIDs: json['redactIDs'] as bool? ?? false,
      redactCustomPatterns: json['redactCustomPatterns'] as bool? ?? false,
      customPatterns: customPatterns,

      // Redaction properties
      redactStyle: redactStyle,
      customRedactChar: json['customRedactChar'] as String? ?? '█',
      blurRadius: json['blurRadius'] != null ? (json['blurRadius'] as num).toDouble() : 5.0,
      preserveLength: json['preserveLength'] as bool? ?? true,
      preserveCase: json['preserveCase'] as bool? ?? false,
      preserveFirstChar: json['preserveFirstChar'] as bool? ?? false,
      preserveLastChar: json['preserveLastChar'] as bool? ?? false,
      preserveFirstNChars: json['preserveFirstNChars'] as int? ?? 0,
      preserveLastNChars: json['preserveLastNChars'] as int? ?? 0,
      showTooltip: json['showTooltip'] as bool? ?? false,
      tooltipText: json['tooltipText'] as String?,
      revealOnTap: json['revealOnTap'] as bool? ?? false,
      revealOnLongPress: json['revealOnLongPress'] as bool? ?? false,
      revealOnHover: json['revealOnHover'] as bool? ?? false,
      revealOnDoubleTap: json['revealOnDoubleTap'] as bool? ?? false,

      // Appearance properties
      textColor: textColor,
      redactedColor: redactedColor,
      backgroundColor: backgroundColor,
      fontSize: json['fontSize'] != null ? (json['fontSize'] as num).toDouble() : 16.0,
      fontWeight: fontWeight,
      isItalic: json['isItalic'] as bool? ?? false,
      fontFamily: json['fontFamily'] as String?,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation: json['elevation'] != null ? (json['elevation'] as num).toDouble() : 2.0,
      textAlign: textAlign,
      selectable: json['selectable'] as bool? ?? true,

      // Layout properties
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height: json['height'] != null ? (json['height'] as num).toDouble() : null,
      padding: padding,
      margin: margin,
      borderRadius: json['borderRadius'] != null ? (json['borderRadius'] as num).toDouble() : 4.0,
      hasBorder: json['hasBorder'] as bool? ?? false,
      borderColor: borderColor,
      borderWidth: json['borderWidth'] != null ? (json['borderWidth'] as num).toDouble() : 1.0,

      // Behavior properties
      isDisabled: json['isDisabled'] as bool? ?? false,
      copyable: json['copyable'] as bool? ?? false,
      showCopyButton: json['showCopyButton'] as bool? ?? false,
      copyButtonTooltip: json['copyButtonTooltip'] as String?,
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      animationDuration: animationDuration,
      animationCurve: animationCurve,

      // Advanced interaction properties
      autofocus: json['autofocus'] as bool? ?? false,
      focusColor: focusColor,
      enableFeedback: json['enableFeedback'] as bool? ?? true,

      // Accessibility properties
      semanticsLabel: json['semanticsLabel'] as String?,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,

      // JSON configuration properties
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      useJsonRedactConfig: json['useJsonRedactConfig'] as bool? ?? false,
      jsonConfig: json,
      jsonCallbacks: json.containsKey('callbacks') ? json['callbacks'] as Map<String, dynamic> : null,
    );
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'transparent': return Colors.transparent;
          default: return Colors.black;
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a = colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return Colors.black; // Default color
  }

  /// Parses edge insets from a map or number
  static EdgeInsetsGeometry _parseEdgeInsets(dynamic value) {
    if (value is num) {
      return EdgeInsets.all(value.toDouble());
    } else if (value is Map) {
      if (value.containsKey('all')) {
        return EdgeInsets.all((value['all'] as num).toDouble());
      } else if (value.containsKey('horizontal') || value.containsKey('vertical')) {
        return EdgeInsets.symmetric(
          horizontal: value.containsKey('horizontal') ? (value['horizontal'] as num).toDouble() : 0.0,
          vertical: value.containsKey('vertical') ? (value['vertical'] as num).toDouble() : 0.0,
        );
      } else {
        return EdgeInsets.fromLTRB(
          value.containsKey('left') ? (value['left'] as num).toDouble() : 0.0,
          value.containsKey('top') ? (value['top'] as num).toDouble() : 0.0,
          value.containsKey('right') ? (value['right'] as num).toDouble() : 0.0,
          value.containsKey('bottom') ? (value['bottom'] as num).toDouble() : 0.0,
        );
      }
    }
    return const EdgeInsets.all(0.0); // Default padding
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Content properties
      'text': text,
      'sensitiveWords': sensitiveWords,
      'redactAll': redactAll,
      'caseSensitive': caseSensitive,
      'redactNumbers': redactNumbers,
      'redactEmails': redactEmails,
      'redactPhoneNumbers': redactPhoneNumbers,
      'redactAddresses': redactAddresses,
      'redactDates': redactDates,
      'redactCreditCards': redactCreditCards,
      'redactSocialSecurity': redactSocialSecurity,
      'redactPassports': redactPassports,
      'redactLicenses': redactLicenses,
      'redactIDs': redactIDs,
      'redactCustomPatterns': redactCustomPatterns,

      // Redaction properties
      'redactStyle': redactStyle.toString().split('.').last,
      'customRedactChar': customRedactChar,
      'blurRadius': blurRadius,
      'preserveLength': preserveLength,
      'preserveCase': preserveCase,
      'preserveFirstChar': preserveFirstChar,
      'preserveLastChar': preserveLastChar,
      'preserveFirstNChars': preserveFirstNChars,
      'preserveLastNChars': preserveLastNChars,
      'showTooltip': showTooltip,
      'tooltipText': tooltipText,
      'revealOnTap': revealOnTap,
      'revealOnLongPress': revealOnLongPress,
      'revealOnHover': revealOnHover,
      'revealOnDoubleTap': revealOnDoubleTap,

      // Appearance properties
      'textColor': '#${textColor.toHexString()}',
      'redactedColor': '#${redactedColor.toHexString()}',
      'backgroundColor': '#${backgroundColor.toHexString()}',
      'fontSize': fontSize,
      'fontWeight': fontWeight.index * 100 + 100,
      'isItalic': isItalic,
      'fontFamily': fontFamily,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'textAlign': textAlign.toString().split('.').last,
      'selectable': selectable,

      // Layout properties
      'width': width,
      'height': height,
      'padding': _edgeInsetsToJson(padding),
      'margin': _edgeInsetsToJson(margin),
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'borderColor': '#${borderColor.toHexString()}',
      'borderWidth': borderWidth,

      // Behavior properties
      'isDisabled': isDisabled,
      'copyable': copyable,
      'showCopyButton': showCopyButton,
      'copyButtonTooltip': copyButtonTooltip,
      'hasAnimation': hasAnimation,
      'animationDuration': animationDuration.inMilliseconds,
      'animationCurve': _curveToString(animationCurve),

      // Advanced interaction properties
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration properties
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonRedactConfig': useJsonRedactConfig,
    };
  }

  /// Converts EdgeInsetsGeometry to a JSON representation
  static Map<String, dynamic> _edgeInsetsToJson(EdgeInsetsGeometry edgeInsets) {
    if (edgeInsets is EdgeInsets) {
      if (edgeInsets.left == edgeInsets.top && edgeInsets.left == edgeInsets.right && edgeInsets.left == edgeInsets.bottom) {
        return {'all': edgeInsets.left};
      } else if (edgeInsets.left == edgeInsets.right && edgeInsets.top == edgeInsets.bottom) {
        return {
          'horizontal': edgeInsets.left,
          'vertical': edgeInsets.top,
        };
      } else {
        return {
          'left': edgeInsets.left,
          'top': edgeInsets.top,
          'right': edgeInsets.right,
          'bottom': edgeInsets.bottom,
        };
      }
    }
    return {'all': 0.0}; // Default
  }

  /// Converts Curve to a string representation
  static String _curveToString(Curve curve) {
    if (curve == Curves.linear) return 'linear';
    if (curve == Curves.decelerate) return 'decelerate';
    if (curve == Curves.ease) return 'ease';
    if (curve == Curves.easeIn) return 'easeIn';
    if (curve == Curves.easeOut) return 'easeOut';
    if (curve == Curves.easeInOut) return 'easeInOut';
    if (curve == Curves.elasticIn) return 'elasticIn';
    if (curve == Curves.elasticOut) return 'elasticOut';
    if (curve == Curves.elasticInOut) return 'elasticInOut';
    return 'easeInOut';
  }

  @override
  State<RedactWidget> createState() => _RedactWidgetState();
}

class _RedactWidgetState extends State<RedactWidget> with SingleTickerProviderStateMixin {
  bool _isRevealed = false;
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Regular expressions for detecting sensitive information
  final RegExp _emailRegex = RegExp(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b');
  final RegExp _phoneRegex = RegExp(r'\b(\+\d{1,3}[\s.-]?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}\b');
  final RegExp _creditCardRegex = RegExp(r'\b(?:\d{4}[\s-]?){3}\d{4}\b');
  final RegExp _ssnRegex = RegExp(r'\b\d{3}[\s-]?\d{2}[\s-]?\d{4}\b');
  final RegExp _dateRegex = RegExp(r'\b\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}\b');
  final RegExp _numberRegex = RegExp(r'\b\d+\b');
  final RegExp _addressRegex = RegExp(r'\b\d+\s+[A-Za-z0-9\s,\.]+(?:Avenue|Ave|Street|St|Road|Rd|Boulevard|Blvd|Lane|Ln|Drive|Dr|Way|Court|Ct|Plaza|Square|Sq|Terrace|Ter|Place|Pl)\b', caseSensitive: false);
  final RegExp _passportRegex = RegExp(r'\b[A-Z]{1,2}\d{6,9}\b');
  final RegExp _licenseRegex = RegExp(r'\b[A-Z0-9]{5,13}\b');
  final RegExp _idRegex = RegExp(r'\b[A-Z0-9]{6,12}\b');

  // Interaction state
  bool _isFocused = false;
  late FocusNode _focusNode;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    _animation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: widget.animationCurve,
      ),
    );

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (_isFocused && widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    });
  }

  @override
  void didUpdateWidget(RedactWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update focus node if changed
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode = widget.focusNode ?? _focusNode;
      _focusNode.addListener(_onFocusChange);
    }

    // Update callback state if provided
    if (widget.callbackState != null && widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Process the text to redact sensitive information
    final processedText = _isRevealed ? widget.text : _redactText(widget.text);

    // Create the text style
    final textStyle = TextStyle(
      color: _isRevealed ? widget.textColor : widget.redactedColor,
      fontSize: widget.fontSize,
      fontWeight: widget.fontWeight,
      fontStyle: widget.isItalic ? FontStyle.italic : FontStyle.normal,
      fontFamily: widget.fontFamily,
    );

    // Create the text widget
    Widget textWidget;
    if (widget.selectable) {
      textWidget = SelectableText(
        processedText,
        style: textStyle,
        textAlign: widget.textAlign,
      );
    } else {
      textWidget = Text(
        processedText,
        style: textStyle,
        textAlign: widget.textAlign,
      );
    }

    // Apply blur effect if needed
    if (!_isRevealed && widget.redactStyle == RedactStyle.blur) {
      textWidget = ImageFiltered(
        imageFilter: ImageFilter.blur(
          sigmaX: widget.blurRadius,
          sigmaY: widget.blurRadius,
        ),
        child: textWidget,
      );
    }

    // Add copy button if needed
    if (widget.copyable && widget.showCopyButton) {
      textWidget = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(child: textWidget),
          IconButton(
            icon: const Icon(Icons.copy, size: 18),
            onPressed: widget.isDisabled ? null : _handleCopy,
            tooltip: widget.copyButtonTooltip ?? 'Copy text',
          ),
        ],
      );
    }

    // Create the container
    Widget container = Container(
      width: widget.width,
      height: widget.height,
      padding: widget.padding,
      margin: widget.margin,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(26),
                  blurRadius: widget.elevation,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: textWidget,
    );

    // Apply animation if needed
    if (widget.hasAnimation) {
      container = ScaleTransition(
        scale: _animation,
        child: container,
      );
    }

    // Create the interactive widget
    Widget interactiveWidget = MouseRegion(
      cursor: widget.isDisabled ? SystemMouseCursors.forbidden : SystemMouseCursors.text,
      onEnter: (_) {
        if (widget.revealOnHover && !widget.isDisabled) {
          _toggleReveal(true);
        }
      },
      onExit: (_) {
        if (widget.revealOnHover && !widget.isDisabled) {
          _toggleReveal(false);
        }
      },
      child: GestureDetector(
        onTap: widget.isDisabled
            ? null
            : () {
                if (widget.revealOnTap) {
                  _toggleReveal(!_isRevealed);
                }

                if (widget.hasAnimation) {
                  _animationController.forward().then((_) {
                    _animationController.reverse();
                  });
                }

                // Call standard callback
                if (widget.onTap != null) {
                  widget.onTap!(widget.text);
                }

                // Execute JSON callback if defined
                if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
                    widget.jsonCallbacks!.containsKey('onTap')) {
                  _executeJsonCallback('onTap', widget.text);
                }
              },
        onLongPress: widget.isDisabled
            ? null
            : () {
                if (widget.revealOnLongPress) {
                  _toggleReveal(!_isRevealed);
                }

                // Call standard callback
                if (widget.onLongPress != null) {
                  widget.onLongPress!(widget.text);
                }

                // Execute JSON callback if defined
                if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
                    widget.jsonCallbacks!.containsKey('onLongPress')) {
                  _executeJsonCallback('onLongPress', widget.text);
                }
              },
        onDoubleTap: widget.isDisabled || !widget.revealOnDoubleTap
            ? null
            : () {
                _toggleReveal(!_isRevealed);
              },
        child: container,
      ),
    );

    // Add tooltip if needed
    if (widget.showTooltip) {
      interactiveWidget = Tooltip(
        message: widget.tooltipText ?? (_isRevealed ? 'Click to hide' : 'Click to reveal'),
        child: interactiveWidget,
      );
    }

    Widget content = interactiveWidget;

    // Apply focus handling
    if (widget.autofocus || widget.onFocus != null) {
      content = Focus(
        focusNode: _focusNode,
        autofocus: widget.autofocus,
        onFocusChange: widget.onFocus,
        child: content,
      );
    }

    // Apply accessibility
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      content = Semantics(
        label: widget.semanticsLabel,
        excludeSemantics: false,
        child: content,
      );
    }

    return content;
  }

  void _toggleReveal(bool revealed) {
    if (_isRevealed != revealed) {
      setState(() {
        _isRevealed = revealed;
      });

      // Call standard callback
      if (widget.onRevealChanged != null) {
        widget.onRevealChanged!(_isRevealed);
      }

      // Execute JSON callback if defined
      if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onRevealChanged')) {
        _executeJsonCallback('onRevealChanged', _isRevealed);
      }
    }
  }

  void _handleCopy() {
    final textToCopy = _isRevealed ? widget.text : _redactText(widget.text);
    Clipboard.setData(ClipboardData(text: textToCopy));

    // Call standard callback
    if (widget.onCopy != null) {
      widget.onCopy!(textToCopy);
    }

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onCopy')) {
      _executeJsonCallback('onCopy', textToCopy);
    }

    // Show a snackbar to indicate the text was copied
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${_isRevealed ? 'Original' : 'Redacted'} text copied to clipboard'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  String _redactText(String text) {
    if (widget.redactAll) {
      return _applyRedaction(text);
    }

    String result = text;

    // Redact sensitive words
    if (widget.sensitiveWords != null && widget.sensitiveWords!.isNotEmpty) {
      for (final word in widget.sensitiveWords!) {
        final pattern = widget.caseSensitive
            ? RegExp(r'\b' + RegExp.escape(word) + r'\b')
            : RegExp(r'\b' + RegExp.escape(word) + r'\b', caseSensitive: false);

        result = result.replaceAllMapped(pattern, (match) {
          return _applyRedaction(match.group(0)!);
        });
      }
    }

    // Redact emails
    if (widget.redactEmails) {
      result = result.replaceAllMapped(_emailRegex, (match) {
        return _applyRedaction(match.group(0)!);
      });
    }

    // Redact phone numbers
    if (widget.redactPhoneNumbers) {
      result = result.replaceAllMapped(_phoneRegex, (match) {
        return _applyRedaction(match.group(0)!);
      });
    }

    // Redact credit card numbers
    if (widget.redactCreditCards) {
      result = result.replaceAllMapped(_creditCardRegex, (match) {
        return _applyRedaction(match.group(0)!);
      });
    }

    // Redact social security numbers
    if (widget.redactSocialSecurity) {
      result = result.replaceAllMapped(_ssnRegex, (match) {
        return _applyRedaction(match.group(0)!);
      });
    }

    // Redact dates
    if (widget.redactDates) {
      result = result.replaceAllMapped(_dateRegex, (match) {
        return _applyRedaction(match.group(0)!);
      });
    }

    // Redact addresses
    if (widget.redactAddresses) {
      result = result.replaceAllMapped(_addressRegex, (match) {
        return _applyRedaction(match.group(0)!);
      });
    }

    // Redact passport numbers
    if (widget.redactPassports) {
      result = result.replaceAllMapped(_passportRegex, (match) {
        return _applyRedaction(match.group(0)!);
      });
    }

    // Redact license numbers
    if (widget.redactLicenses) {
      result = result.replaceAllMapped(_licenseRegex, (match) {
        return _applyRedaction(match.group(0)!);
      });
    }

    // Redact ID numbers
    if (widget.redactIDs) {
      result = result.replaceAllMapped(_idRegex, (match) {
        return _applyRedaction(match.group(0)!);
      });
    }

    // Redact numbers
    if (widget.redactNumbers) {
      result = result.replaceAllMapped(_numberRegex, (match) {
        return _applyRedaction(match.group(0)!);
      });
    }

    // Redact custom patterns
    if (widget.redactCustomPatterns && widget.customPatterns != null) {
      for (final pattern in widget.customPatterns!) {
        result = result.replaceAllMapped(pattern, (match) {
          return _applyRedaction(match.group(0)!);
        });
      }
    }

    return result;
  }

  String _applyRedaction(String text) {
    if (text.isEmpty) {
      return text;
    }

    // Preserve first and last characters if needed
    String firstChars = '';
    String lastChars = '';
    String middleText = text;

    if (widget.preserveFirstChar && text.length > 1) {
      firstChars = text[0];
      middleText = text.substring(1);
    } else if (widget.preserveFirstNChars > 0 && text.length > widget.preserveFirstNChars) {
      firstChars = text.substring(0, widget.preserveFirstNChars);
      middleText = text.substring(widget.preserveFirstNChars);
    }

    if (widget.preserveLastChar && middleText.length > 1) {
      lastChars = middleText[middleText.length - 1];
      middleText = middleText.substring(0, middleText.length - 1);
    } else if (widget.preserveLastNChars > 0 && middleText.length > widget.preserveLastNChars) {
      lastChars = middleText.substring(middleText.length - widget.preserveLastNChars);
      middleText = middleText.substring(0, middleText.length - widget.preserveLastNChars);
    }

    // Apply redaction style
    String redactedMiddle;
    switch (widget.redactStyle) {
      case RedactStyle.block:
        redactedMiddle = widget.preserveLength
            ? '█' * middleText.length
            : '█████';
        break;
      case RedactStyle.asterisks:
        redactedMiddle = widget.preserveLength
            ? '*' * middleText.length
            : '*****';
        break;
      case RedactStyle.dots:
        redactedMiddle = widget.preserveLength
            ? '•' * middleText.length
            : '•••••';
        break;
      case RedactStyle.x:
        redactedMiddle = widget.preserveLength
            ? 'x' * middleText.length
            : 'xxxxx';
        break;
      case RedactStyle.dashes:
        redactedMiddle = widget.preserveLength
            ? '-' * middleText.length
            : '-----';
        break;
      case RedactStyle.blur:
        // For blur style, we still need to replace the text
        // The actual blur effect is applied in the build method
        redactedMiddle = widget.preserveLength
            ? middleText
            : '█████';
        break;
      case RedactStyle.custom:
        redactedMiddle = widget.preserveLength
            ? widget.customRedactChar * middleText.length
            : widget.customRedactChar * 5;
        break;
    }

    // Preserve case if needed
    if (widget.preserveCase && widget.redactStyle != RedactStyle.blur) {
      redactedMiddle = _preserveCase(middleText, redactedMiddle);
    }

    return firstChars + redactedMiddle + lastChars;
  }

  String _preserveCase(String original, String redacted) {
    final result = StringBuffer();

    for (int i = 0; i < original.length && i < redacted.length; i++) {
      if (original[i].toUpperCase() == original[i]) {
        result.write(redacted[i].toUpperCase());
      } else {
        result.write(redacted[i].toLowerCase());
      }
    }

    // If redacted is longer than original, append the rest
    if (redacted.length > original.length) {
      result.write(redacted.substring(original.length));
    }

    return result.toString();
  }
}