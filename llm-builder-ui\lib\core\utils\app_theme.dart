import 'package:flutter/material.dart';
import 'app_colors.dart';

// Light Theme
final ThemeData lightTheme = ThemeData(
  brightness: Brightness.light,
  colorScheme: ColorScheme.light(
    primary: AppColors.primary,
    secondary: AppColors.secondary,
    surface: Colors.white,
    error: AppColors.error,
    onPrimary: AppColors.textDark,
    onSecondary: AppColors.textLight,
    onSurface: AppColors.textLight,
    onError: AppColors.textDark,
  ),
  scaffoldBackgroundColor: AppColors.backgroundLight,
  appBarTheme: const AppBarTheme(
    backgroundColor: AppColors.primary,
    foregroundColor: AppColors.textDark,
    elevation: 0,
  ),
  floatingActionButtonTheme: const FloatingActionButtonThemeData(
    backgroundColor: AppColors.primary,
  ),
  textTheme: TextTheme(
    headlineLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.bold, color: AppColors.primary),
    headlineMedium: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: AppColors.primary),
    titleLarge: TextStyle(fontSize: 20, fontWeight: FontWeight.w600, color: AppColors.textLight),
    bodyLarge: TextStyle(fontSize: 16, color: AppColors.textLight),
    bodyMedium: TextStyle(fontSize: 14, color: AppColors.textSecondaryLight),
    labelLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: AppColors.primary),
  ),
  inputDecorationTheme: const InputDecorationTheme(
    border: OutlineInputBorder(),
    enabledBorder: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.grey),
    ),
    focusedBorder: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.blue),
    ),
    errorBorder: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.red),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.red, width: 2),
    ),
    hoverColor: Colors.transparent,
  ),
);

// Dark Theme
final ThemeData darkTheme = ThemeData(
  brightness: Brightness.dark,
  colorScheme: ColorScheme.dark(
    primary: AppColors.secondary,
    secondary: AppColors.secondary,
    background: AppColors.backgroundDark,
    surface: AppColors.surfaceDark,
    error: AppColors.error,
    onPrimary: AppColors.textDark, // white
    onSecondary: AppColors.textDark, // white
    onBackground: AppColors.textDark, // white
    onSurface: AppColors.textDark, // white
    onError: AppColors.textDark, // white
  ),
  scaffoldBackgroundColor: AppColors.backgroundDark,
  appBarTheme: const AppBarTheme(
    backgroundColor: AppColors.backgroundDark,
    foregroundColor: AppColors.textDark, // white
    elevation: 0,
    iconTheme: IconThemeData(color: AppColors.textDark), // white
    titleTextStyle: TextStyle(color: AppColors.textDark, fontSize: 20, fontWeight: FontWeight.w600),
  ),
  iconTheme: const IconThemeData(color: AppColors.textDark), // white
  floatingActionButtonTheme: const FloatingActionButtonThemeData(
    backgroundColor: AppColors.secondary,
    foregroundColor: AppColors.textDark, // white
  ),
  textTheme: const TextTheme(
    headlineLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.bold, color: AppColors.textDark),
    headlineMedium: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: AppColors.textDark),
    titleLarge: TextStyle(fontSize: 20, fontWeight: FontWeight.w600, color: AppColors.textDark),
    bodyLarge: TextStyle(fontSize: 16, color: AppColors.textDark),
    bodyMedium: TextStyle(fontSize: 14, color: AppColors.textDark),
    labelLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: AppColors.textDark),
  ),
  inputDecorationTheme: InputDecorationTheme(
    border: const OutlineInputBorder(),
    enabledBorder: OutlineInputBorder(
      borderSide: BorderSide(color: AppColors.textDark),
    ),
    focusedBorder: OutlineInputBorder(
      borderSide: BorderSide(color: AppColors.textDark, width: 2),
    ),
    errorBorder: const OutlineInputBorder(
      borderSide: BorderSide(color: Colors.red),
    ),
    focusedErrorBorder: const OutlineInputBorder(
      borderSide: BorderSide(color: Colors.red, width: 2),
    ),
    hoverColor: Colors.transparent,
  ),
); 