import 'package:flutter/material.dart';

class CommonButton extends StatelessWidget {
  final IconData? icon;
  final String? text;
  final Widget? child;
  final String tooltip;
  final VoidCallback onPressed;
  final TextStyle? textStyle;
  final Color? backgroundColor;

  const CommonButton({
    super.key,
    this.icon,
    this.text,
    this.child,
    required this.tooltip,
    required this.onPressed,
    this.textStyle,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Tooltip(
      message: tooltip,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          minimumSize: const Size.fromHeight(48), // Set button height
          backgroundColor: backgroundColor ?? (isDark ? Theme.of(context).colorScheme.secondary : Theme.of(context).colorScheme.primary),
          textStyle: textStyle ?? Theme.of(context).textTheme.labelLarge,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: child ?? (text != null ? Text(text!, style: textStyle ?? Theme.of(context).textTheme.labelLarge) : const SizedBox.shrink()),
      ),
    );
  }
} 