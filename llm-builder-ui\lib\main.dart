import 'dart:async';
import 'package:builder_app/app.dart';
import 'package:flutter/material.dart';
import 'package:builder_app/core/utils/environment_helper.dart';

void main() {
  runZonedGuarded<Future<void>>(
    () async {
      // Initialize environment configuration
      await EnvironmentHelper.init();
      runApp(App());
    },
    (error, stackTrace) {
      // Handle uncaught errors here (e.g., log to a service)
      debugPrint('Uncaught error: \n$error\n$stackTrace');
    },
  );
}






