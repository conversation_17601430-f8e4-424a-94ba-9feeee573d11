import 'dart:convert';
import 'dart:developer';

import '../models/user_model.dart';
import '../../../../core/utils/api_client.dart';

abstract class LoginRemoteDataSource {
  Future<UserModel> login({required String email, required String password});
}

class LoginRemoteDataSourceImpl implements LoginRemoteDataSource {
  @override
  Future<UserModel> login({required String email, required String password}) async {
    final response = await ApiClient().post(
      'api/v2/auth/login',
      data: {
        'username': email,
        'password': password,
      },
    );
    if (response.statusCode == 200) {
      log("================= ${jsonEncode(response.data)}");
      return UserModel.fromJson(response.data);
    } else {
      throw Exception(response.data['message'] ?? 'Login failed');
    }
  }
}