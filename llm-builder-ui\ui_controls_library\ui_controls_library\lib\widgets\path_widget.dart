import 'package:flutter/material.dart';
import 'dart:io';

/// A widget that displays and manages file or directory paths.
///
/// This widget allows users to input, select, validate, and display file or directory paths
/// with various configuration options.
class PathWidget extends StatefulWidget {
  /// Initial path value
  final String? initialPath;

  /// Whether the path should be for a file or directory
  final PathType pathType;

  /// Whether to allow path editing
  final bool isReadOnly;

  /// Whether the widget is disabled
  final bool isDisabled;

  /// Whether to show a file/directory picker button
  final bool showPickerButton;

  /// Whether to show a browse button that opens the file/directory in the system explorer
  final bool showBrowseButton;

  /// Whether to validate the path exists
  final bool validatePathExists;

  /// Whether to show a preview of the file/directory (if applicable)
  final bool showPreview;

  /// File extensions to filter (e.g., ['jpg', 'png'])
  final List<String>? allowedExtensions;

  /// Placeholder text for the input field
  final String? hintText;

  /// Label text for the widget
  final String? labelText;

  /// Helper text to display below the input
  final String? helperText;

  /// Error text to display when validation fails
  final String? errorText;

  /// The color of the text
  final Color textColor;

  /// The background color of the widget
  final Color backgroundColor;

  /// The color of the border
  final Color borderColor;

  /// The width of the border
  final double borderWidth;

  /// The radius of the border corners
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The font size for the text
  final double fontSize;

  /// The font weight for the text
  final FontWeight fontWeight;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// The color of the picker button
  final Color pickerButtonColor;

  /// The color of the browse button
  final Color browseButtonColor;

  /// The icon for the picker button
  final IconData pickerButtonIcon;

  /// The icon for the browse button
  final IconData browseButtonIcon;

  /// Callback when the path changes
  final Function(String)? onPathChanged;

  /// Callback when the path is validated
  final Function(bool, String?)? onPathValidated;

  /// Creates a path widget.
  const PathWidget({
    super.key,
    this.initialPath,
    this.pathType = PathType.file,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.showPickerButton = true,
    this.showBrowseButton = true,
    this.validatePathExists = true,
    this.showPreview = false,
    this.allowedExtensions,
    this.hintText,
    this.labelText,
    this.helperText,
    this.errorText,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasBorder = true,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.width,
    this.height,
    this.pickerButtonColor = Colors.blue,
    this.browseButtonColor = Colors.green,
    this.pickerButtonIcon = Icons.folder_open,
    this.browseButtonIcon = Icons.open_in_new,
    this.onPathChanged,
    this.onPathValidated,
  });

  @override
  State<PathWidget> createState() => _PathWidgetState();
}

/// Enum for path types
enum PathType {
  /// File path
  file,

  /// Directory path
  directory,

  /// Either file or directory path
  any
}

class _PathWidgetState extends State<PathWidget> {
  final TextEditingController _pathController = TextEditingController();
  String? _validationError;
  bool _pathExists = false;

  @override
  void initState() {
    super.initState();

    // Initialize with provided path if available
    if (widget.initialPath != null) {
      _pathController.text = widget.initialPath!;
      _validatePath();
    }
  }

  @override
  void dispose() {
    _pathController.dispose();
    super.dispose();
  }

  void _validatePath() {
    final path = _pathController.text.trim();

    if (path.isEmpty) {
      setState(() {
        _validationError = 'Path cannot be empty';
        _pathExists = false;
      });
      _notifyValidation(false, 'Path cannot be empty');
      return;
    }

    // Check if path exists (if validation is enabled)
    if (widget.validatePathExists) {
      bool exists = false;
      String? error;

      try {
        if (widget.pathType == PathType.file) {
          exists = File(path).existsSync();
          if (!exists) {
            error = 'File does not exist';
          }
        } else if (widget.pathType == PathType.directory) {
          exists = Directory(path).existsSync();
          if (!exists) {
            error = 'Directory does not exist';
          }
        } else {
          // PathType.any - check if either file or directory exists
          exists = File(path).existsSync() || Directory(path).existsSync();
          if (!exists) {
            error = 'Path does not exist';
          }
        }
      } catch (e) {
        exists = false;
        error = 'Invalid path format';
      }

      // Check file extension if specified and path is for a file
      if (exists &&
          widget.allowedExtensions != null &&
          widget.allowedExtensions!.isNotEmpty &&
          (widget.pathType == PathType.file ||
           (widget.pathType == PathType.any && File(path).existsSync()))) {

        final extension = path.split('.').last.toLowerCase();
        if (!widget.allowedExtensions!.contains(extension)) {
          exists = false;
          error = 'File extension not allowed. Allowed: ${widget.allowedExtensions!.join(', ')}';
        }
      }

      setState(() {
        _pathExists = exists;
        _validationError = exists ? null : error;
      });

      _notifyValidation(exists, error);
    } else {
      // If validation is disabled, just check basic format
      final bool hasInvalidChars = path.contains(RegExp(r'[<>:"|?*]'));

      setState(() {
        _validationError = hasInvalidChars ? 'Path contains invalid characters' : null;
      });

      _notifyValidation(!hasInvalidChars, hasInvalidChars ? 'Path contains invalid characters' : null);
    }
  }

  void _notifyValidation(bool isValid, String? error) {
    if (widget.onPathValidated != null) {
      widget.onPathValidated!(isValid, error);
    }
  }

  void _notifyPathChanged(String path) {
    if (widget.onPathChanged != null) {
      widget.onPathChanged!(path);
    }
  }

  Future<void> _pickPath() async {
    // This is a placeholder for actual file/directory picking functionality
    // In a real implementation, you would use a file picker plugin

    // For demonstration purposes, we'll just set a sample path
    String samplePath;

    if (widget.pathType == PathType.file) {
      samplePath = '/path/to/sample${widget.allowedExtensions != null && widget.allowedExtensions!.isNotEmpty ? '.${widget.allowedExtensions!.first}' : '.txt'}';
    } else {
      samplePath = '/path/to/directory';
    }

    setState(() {
      _pathController.text = samplePath;
    });

    _validatePath();
    _notifyPathChanged(samplePath);
  }

  void _browsePath() {
    // This is a placeholder for opening the file/directory in system explorer
    // In a real implementation, you would use a platform-specific method to open the file/directory

    // For demonstration purposes, we'll just show a message in debug console
    debugPrint('Opening path: ${_pathController.text}');
  }

  Widget _buildPreview() {
    final path = _pathController.text.trim();

    if (!_pathExists || path.isEmpty) {
      return const Center(
        child: Text('No preview available'),
      );
    }

    try {
      if (File(path).existsSync()) {
        // Check if it's an image file
        final extension = path.split('.').last.toLowerCase();
        if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].contains(extension)) {
          // For demonstration, we'll show a placeholder
          return Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.image, size: 48, color: widget.textColor.withAlpha(128)),
                const SizedBox(height: 8),
                Text(
                  'Image Preview',
                  style: TextStyle(
                    color: widget.textColor,
                    fontSize: widget.fontSize,
                  ),
                ),
              ],
            ),
          );
        } else {
          // For other file types, show file info
          final file = File(path);
          final fileSize = file.lengthSync();
          final formattedSize = _formatFileSize(fileSize);

          return Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.insert_drive_file, size: 48, color: widget.textColor.withAlpha(128)),
                const SizedBox(height: 8),
                Text(
                  'File: ${path.split(Platform.pathSeparator).last}',
                  style: TextStyle(
                    color: widget.textColor,
                    fontSize: widget.fontSize,
                  ),
                ),
                Text(
                  'Size: $formattedSize',
                  style: TextStyle(
                    color: widget.textColor,
                    fontSize: widget.fontSize - 2,
                  ),
                ),
              ],
            ),
          );
        }
      } else if (Directory(path).existsSync()) {
        // For directories, show directory info
        final dir = Directory(path);
        final items = dir.listSync();
        final fileCount = items.whereType<File>().length;
        final dirCount = items.whereType<Directory>().length;

        return Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.folder, size: 48, color: widget.textColor.withAlpha(128)),
              const SizedBox(height: 8),
              Text(
                'Directory: ${path.split(Platform.pathSeparator).last}',
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.fontSize,
                ),
              ),
              Text(
                'Contents: $fileCount files, $dirCount directories',
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.fontSize - 2,
                ),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // Ignore errors in preview generation
    }

    return const Center(
      child: Text('No preview available'),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  @override
  Widget build(BuildContext context) {
    final Color effectiveTextColor = widget.isDisabled
        ? Colors.grey
        : widget.textColor;

    final String effectiveHintText = widget.hintText ??
        (widget.pathType == PathType.file
            ? 'Enter file path'
            : widget.pathType == PathType.directory
                ? 'Enter directory path'
                : 'Enter path');

    return Container(
      width: widget.width,
      height: widget.height,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Label
          if (widget.labelText != null) ...[
            Text(
              widget.labelText!,
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
          ],

          // Path input with buttons
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Path input field
              Expanded(
                child: TextField(
                  controller: _pathController,
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                  ),
                  decoration: InputDecoration(
                    hintText: effectiveHintText,
                    hintStyle: TextStyle(
                      color: effectiveTextColor.withAlpha(128),
                      fontSize: widget.fontSize,
                    ),
                    errorText: _validationError ?? widget.errorText,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    prefixIcon: Icon(
                      widget.pathType == PathType.file
                          ? Icons.insert_drive_file
                          : Icons.folder,
                      color: effectiveTextColor.withAlpha(128),
                    ),
                  ),
                  enabled: !widget.isDisabled && !widget.isReadOnly,
                  onChanged: (value) {
                    _validatePath();
                    _notifyPathChanged(value);
                  },
                  onEditingComplete: _validatePath,
                ),
              ),

              // Picker button
              if (widget.showPickerButton && !widget.isDisabled) ...[
                const SizedBox(width: 8),
                IconButton(
                  onPressed: widget.isDisabled ? null : _pickPath,
                  icon: Icon(widget.pickerButtonIcon),
                  color: widget.pickerButtonColor,
                  tooltip: widget.pathType == PathType.file
                      ? 'Select File'
                      : 'Select Directory',
                ),
              ],

              // Browse button
              if (widget.showBrowseButton && _pathExists && !widget.isDisabled) ...[
                const SizedBox(width: 8),
                IconButton(
                  onPressed: widget.isDisabled ? null : _browsePath,
                  icon: Icon(widget.browseButtonIcon),
                  color: widget.browseButtonColor,
                  tooltip: 'Open in Explorer',
                ),
              ],
            ],
          ),

          // Helper text
          if (widget.helperText != null && _validationError == null) ...[
            const SizedBox(height: 8),
            Text(
              widget.helperText!,
              style: TextStyle(
                color: effectiveTextColor.withAlpha(179),
                fontSize: widget.fontSize - 2,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],

          // Preview
          if (widget.showPreview && _pathExists) ...[
            const SizedBox(height: 16),
            Container(
              height: 150,
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(widget.borderRadius / 2 - widget.borderWidth / 2),
                child: _buildPreview(),
              ),
            ),
          ],
        ],
      ),
    );
  }
}