Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter run --machine --start-paused -d chrome --devtools-server-address http://127.0.0.1:9101/ --target C:\Users\<USER>\OneDrive - Brane Enterprises Pvt Limited\Desktop\llm_builder\llm-builder-ui\lib\main.dart

## exception

StateError: Bad state: No running isolate (inspector is not set).

```
#0      ChromeProxyService.inspector (package:dwds/src/services/chrome_proxy_service.dart:79:7)
#1      _waitForResumeEventToRunMain.<anonymous closure> (package:dwds/src/dwds_vm_client.dart:464:30)
#2      _rootRunUnary (dart:async/zone.dart:1546:13)
#3      _CustomZone.runUnary (dart:async/zone.dart:1429:19)
#4      _CustomZone.runUnaryGuarded (dart:async/zone.dart:1329:7)
#5      _BufferingStreamSubscription._sendData (dart:async/stream_impl.dart:381:11)
#6      _DelayedData.perform (dart:async/stream_impl.dart:573:14)
#7      _PendingEvents.handleNext (dart:async/stream_impl.dart:678:11)
#8      _PendingEvents.schedule.<anonymous closure> (dart:async/stream_impl.dart:649:7)
#9      StackZoneSpecification._run (package:stack_trace/src/stack_zone_specification.dart:207:15)
#10     StackZoneSpecification._registerCallback.<anonymous closure> (package:stack_trace/src/stack_zone_specification.dart:114:48)
#11     _rootRun (dart:async/zone.dart:1517:47)
#12     _CustomZone.run (dart:async/zone.dart:1422:19)
#13     _CustomZone.runGuarded (dart:async/zone.dart:1321:7)
#14     _CustomZone.bindCallbackGuarded.<anonymous closure> (dart:async/zone.dart:1362:23)
#15     StackZoneSpecification._run (package:stack_trace/src/stack_zone_specification.dart:207:15)
#16     StackZoneSpecification._registerCallback.<anonymous closure> (package:stack_trace/src/stack_zone_specification.dart:114:48)
#17     _rootRun (dart:async/zone.dart:1525:13)
#18     _CustomZone.run (dart:async/zone.dart:1422:19)
#19     _CustomZone.runGuarded (dart:async/zone.dart:1321:7)
#20     _CustomZone.bindCallbackGuarded.<anonymous closure> (dart:async/zone.dart:1362:23)
#21     _microtaskLoop (dart:async/schedule_microtask.dart:40:21)
#22     _startMicrotaskLoop (dart:async/schedule_microtask.dart:49:5)
#23     _runPendingImmediateCallback (dart:isolate-patch/isolate_patch.dart:128:13)
#24     _RawReceivePort._handleMessage (dart:isolate-patch/isolate_patch.dart:195:5)
```

## flutter doctor

```
[✓] Flutter (Channel stable, 3.29.3, on Microsoft Windows [Version 10.0.26100.4351], locale en-IN) [815ms]
    • Flutter version 3.29.3 on channel stable at C:\Users\<USER>\Downloads\flutter_windows_3.27.0-stable\flutter
    • Upstream repository https://github.com/flutter/flutter.git
    • Framework revision ea121f8859 (4 months ago), 2025-04-11 19:10:07 +0000
    • Engine revision cf56914b32
    • Dart version 3.7.2
    • DevTools version 2.42.3

[✓] Windows Version (Windows 11 or higher, 24H2, 2009) [4.6s]

[✓] Android toolchain - develop for Android devices (Android SDK version 35.0.1) [5.5s]
    • Android SDK at C:\Users\<USER>\AppData\Local\Android\sdk
    • Platform android-35, build-tools 35.0.1
    • Java binary at: C:\Program Files\Android\Android Studio\jbr\bin\java
      This is the JDK bundled with the latest Android Studio installation on this machine.
      To manually set the JDK path, use: `flutter config --jdk-dir="path/to/jdk"`.
    • Java version OpenJDK Runtime Environment (build 21.0.6+-13368085-b895.109)
    • All Android licenses accepted.

[✓] Chrome - develop for the web [23ms]
    • Chrome at C:\Program Files\Google\Chrome\Application\chrome.exe

[✗] Visual Studio - develop Windows apps [22ms]
    ✗ Visual Studio not installed; this is necessary to develop Windows apps.
      Download at https://visualstudio.microsoft.com/downloads/.
      Please install the "Desktop development with C++" workload, including all of its default components

[✓] Android Studio (version 2024.3.2) [18ms]
    • Android Studio at C:\Program Files\Android\Android Studio
    • Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    • Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    • Java version OpenJDK Runtime Environment (build 21.0.6+-13368085-b895.109)

[✓] VS Code, 64-bit edition (version 1.99.3) [16ms]
    • VS Code at C:\Program Files\Microsoft VS Code
    • Flutter extension version 3.114.0

[✓] Connected device (3 available) [607ms]
    • Windows (desktop) • windows • windows-x64    • Microsoft Windows [Version 10.0.26100.4351]
    • Chrome (web)      • chrome  • web-javascript • Google Chrome 138.0.7204.169
    • Edge (web)        • edge    • web-javascript • Microsoft Edge 138.0.3351.109

[✓] Network resources [699ms]
    • All expected network resources are available.

! Doctor found issues in 1 category.
```
