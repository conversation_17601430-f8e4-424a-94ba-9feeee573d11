import 'package:flutter/material.dart';
import 'dart:math';

/// A widget that calculates and displays quartiles from a dataset.
///
/// This widget allows users to input data values and calculate quartiles
/// (Q1, Q2/median, Q3) with visualization options and additional statistics
/// like IQR (Interquartile Range) and outliers.
class QuartileWidget extends StatefulWidget {
  /// Initial dataset as comma-separated string
  final String? initialDataset;

  /// Whether to show the data distribution visualization
  final bool showDistribution;

  /// Whether to show the quartile markers on the distribution
  final bool showQuartileMarkers;

  /// Whether to show the box plot visualization
  final bool showBoxPlot;

  /// Whether to show the detailed calculation steps
  final bool showCalculationSteps;

  /// Whether to show outliers in the visualization
  final bool showOutliers;

  /// Whether to allow editing of the values
  final bool isReadOnly;

  /// Whether the widget is disabled
  final bool isDisabled;

  /// The title or label for the widget
  final String? title;

  /// Helper text to display below the inputs
  final String? helperText;

  /// Error text to display when there's an input error
  final String? errorText;

  /// The color of the text
  final Color textColor;

  /// The background color of the widget
  final Color backgroundColor;

  /// The color of the border
  final Color borderColor;

  /// The width of the border
  final double borderWidth;

  /// The radius of the border corners
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The font size for the text
  final double fontSize;

  /// The font weight for the text
  final FontWeight fontWeight;

  /// The color for the data points in the visualization
  final Color dataPointColor;

  /// The color for the quartile markers
  final Color quartileMarkerColor;

  /// The color for the box plot
  final Color boxPlotColor;

  /// The color for outliers
  final Color outlierColor;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// Callback when the quartile results change
  final Function(Map<String, double>)? onQuartilesCalculated;

  /// Callback when the dataset changes
  final Function(List<double>)? onDatasetChanged;

  /// Creates a quartile widget.
  const QuartileWidget({
    super.key,
    this.initialDataset,
    this.showDistribution = true,
    this.showQuartileMarkers = true,
    this.showBoxPlot = true,
    this.showCalculationSteps = false,
    this.showOutliers = true,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.title,
    this.helperText,
    this.errorText,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasBorder = true,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.dataPointColor = Colors.blue,
    this.quartileMarkerColor = Colors.red,
    this.boxPlotColor = Colors.green,
    this.outlierColor = Colors.orange,
    this.width,
    this.height,
    this.onQuartilesCalculated,
    this.onDatasetChanged,
  });

  @override
  State<QuartileWidget> createState() => _QuartileWidgetState();
}

class _QuartileWidgetState extends State<QuartileWidget> {
  final TextEditingController _datasetController = TextEditingController();

  List<double> _dataset = [];
  Map<String, double> _quartileResults = {};
  List<double> _outliers = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // Initialize with provided dataset
    if (widget.initialDataset != null) {
      _datasetController.text = widget.initialDataset!;
      _parseDataset();
    }
  }

  @override
  void dispose() {
    _datasetController.dispose();
    super.dispose();
  }

  void _parseDataset() {
    try {
      final input = _datasetController.text.trim();
      if (input.isEmpty) {
        setState(() {
          _dataset = [];
          _errorMessage = 'Please enter data values';
        });
        return;
      }

      // Split by commas, spaces, or semicolons
      final parts = input.split(RegExp(r'[,;\s]+'));
      final parsedData = <double>[];

      for (final part in parts) {
        if (part.trim().isNotEmpty) {
          try {
            parsedData.add(double.parse(part.trim()));
          } catch (e) {
            setState(() {
              _errorMessage = 'Invalid number format: $part';
            });
            return;
          }
        }
      }

      if (parsedData.isEmpty) {
        setState(() {
          _dataset = [];
          _errorMessage = 'No valid data values found';
        });
        return;
      }

      setState(() {
        _dataset = parsedData;
        _errorMessage = null;
      });

      // Notify callback if provided
      if (widget.onDatasetChanged != null) {
        widget.onDatasetChanged!(_dataset);
      }

      _calculateQuartiles();
    } catch (e) {
      setState(() {
        _errorMessage = 'Error parsing dataset: ${e.toString()}';
      });
    }
  }

  void _calculateQuartiles() {
    if (_dataset.isEmpty) {
      setState(() {
        _quartileResults = {};
        _outliers = [];
        _errorMessage = 'No data available for quartile calculation';
      });
      return;
    }

    try {
      // Sort the dataset
      final sortedData = List<double>.from(_dataset)..sort();
      final n = sortedData.length;

      // Calculate quartiles
      final results = <String, double>{};

      // Min (Q0)
      results['min'] = sortedData.first;

      // Q1 (First quartile)
      final q1Position = (n - 1) * 0.25;
      final q1Index = q1Position.floor();
      final q1Fraction = q1Position - q1Index;

      if (q1Fraction == 0) {
        results['q1'] = sortedData[q1Index];
      } else {
        results['q1'] = sortedData[q1Index] + q1Fraction * (sortedData[q1Index + 1] - sortedData[q1Index]);
      }

      // Q2 (Median)
      final q2Position = (n - 1) * 0.5;
      final q2Index = q2Position.floor();
      final q2Fraction = q2Position - q2Index;

      if (q2Fraction == 0) {
        results['median'] = sortedData[q2Index];
      } else {
        results['median'] = sortedData[q2Index] + q2Fraction * (sortedData[q2Index + 1] - sortedData[q2Index]);
      }

      // Q3 (Third quartile)
      final q3Position = (n - 1) * 0.75;
      final q3Index = q3Position.floor();
      final q3Fraction = q3Position - q3Index;

      if (q3Fraction == 0) {
        results['q3'] = sortedData[q3Index];
      } else {
        results['q3'] = sortedData[q3Index] + q3Fraction * (sortedData[q3Index + 1] - sortedData[q3Index]);
      }

      // Max (Q4)
      results['max'] = sortedData.last;

      // Calculate IQR (Interquartile Range)
      final iqr = results['q3']! - results['q1']!;
      results['iqr'] = iqr;

      // Calculate lower and upper fences for outliers
      final lowerFence = results['q1']! - 1.5 * iqr;
      final upperFence = results['q3']! + 1.5 * iqr;
      results['lowerFence'] = lowerFence;
      results['upperFence'] = upperFence;

      // Identify outliers
      final outliers = <double>[];
      for (final value in sortedData) {
        if (value < lowerFence || value > upperFence) {
          outliers.add(value);
        }
      }

      setState(() {
        _quartileResults = results;
        _outliers = outliers;
        _errorMessage = null;
      });

      // Notify callback if provided
      if (widget.onQuartilesCalculated != null) {
        widget.onQuartilesCalculated!(_quartileResults);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error calculating quartiles: ${e.toString()}';
      });
    }
  }

  String _getCalculationSteps() {
    if (_dataset.isEmpty) return 'No data available';
    if (_quartileResults.isEmpty) return 'Quartiles not calculated';

    final sortedData = List<double>.from(_dataset)..sort();
    final n = sortedData.length;

    final buffer = StringBuffer();
    buffer.writeln('Quartile Calculation Steps:');
    buffer.writeln('1. Sort the data: ${sortedData.map((d) => d.toStringAsFixed(2)).join(', ')}');
    buffer.writeln('2. Number of data points: $n');

    // Min (Q0)
    buffer.writeln('3. Minimum (Q0): ${_quartileResults['min']?.toStringAsFixed(4)}');

    // Q1 calculation
    final q1Position = (n - 1) * 0.25;
    final q1Index = q1Position.floor();
    final q1Fraction = q1Position - q1Index;

    buffer.writeln('4. First Quartile (Q1) calculation:');
    buffer.writeln('   - Position: 0.25 × ($n - 1) = $q1Position');

    if (q1Fraction == 0) {
      buffer.writeln('   - Position is an integer, so Q1 = value at index $q1Index = ${sortedData[q1Index]}');
    } else {
      buffer.writeln('   - Position is between indices $q1Index and ${q1Index + 1}');
      buffer.writeln('   - Value at index $q1Index: ${sortedData[q1Index]}');
      buffer.writeln('   - Value at index ${q1Index + 1}: ${sortedData[q1Index + 1]}');
      buffer.writeln('   - Fractional part: $q1Fraction');
      buffer.writeln('   - Q1 = ${sortedData[q1Index]} + $q1Fraction × (${sortedData[q1Index + 1]} - ${sortedData[q1Index]}) = ${_quartileResults['q1']?.toStringAsFixed(4)}');
    }

    // Q2 calculation
    final q2Position = (n - 1) * 0.5;
    final q2Index = q2Position.floor();
    final q2Fraction = q2Position - q2Index;

    buffer.writeln('5. Second Quartile (Q2/Median) calculation:');
    buffer.writeln('   - Position: 0.5 × ($n - 1) = $q2Position');

    if (q2Fraction == 0) {
      buffer.writeln('   - Position is an integer, so Q2 = value at index $q2Index = ${sortedData[q2Index]}');
    } else {
      buffer.writeln('   - Position is between indices $q2Index and ${q2Index + 1}');
      buffer.writeln('   - Value at index $q2Index: ${sortedData[q2Index]}');
      buffer.writeln('   - Value at index ${q2Index + 1}: ${sortedData[q2Index + 1]}');
      buffer.writeln('   - Fractional part: $q2Fraction');
      buffer.writeln('   - Q2 = ${sortedData[q2Index]} + $q2Fraction × (${sortedData[q2Index + 1]} - ${sortedData[q2Index]}) = ${_quartileResults['median']?.toStringAsFixed(4)}');
    }

    // Q3 calculation
    final q3Position = (n - 1) * 0.75;
    final q3Index = q3Position.floor();
    final q3Fraction = q3Position - q3Index;

    buffer.writeln('6. Third Quartile (Q3) calculation:');
    buffer.writeln('   - Position: 0.75 × ($n - 1) = $q3Position');

    if (q3Fraction == 0) {
      buffer.writeln('   - Position is an integer, so Q3 = value at index $q3Index = ${sortedData[q3Index]}');
    } else {
      buffer.writeln('   - Position is between indices $q3Index and ${q3Index + 1}');
      buffer.writeln('   - Value at index $q3Index: ${sortedData[q3Index]}');
      buffer.writeln('   - Value at index ${q3Index + 1}: ${sortedData[q3Index + 1]}');
      buffer.writeln('   - Fractional part: $q3Fraction');
      buffer.writeln('   - Q3 = ${sortedData[q3Index]} + $q3Fraction × (${sortedData[q3Index + 1]} - ${sortedData[q3Index]}) = ${_quartileResults['q3']?.toStringAsFixed(4)}');
    }

    // Max (Q4)
    buffer.writeln('7. Maximum (Q4): ${_quartileResults['max']?.toStringAsFixed(4)}');

    // IQR calculation
    buffer.writeln('8. Interquartile Range (IQR) calculation:');
    buffer.writeln('   - IQR = Q3 - Q1 = ${_quartileResults['q3']?.toStringAsFixed(4)} - ${_quartileResults['q1']?.toStringAsFixed(4)} = ${_quartileResults['iqr']?.toStringAsFixed(4)}');

    // Outlier fences
    buffer.writeln('9. Outlier fences calculation:');
    buffer.writeln('   - Lower fence = Q1 - 1.5 × IQR = ${_quartileResults['q1']?.toStringAsFixed(4)} - 1.5 × ${_quartileResults['iqr']?.toStringAsFixed(4)} = ${_quartileResults['lowerFence']?.toStringAsFixed(4)}');
    buffer.writeln('   - Upper fence = Q3 + 1.5 × IQR = ${_quartileResults['q3']?.toStringAsFixed(4)} + 1.5 × ${_quartileResults['iqr']?.toStringAsFixed(4)} = ${_quartileResults['upperFence']?.toStringAsFixed(4)}');

    // Outliers
    if (_outliers.isEmpty) {
      buffer.writeln('10. No outliers found (all values are within the fences)');
    } else {
      buffer.writeln('10. Outliers (values outside the fences):');
      buffer.writeln('    - ${_outliers.map((d) => d.toStringAsFixed(2)).join(', ')}');
    }

    return buffer.toString();
  }

  @override
  Widget build(BuildContext context) {
    final Color effectiveTextColor = widget.isDisabled
        ? Colors.grey
        : widget.textColor;

    return Container(
      width: widget.width,
      height: widget.height,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title
          if (widget.title != null) ...[
            Text(
              widget.title!,
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize + 2,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Dataset Input
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Dataset:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _datasetController,
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                ),
                decoration: InputDecoration(
                  hintText: 'Enter comma-separated values (e.g., 10, 20, 30, 40, 50)',
                  hintStyle: TextStyle(
                    color: effectiveTextColor.withAlpha(128),
                    fontSize: widget.fontSize,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  errorText: _errorMessage,
                ),
                enabled: !widget.isDisabled && !widget.isReadOnly,
                onChanged: (value) {
                  _parseDataset();
                },
                maxLines: 2,
                minLines: 1,
              ),
            ],
          ),

          // Results
          if (_quartileResults.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Quartile Results:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.backgroundColor.withAlpha(179),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Minimum: ${_quartileResults['min']?.toStringAsFixed(4)}',
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize,
                    ),
                  ),
                  Text(
                    'First Quartile (Q1): ${_quartileResults['q1']?.toStringAsFixed(4)}',
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize,
                    ),
                  ),
                  Text(
                    'Median (Q2): ${_quartileResults['median']?.toStringAsFixed(4)}',
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize,
                    ),
                  ),
                  Text(
                    'Third Quartile (Q3): ${_quartileResults['q3']?.toStringAsFixed(4)}',
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize,
                    ),
                  ),
                  Text(
                    'Maximum: ${_quartileResults['max']?.toStringAsFixed(4)}',
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Interquartile Range (IQR): ${_quartileResults['iqr']?.toStringAsFixed(4)}',
                    style: TextStyle(
                      color: effectiveTextColor,
                      fontSize: widget.fontSize,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (_outliers.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Outliers: ${_outliers.map((d) => d.toStringAsFixed(2)).join(', ')}',
                      style: TextStyle(
                        color: widget.outlierColor,
                        fontSize: widget.fontSize,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],

          // Box Plot Visualization
          if (widget.showBoxPlot && _quartileResults.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Box Plot:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 100,
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: _buildBoxPlot(),
              ),
            ),
          ],

          // Data Distribution Visualization
          if (widget.showDistribution && _dataset.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Data Distribution:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 150,
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: _buildDistributionChart(),
              ),
            ),
          ],

          // Calculation Steps
          if (widget.showCalculationSteps && _quartileResults.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Calculation Steps:',
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.backgroundColor.withAlpha(179),
                borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                border: Border.all(
                  color: widget.borderColor.withAlpha(128),
                  width: widget.borderWidth / 2,
                ),
              ),
              child: Text(
                _getCalculationSteps(),
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize - 1,
                  height: 1.4,
                ),
              ),
            ),
          ],

          // Helper Text
          if (widget.helperText != null) ...[
            const SizedBox(height: 8),
            Text(
              widget.helperText!,
              style: TextStyle(
                color: effectiveTextColor.withAlpha(179),
                fontSize: widget.fontSize - 2,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBoxPlot() {
    if (_quartileResults.isEmpty) {
      return const Center(child: Text('No data to display'));
    }

    return CustomPaint(
      size: const Size(double.infinity, 100),
      painter: _BoxPlotPainter(
        min: _quartileResults['min']!,
        q1: _quartileResults['q1']!,
        median: _quartileResults['median']!,
        q3: _quartileResults['q3']!,
        max: _quartileResults['max']!,
        lowerFence: _quartileResults['lowerFence']!,
        upperFence: _quartileResults['upperFence']!,
        outliers: _outliers,
        boxColor: widget.boxPlotColor,
        medianColor: widget.quartileMarkerColor,
        outlierColor: widget.outlierColor,
        textColor: widget.textColor,
      ),
    );
  }

  Widget _buildDistributionChart() {
    if (_dataset.isEmpty) {
      return const Center(child: Text('No data to display'));
    }

    // Sort the dataset
    final sortedData = List<double>.from(_dataset)..sort();

    return CustomPaint(
      size: const Size(double.infinity, 150),
      painter: _DistributionPainter(
        dataset: sortedData,
        quartiles: _quartileResults.isNotEmpty ? {
          'min': _quartileResults['min']!,
          'q1': _quartileResults['q1']!,
          'median': _quartileResults['median']!,
          'q3': _quartileResults['q3']!,
          'max': _quartileResults['max']!,
        } : null,
        dataPointColor: widget.dataPointColor,
        quartileMarkerColor: widget.quartileMarkerColor,
        showQuartileMarkers: widget.showQuartileMarkers && _quartileResults.isNotEmpty,
        textColor: widget.textColor,
      ),
    );
  }
}

class _BoxPlotPainter extends CustomPainter {
  final double min;
  final double q1;
  final double median;
  final double q3;
  final double max;
  final double lowerFence;
  final double upperFence;
  final List<double> outliers;
  final Color boxColor;
  final Color medianColor;
  final Color outlierColor;
  final Color textColor;

  _BoxPlotPainter({
    required this.min,
    required this.q1,
    required this.median,
    required this.q3,
    required this.max,
    required this.lowerFence,
    required this.upperFence,
    required this.outliers,
    required this.boxColor,
    required this.medianColor,
    required this.outlierColor,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = boxColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final fillPaint = Paint()
      ..color = boxColor.withAlpha(51) // 0.2 * 255 = 51
      ..style = PaintingStyle.fill;

    final medianPaint = Paint()
      ..color = medianColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final whiskerPaint = Paint()
      ..color = boxColor
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final outlierPaint = Paint()
      ..color = outlierColor
      ..strokeWidth = 1.0
      ..style = PaintingStyle.fill;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    // Calculate the range for scaling
    final dataMin = min < lowerFence ? min : lowerFence;
    final dataMax = max > upperFence ? max : upperFence;
    final range = dataMax - dataMin;

    // Ensure range is not zero
    final effectiveRange = range > 0 ? range : 1.0;

    // Calculate positions
    final xMin = 40.0; // Left margin for labels
    final xMax = size.width - 20.0; // Right margin
    final yMiddle = size.height / 2;
    final boxHeight = 30.0;

    // Function to convert data value to x position
    double valueToX(double value) {
      return xMin + (value - dataMin) / effectiveRange * (xMax - xMin);
    }

    // Draw outliers
    for (final value in outliers) {
      final x = valueToX(value);
      canvas.drawCircle(
        Offset(x, yMiddle),
        3.0,
        outlierPaint,
      );
    }

    // Draw whiskers (lines from min/max to Q1/Q3)
    final xLowerWhisker = valueToX(min < lowerFence ? lowerFence : min);
    final xUpperWhisker = valueToX(max > upperFence ? upperFence : max);
    final xQ1 = valueToX(q1);
    final xQ3 = valueToX(q3);

    // Lower whisker
    canvas.drawLine(
      Offset(xLowerWhisker, yMiddle),
      Offset(xQ1, yMiddle),
      whiskerPaint,
    );

    // Upper whisker
    canvas.drawLine(
      Offset(xQ3, yMiddle),
      Offset(xUpperWhisker, yMiddle),
      whiskerPaint,
    );

    // Draw vertical lines at whisker ends
    canvas.drawLine(
      Offset(xLowerWhisker, yMiddle - 10),
      Offset(xLowerWhisker, yMiddle + 10),
      whiskerPaint,
    );

    canvas.drawLine(
      Offset(xUpperWhisker, yMiddle - 10),
      Offset(xUpperWhisker, yMiddle + 10),
      whiskerPaint,
    );

    // Draw the box (Q1 to Q3)
    final boxRect = Rect.fromPoints(
      Offset(xQ1, yMiddle - boxHeight / 2),
      Offset(xQ3, yMiddle + boxHeight / 2),
    );

    canvas.drawRect(boxRect, fillPaint);
    canvas.drawRect(boxRect, paint);

    // Draw the median line
    final xMedian = valueToX(median);
    canvas.drawLine(
      Offset(xMedian, yMiddle - boxHeight / 2),
      Offset(xMedian, yMiddle + boxHeight / 2),
      medianPaint,
    );

    // Draw labels
    void drawLabel(double value, double x, double y, String label) {
      textPainter.text = TextSpan(
        text: '$label: ${value.toStringAsFixed(1)}',
        style: TextStyle(color: textColor, fontSize: 10),
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, y));
    }

    drawLabel(min, xLowerWhisker, yMiddle + boxHeight / 2 + 15, 'Min');
    drawLabel(q1, xQ1, yMiddle - boxHeight / 2 - 15, 'Q1');
    drawLabel(median, xMedian, yMiddle + boxHeight / 2 + 15, 'Median');
    drawLabel(q3, xQ3, yMiddle - boxHeight / 2 - 15, 'Q3');
    drawLabel(max, xUpperWhisker, yMiddle + boxHeight / 2 + 15, 'Max');
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

class _DistributionPainter extends CustomPainter {
  final List<double> dataset;
  final Map<String, double>? quartiles;
  final Color dataPointColor;
  final Color quartileMarkerColor;
  final bool showQuartileMarkers;
  final Color textColor;

  _DistributionPainter({
    required this.dataset,
    this.quartiles,
    required this.dataPointColor,
    required this.quartileMarkerColor,
    required this.showQuartileMarkers,
    required this.textColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (dataset.isEmpty) return;

    final paint = Paint()
      ..color = dataPointColor
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round;



    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    // Calculate min and max for scaling
    final minValue = dataset.reduce(min);
    final maxValue = dataset.reduce(max);
    final range = maxValue - minValue;

    // Ensure range is not zero
    final effectiveRange = range > 0 ? range : 1.0;

    // Draw horizontal axis
    canvas.drawLine(
      Offset(0, size.height - 20),
      Offset(size.width, size.height - 20),
      paint..color = Colors.black54,
    );

    // Draw min and max labels
    textPainter.text = TextSpan(
      text: minValue.toStringAsFixed(1),
      style: TextStyle(color: textColor, fontSize: 10),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(0, size.height - 18));

    textPainter.text = TextSpan(
      text: maxValue.toStringAsFixed(1),
      style: TextStyle(color: textColor, fontSize: 10),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(size.width - textPainter.width, size.height - 18));

    // Draw data points
    for (final value in dataset) {
      final x = (value - minValue) / effectiveRange * size.width;
      canvas.drawCircle(
        Offset(x, size.height - 30),
        3,
        paint..color = dataPointColor,
      );
    }

    // Draw quartile markers
    if (showQuartileMarkers && quartiles != null) {
      void drawMarker(String label, double value, String text) {
        final x = (value - minValue) / effectiveRange * size.width;

        // Draw vertical line
        canvas.drawLine(
          Offset(x, size.height - 40),
          Offset(x, 10),
          paint..color = quartileMarkerColor,
        );

        // Draw label
        textPainter.text = TextSpan(
          text: text,
          style: TextStyle(color: quartileMarkerColor, fontSize: 10, fontWeight: FontWeight.bold),
        );
        textPainter.layout();
        textPainter.paint(canvas, Offset(x - textPainter.width / 2, 15));

        // Draw value
        textPainter.text = TextSpan(
          text: value.toStringAsFixed(1),
          style: TextStyle(color: quartileMarkerColor, fontSize: 10),
        );
        textPainter.layout();
        textPainter.paint(canvas, Offset(x - textPainter.width / 2, 30));
      }

      if (quartiles!.containsKey('min')) {
        drawMarker('min', quartiles!['min']!, 'Min');
      }

      if (quartiles!.containsKey('q1')) {
        drawMarker('q1', quartiles!['q1']!, 'Q1');
      }

      if (quartiles!.containsKey('median')) {
        drawMarker('median', quartiles!['median']!, 'Median');
      }

      if (quartiles!.containsKey('q3')) {
        drawMarker('q3', quartiles!['q3']!, 'Q3');
      }

      if (quartiles!.containsKey('max')) {
        drawMarker('max', quartiles!['max']!, 'Max');
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}