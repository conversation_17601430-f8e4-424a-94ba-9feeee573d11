import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';

/// A widget that calculates and displays percentiles from a dataset.
///
/// This widget allows users to input a dataset and percentile values,
/// and displays the calculated percentile results with optional visualization.
class PercentileWidget extends StatefulWidget {
  /// Initial dataset as a comma-separated string
  final String? initialDataset;

  /// Initial percentile values to calculate (e.g., [25, 50, 75] for quartiles)
  final List<double>? initialPercentiles;

  /// Whether to show a chart visualization of the data and percentiles
  final bool showChart;

  /// Whether to show the result of the percentile calculation
  final bool showResult;

  /// Whether to show detailed calculations
  final bool showDetailedCalculations;

  /// Whether to allow editing of the values
  final bool isReadOnly;

  /// Whether the widget is disabled
  final bool isDisabled;

  /// The title or label for the widget
  final String? title;

  /// Helper text to display below the inputs
  final String? helperText;

  /// Error text to display when there's an input error
  final String? errorText;

  /// The color of the text
  final Color textColor;

  /// The background color of the widget
  final Color backgroundColor;

  /// The color of the border
  final Color borderColor;

  /// The width of the border
  final double borderWidth;

  /// The radius of the border corners
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The font size for the text
  final double fontSize;

  /// The font weight for the text
  final FontWeight fontWeight;

  /// The color of the chart lines for data points
  final Color dataPointColor;

  /// The color of the chart lines for percentile markers
  final Color percentileColor;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// Callback when the percentile values change
  final Function(Map<double, double>)? onPercentilesCalculated;

  /// Callback when the dataset changes
  final Function(List<double>)? onDatasetChanged;

  /// Creates a percentile widget.
  const PercentileWidget({
    super.key,
    this.initialDataset,
    this.initialPercentiles,
    this.showChart = true,
    this.showResult = true,
    this.showDetailedCalculations = false,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.title,
    this.helperText,
    this.errorText,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasBorder = true,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.dataPointColor = Colors.blue,
    this.percentileColor = Colors.red,
    this.width,
    this.height,
    this.onPercentilesCalculated,
    this.onDatasetChanged,
  });

  @override
  State<PercentileWidget> createState() => _PercentileWidgetState();
}

class _PercentileWidgetState extends State<PercentileWidget> {
  final TextEditingController _datasetController = TextEditingController();
  final TextEditingController _percentilesController = TextEditingController();

  List<double> _dataset = [];
  List<double> _percentiles = [25, 50, 75]; // Default to quartiles
  Map<double, double> _percentileResults = {};

  String? _inputError;
  bool _hasCalculated = false;

  @override
  void initState() {
    super.initState();

    // Initialize with provided dataset if available
    if (widget.initialDataset != null) {
      _datasetController.text = widget.initialDataset!;
      _parseDataset();
    }

    // Initialize with provided percentiles if available
    if (widget.initialPercentiles != null && widget.initialPercentiles!.isNotEmpty) {
      _percentiles = List.from(widget.initialPercentiles!);
      _percentilesController.text = _percentiles.map((p) => p.toString()).join(', ');
    } else {
      _percentilesController.text = _percentiles.map((p) => p.toString()).join(', ');
    }

    // Calculate percentiles if dataset is provided
    if (_dataset.isNotEmpty) {
      _calculatePercentiles();
    }
  }

  @override
  void dispose() {
    _datasetController.dispose();
    _percentilesController.dispose();
    super.dispose();
  }

  void _parseDataset() {
    try {
      _dataset = _datasetController.text
          .split(',')
          .map((s) => double.parse(s.trim()))
          .toList();

      setState(() {
        _inputError = null;
      });

      if (widget.onDatasetChanged != null) {
        widget.onDatasetChanged!(_dataset);
      }
    } catch (e) {
      setState(() {
        _inputError = 'Invalid dataset format. Use comma-separated numbers.';
        _dataset = [];
        _percentileResults = {};
        _hasCalculated = false;
      });
    }
  }

  void _parsePercentiles() {
    try {
      final values = _percentilesController.text
          .split(',')
          .map((s) => double.parse(s.trim()))
          .toList();

      // Validate percentile values (must be between 0 and 100)
      for (final p in values) {
        if (p < 0 || p > 100) {
          throw Exception('Percentile values must be between 0 and 100');
        }
      }

      setState(() {
        _percentiles = values;
        _inputError = null;
      });
    } catch (e) {
      setState(() {
        if (e.toString().contains('must be between 0 and 100')) {
          _inputError = 'Percentile values must be between 0 and 100';
        } else {
          _inputError = 'Invalid percentile format. Use comma-separated numbers.';
        }
        // Keep the previous percentiles
      });
    }
  }

  void _calculatePercentiles() {
    if (_dataset.isEmpty) {
      setState(() {
        _inputError = 'Dataset cannot be empty';
        _percentileResults = {};
        _hasCalculated = false;
      });
      return;
    }

    if (_percentiles.isEmpty) {
      setState(() {
        _inputError = 'No percentiles specified';
        _percentileResults = {};
        _hasCalculated = false;
      });
      return;
    }

    // Sort the dataset (required for percentile calculation)
    final sortedData = List<double>.from(_dataset)..sort();

    // Calculate each percentile
    final results = <double, double>{};

    for (final percentile in _percentiles) {
      final result = _calculatePercentile(sortedData, percentile);
      results[percentile] = result;
    }

    setState(() {
      _percentileResults = results;
      _hasCalculated = true;
      _inputError = null;
    });

    if (widget.onPercentilesCalculated != null) {
      widget.onPercentilesCalculated!(_percentileResults);
    }
  }

  double _calculatePercentile(List<double> sortedData, double percentile) {
    if (sortedData.isEmpty) return 0;
    if (sortedData.length == 1) return sortedData[0];

    // Calculate the index
    final n = sortedData.length;
    final rank = (percentile / 100) * (n - 1);

    // Get the integer part and fractional part of the rank
    final intRank = rank.floor();
    final fracRank = rank - intRank;

    // Linear interpolation between the two nearest values
    if (intRank + 1 < n) {
      return sortedData[intRank] + fracRank * (sortedData[intRank + 1] - sortedData[intRank]);
    } else {
      return sortedData[n - 1];
    }
  }

  @override
  Widget build(BuildContext context) {
    final Color effectiveTextColor = widget.isDisabled
        ? Colors.grey
        : widget.textColor;

    return Container(
      width: widget.width,
      height: widget.height,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            if (widget.title != null) ...[
              Text(
                widget.title!,
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize + 2,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Dataset Input
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Dataset',
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                    fontWeight: widget.fontWeight,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: _datasetController,
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Enter comma-separated values (e.g., 10,20,30,40,50)',
                    hintStyle: TextStyle(
                      color: effectiveTextColor.withAlpha(128),
                      fontSize: widget.fontSize,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  enabled: !widget.isDisabled && !widget.isReadOnly,
                  onChanged: (value) {
                    _parseDataset();
                  },
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9,.\-]')),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Percentiles Input
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Percentiles',
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                    fontWeight: widget.fontWeight,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: _percentilesController,
                  style: TextStyle(
                    color: effectiveTextColor,
                    fontSize: widget.fontSize,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Enter comma-separated percentiles (e.g., 25,50,75)',
                    hintStyle: TextStyle(
                      color: effectiveTextColor.withAlpha(128),
                      fontSize: widget.fontSize,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  enabled: !widget.isDisabled && !widget.isReadOnly,
                  onChanged: (value) {
                    _parsePercentiles();
                  },
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9,.]')),
                  ],
                ),
              ],
            ),

            // Calculate Button
            if (!widget.isDisabled && !widget.isReadOnly) ...[
              const SizedBox(height: 16),
              Center(
                child: ElevatedButton(
                  onPressed: _calculatePercentiles,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.dataPointColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                    ),
                  ),
                  child: const Text('Calculate Percentiles'),
                ),
              ),
            ],

            // Error Message
            if (_inputError != null || widget.errorText != null) ...[
              const SizedBox(height: 8),
              Text(
                _inputError ?? widget.errorText!,
                style: TextStyle(
                  color: Colors.red,
                  fontSize: widget.fontSize - 2,
                ),
              ),
            ],

            // Result Display
            if (widget.showResult && _hasCalculated && _percentileResults.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: widget.backgroundColor.withAlpha(179),
                  borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                  border: Border.all(
                    color: widget.borderColor.withAlpha(128),
                    width: widget.borderWidth / 2,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Percentile Results:',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...(_percentileResults.entries.map((entry) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Text(
                          'P${entry.key.toStringAsFixed(1)}: ${entry.value.toStringAsFixed(2)}',
                          style: TextStyle(
                            color: effectiveTextColor,
                            fontSize: widget.fontSize,
                          ),
                        ),
                      );
                    }).toList()),
                  ],
                ),
              ),
            ],

            // Detailed Calculations
            if (widget.showDetailedCalculations && _hasCalculated && _dataset.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'Detailed Calculations:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 150,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: widget.borderColor.withAlpha(128),
                    width: widget.borderWidth / 2,
                  ),
                  borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                ),
                child: ListView(
                  padding: const EdgeInsets.all(8),
                  children: [
                    Text(
                      'Sorted Dataset: ${List<double>.from(_dataset)..sort()..map((v) => v.toStringAsFixed(2)).join(', ')}',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize - 2,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Dataset Size: ${_dataset.length}',
                      style: TextStyle(
                        color: effectiveTextColor,
                        fontSize: widget.fontSize - 2,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...(_percentileResults.entries.map((entry) {
                      final percentile = entry.key;
                      final result = entry.value;
                      final n = _dataset.length;
                      final rank = (percentile / 100) * (n - 1);
                      final intRank = rank.floor();
                      final fracRank = rank - intRank;

                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'P$percentile Calculation:',
                              style: TextStyle(
                                color: effectiveTextColor,
                                fontSize: widget.fontSize - 2,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '  Rank = (percentile / 100) × (n - 1) = ($percentile / 100) × (${_dataset.length} - 1) = $rank',
                              style: TextStyle(
                                color: effectiveTextColor,
                                fontSize: widget.fontSize - 2,
                              ),
                            ),
                            Text(
                              '  Integer Rank = $intRank, Fractional Part = $fracRank',
                              style: TextStyle(
                                color: effectiveTextColor,
                                fontSize: widget.fontSize - 2,
                              ),
                            ),
                            if (intRank + 1 < n)
                              Text(
                                '  Value = data[$intRank] + $fracRank × (data[${intRank + 1}] - data[$intRank]) = ${result.toStringAsFixed(2)}',
                                style: TextStyle(
                                  color: effectiveTextColor,
                                  fontSize: widget.fontSize - 2,
                                ),
                              )
                            else
                              Text(
                                '  Value = data[${n - 1}] = ${result.toStringAsFixed(2)}',
                                style: TextStyle(
                                  color: effectiveTextColor,
                                  fontSize: widget.fontSize - 2,
                                ),
                              ),
                          ],
                        ),
                      );
                    }).toList()),
                  ],
                ),
              ),
            ],

            // Chart
            if (widget.showChart && _hasCalculated && _dataset.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'Data Visualization:',
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: widget.fontSize,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 200,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: widget.borderColor.withAlpha(128),
                    width: widget.borderWidth / 2,
                  ),
                  borderRadius: BorderRadius.circular(widget.borderRadius / 2),
                ),
                child: _buildChart(),
              ),
            ],

            // Helper Text
            if (widget.helperText != null) ...[
              const SizedBox(height: 8),
              Text(
                widget.helperText!,
                style: TextStyle(
                  color: effectiveTextColor.withAlpha(179),
                  fontSize: widget.fontSize - 2,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildChart() {
    // Ensure we have data to display
    if (_dataset.isEmpty) {
      return const Center(
        child: Text('No data to display'),
      );
    }

    // Sort the dataset for visualization
    final sortedData = List<double>.from(_dataset)..sort();

    return CustomPaint(
      size: Size.infinite,
      painter: PercentileChartPainter(
        dataset: sortedData,
        percentileResults: _percentileResults,
        dataPointColor: widget.dataPointColor,
        percentileColor: widget.percentileColor,
        textColor: widget.textColor,
        borderColor: widget.borderColor,
        fontSize: widget.fontSize,
      ),
    );
  }
}

/// Custom painter for Percentile chart
class PercentileChartPainter extends CustomPainter {
  final List<double> dataset;
  final Map<double, double> percentileResults;
  final Color dataPointColor;
  final Color percentileColor;
  final Color textColor;
  final Color borderColor;
  final double fontSize;

  PercentileChartPainter({
    required this.dataset,
    required this.percentileResults,
    required this.dataPointColor,
    required this.percentileColor,
    required this.textColor,
    required this.borderColor,
    required this.fontSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (dataset.isEmpty) return;

    // Setup
    final double width = size.width;
    final double height = size.height;
    final double chartPadding = 40.0;
    final double chartWidth = width - (chartPadding * 2);
    final double chartHeight = height - (chartPadding * 2);

    // Find min and max values for Y axis
    final double minValue = dataset.reduce(min);
    final double maxValue = dataset.reduce(max);

    // Add some padding to the min and max values
    final double yPadding = (maxValue - minValue) * 0.1;
    final double effectiveMinValue = minValue - yPadding;
    final double effectiveMaxValue = maxValue + yPadding;

    // Draw border
    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    canvas.drawRect(
      Rect.fromLTWH(chartPadding, chartPadding, chartWidth, chartHeight),
      borderPaint,
    );

    // Draw grid lines
    final gridPaint = Paint()
      ..color = borderColor.withAlpha(51)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Horizontal grid lines
    final int horizontalLines = 5;
    for (int i = 0; i <= horizontalLines; i++) {
      final double y = chartPadding + (chartHeight / horizontalLines * i);
      canvas.drawLine(
        Offset(chartPadding, y),
        Offset(chartPadding + chartWidth, y),
        gridPaint,
      );

      // Draw Y-axis labels
      final double value = effectiveMaxValue - ((effectiveMaxValue - effectiveMinValue) / horizontalLines * i);
      final textSpan = TextSpan(
        text: value.toStringAsFixed(1),
        style: TextStyle(
          color: textColor,
          fontSize: fontSize - 4,
        ),
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(chartPadding - textPainter.width - 5, y - textPainter.height / 2),
      );
    }

    // Draw histogram-like visualization
    final barWidth = chartWidth / (dataset.length > 1 ? dataset.length : 2);
    final barPaint = Paint()
      ..color = dataPointColor
      ..style = PaintingStyle.fill;

    for (int i = 0; i < dataset.length; i++) {
      final double normalizedHeight = (dataset[i] - effectiveMinValue) / (effectiveMaxValue - effectiveMinValue);
      final double barHeight = normalizedHeight * chartHeight;

      canvas.drawRect(
        Rect.fromLTWH(
          chartPadding + (i * barWidth),
          chartPadding + chartHeight - barHeight,
          barWidth * 0.8,
          barHeight,
        ),
        barPaint,
      );
    }

    // Draw percentile markers
    final percentilePaint = Paint()
      ..color = percentileColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    for (final entry in percentileResults.entries) {
      final double percentile = entry.key;
      final double value = entry.value;

      final double normalizedHeight = (value - effectiveMinValue) / (effectiveMaxValue - effectiveMinValue);
      final double y = chartPadding + chartHeight - (normalizedHeight * chartHeight);

      // Draw horizontal line
      canvas.drawLine(
        Offset(chartPadding, y),
        Offset(chartPadding + chartWidth, y),
        percentilePaint,
      );

      // Draw label
      final textSpan = TextSpan(
        text: 'P${percentile.toStringAsFixed(1)}',
        style: TextStyle(
          color: percentileColor,
          fontSize: fontSize - 2,
          fontWeight: FontWeight.bold,
        ),
      );
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(chartPadding + chartWidth + 5, y - textPainter.height / 2),
      );
    }

    // Draw legend
    final legendY = chartPadding / 2;

    // Data points legend
    final dataPointRect = Rect.fromLTWH(chartPadding, legendY - 5, 10, 10);
    canvas.drawRect(dataPointRect, barPaint);

    final dataPointTextSpan = TextSpan(
      text: 'Data Points',
      style: TextStyle(
        color: textColor,
        fontSize: fontSize - 2,
      ),
    );
    final dataPointTextPainter = TextPainter(
      text: dataPointTextSpan,
      textDirection: TextDirection.ltr,
    );
    dataPointTextPainter.layout();
    dataPointTextPainter.paint(
      canvas,
      Offset(chartPadding + 15, legendY - dataPointTextPainter.height / 2),
    );

    // Percentile legend
    final percentileLegendX = chartPadding + 15 + dataPointTextPainter.width + 20;
    canvas.drawLine(
      Offset(percentileLegendX, legendY),
      Offset(percentileLegendX + 20, legendY),
      percentilePaint,
    );

    final percentileTextSpan = TextSpan(
      text: 'Percentiles',
      style: TextStyle(
        color: textColor,
        fontSize: fontSize - 2,
      ),
    );
    final percentileTextPainter = TextPainter(
      text: percentileTextSpan,
      textDirection: TextDirection.ltr,
    );
    percentileTextPainter.layout();
    percentileTextPainter.paint(
      canvas,
      Offset(percentileLegendX + 25, legendY - percentileTextPainter.height / 2),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}