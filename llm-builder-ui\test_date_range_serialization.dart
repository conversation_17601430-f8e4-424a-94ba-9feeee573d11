import 'package:flutter/material.dart';
import 'dart:convert';
import 'ui_controls_library/ui_controls_library/lib/widgets/date_range_widget.dart';
import 'ui_controls_library/ui_controls_library/lib/widgets/ui_builder/flexible_widget_serializer.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'DateRange Serialization Test',
      home: DateRangeSerializationTest(),
    );
  }
}

class DateRangeSerializationTest extends StatefulWidget {
  @override
  _DateRangeSerializationTestState createState() => _DateRangeSerializationTestState();
}

class _DateRangeSerializationTestState extends State<DateRangeSerializationTest> {
  String jsonOutput = '';
  Widget? deserializedWidget;
  bool testPassed = false;
  String testResult = '';

  @override
  void initState() {
    super.initState();
    _runTest();
  }

  void _runTest() {
    try {
      print("=== Starting DateRange Serialization Test ===");
      
      // Create a DateRangeWidget with specific properties
      final originalWidget = DateRangeWidget(
        initialStartDate: DateTime(2024, 1, 1),
        initialEndDate: DateTime(2024, 1, 31),
        allowDateSelection: true,
        format: DateRangeFormat.standard,
        showWeekday: false,
        showYear: true,
        textColor: Colors.blue,
        backgroundColor: Colors.white,
        fontSize: 16.0,
        borderRadius: 8.0,
        enabled: true,
      );

      // Serialize to JSON
      final serialized = FlexibleWidgetSerializer.serialize(originalWidget);
      final jsonString = const JsonEncoder.withIndent('  ').convert(serialized);
      
      print("=== Serialized JSON ===");
      print(jsonString);
      
      // Deserialize back to widget
      final rebuiltWidget = FlexibleWidgetSerializer.deserialize(serialized);
      
      setState(() {
        jsonOutput = jsonString;
        deserializedWidget = rebuiltWidget;
        testPassed = rebuiltWidget != null && rebuiltWidget is DateRangeWidget;
        testResult = testPassed 
          ? "✅ Test PASSED: DateRangeWidget successfully serialized and deserialized"
          : "❌ Test FAILED: Deserialization did not produce a DateRangeWidget";
      });
      
      print("=== Test Result ===");
      print(testResult);
      
    } catch (e) {
      setState(() {
        testResult = "❌ Test FAILED with error: $e";
        testPassed = false;
      });
      print("=== Test Error ===");
      print(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('DateRange Serialization Test'),
        backgroundColor: testPassed ? Colors.green : Colors.red,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Test Result
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: testPassed ? Colors.green.shade100 : Colors.red.shade100,
                border: Border.all(
                  color: testPassed ? Colors.green : Colors.red,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                testResult,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: testPassed ? Colors.green.shade800 : Colors.red.shade800,
                ),
              ),
            ),
            
            SizedBox(height: 24),
            
            // Original Widget
            if (deserializedWidget != null) ...[
              Text(
                'Deserialized DateRangeWidget:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.blue),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: deserializedWidget!,
              ),
              SizedBox(height: 24),
            ],
            
            // JSON Output
            if (jsonOutput.isNotEmpty) ...[
              Text(
                'JSON Output:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Container(
                width: double.infinity,
                height: 300,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey.shade50,
                ),
                child: SingleChildScrollView(
                  child: SelectableText(
                    jsonOutput,
                    style: TextStyle(fontSize: 12, fontFamily: 'Courier'),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
