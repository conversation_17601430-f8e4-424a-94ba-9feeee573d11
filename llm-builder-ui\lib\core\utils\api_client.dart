import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'app_config.dart';
import 'package:builder_app/core/utils/connectivity_service.dart';
import 'package:builder_app/core/utils/shared_prefs_helper.dart';
import 'package:builder_app/l10n/app_localization.dart';

class ApiClient {
  static final ApiClient _instance = ApiClient._internal();
  late final Dio dio;

  factory ApiClient() {
    return _instance;
  }

  ApiClient._internal() {
    dio = Dio(
      BaseOptions(
        baseUrl: AppConfig.baseUrl,
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        headers: {
          'Accept': 'application/json',
          'App-Version': AppConfig.appVersion,
        },
      ),
    );
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Attach access token if available
        final accessToken = await SharedPrefsHelper().getString('access_token');
        if (accessToken != null && accessToken.isNotEmpty) {
          options.headers['Authorization'] = 'Bearer $accessToken';
        }
        handler.next(options);
      },
      onError: (DioException error, handler) async {
        // If 401, try to refresh token
        if (error.response?.statusCode == 401) {
          final refreshed = await _refreshToken();
          if (refreshed) {
            // Retry the original request with new token
            final newAccessToken = await SharedPrefsHelper().getString('access_token');
            final opts = error.requestOptions;
            opts.headers['Authorization'] = 'Bearer $newAccessToken';
            try {
              final cloneReq = await dio.fetch(opts);
              return handler.resolve(cloneReq);
            } catch (e) {
              return handler.reject(error);
            }
          }
        }
        return handler.next(error);
      },
    ));
  }

  /// Set or update a global header value
  void setHeader(String key, String value) {
    dio.options.headers[key] = value;
  }

  String _fullUrl(String path) {
    if (path.startsWith('http')) {
      return path;
    }
    String base = AppConfig.baseUrl;
    if (base.endsWith('/')) base = base.substring(0, base.length - 1);
    if (!path.startsWith('/')) path = '/$path';
    return base + path;
  }

  Future<Response> get(String path, {Map<String, dynamic>? queryParameters}) async {
    if (!await ConnectivityService.isConnected()) {
      throw Exception(AppLocalizations.getString('common.networkError'));
    }
    try {
      final response = await dio.get(_fullUrl(path), queryParameters: queryParameters);
      _handleStatusCode(response);
      return response;
    } on DioException catch (e) {
      if (e.response != null && e.response?.data != null) {
        _handleStatusCode(e.response!);
        throw Exception(e.response?.data['message'] ?? AppLocalizations.getString('api.requestFailed'));
      } else {
        throw Exception('${AppLocalizations.getString('api.networkError')}: ${e.message}');
      }
    } catch (e) {
      throw Exception('${AppLocalizations.getString('api.unexpectedError')}: $e');
    }
  }

  Future<Response> post(String path, {dynamic data, Map<String, dynamic>? queryParameters, Options? options}) async {
    if (!await ConnectivityService.isConnected()) {
      throw Exception(AppLocalizations.getString('common.networkError'));
    }
    log(jsonEncode(data));
    log(_fullUrl(path));
    try {
      final response = await dio.post(_fullUrl(path), data: data, queryParameters: queryParameters);
      log(response.statusCode.toString());
      _handleStatusCode(response);
      return response;
    } on DioException catch (e) {
      log(e.toString());
      if (e.response != null && e.response?.data != null) {
        _handleStatusCode(e.response!);
        throw Exception(e.response?.data['message'] ?? AppLocalizations.getString('api.requestFailed'));
      } else {
        throw Exception('${AppLocalizations.getString('api.networkError')}: ${e.message}');
      }
    } catch (e) {
      log(e.toString());
      throw Exception('${AppLocalizations.getString('api.unexpectedError')}: $e');
    }
  }

  void _handleStatusCode(Response response) {
    switch (response.statusCode) {
      case 200:
      case 201:
        // Success, do nothing
        break;
      case 400:
        throw Exception('${AppLocalizations.getString('api.badRequest')}: ${response.data['message'] ?? ''}');
      case 401:
        throw Exception('${AppLocalizations.getString('auth.unauthorized')}: ${response.data ?? ''}');
      case 403:
        throw Exception('${AppLocalizations.getString('auth.forbidden')}: ${response.data['message'] ?? ''}');
      case 404:
        throw Exception('${AppLocalizations.getString('api.notFound')}: ${response.data['message'] ?? ''}');
      case 500:
        throw Exception('${AppLocalizations.getString('api.internalServerError')}: ${response.data['message'] ?? ''}');
      default:
        if (response.statusCode != null && response.statusCode! >= 400) {
          throw Exception('${AppLocalizations.getString('api.serverError')} ${response.statusCode}: ${response.data['message'] ?? ''}');
        }
    }
  }

  /// Refresh token logic
  Future<bool> _refreshToken() async {
    final refreshToken = await SharedPrefsHelper().getString('refresh_token');
    if (refreshToken == null || refreshToken.isEmpty) return false;
    try {
      final response = await dio.post(
        'api/v2/auth/refresh',
        data: {'refresh_token': refreshToken},
        options: Options(headers: {'Accept': 'application/json'}),
      );
      if (response.statusCode == 200 && response.data != null) {
        final newAccessToken = response.data['access_token'];
        final newRefreshToken = response.data['refresh_token'];
        if (newAccessToken != null && newRefreshToken != null) {
          await SharedPrefsHelper().setString('access_token', newAccessToken);
          await SharedPrefsHelper().setString('refresh_token', newRefreshToken);
          return true;
        }  
      }
      return false;
    } catch (e) {
      log('${AppLocalizations.getString('api.refreshTokenFailed')}: $e');
      return false;
    }
  }
} 