import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Display format for the byte value
enum ByteDisplayFormat {
  /// Decimal format (e.g., "128")
  decimal,

  /// Hexadecimal format (e.g., "0x80")
  hexadecimal,

  /// Binary format (e.g., "10000000")
  binary
}

/// Input mode for the byte widget
enum ByteInputMode {
  /// Text field input
  textField,

  /// Slider input
  slider,

  /// Buttons input (increment/decrement)
  buttons,

  /// Combined input (text field with buttons)
  combined
}

/// A widget for inputting and displaying byte values (0-255).
///
/// This widget provides extensive customization options for displaying
/// and inputting byte values with various formats and styles.
class ByteWidget extends StatefulWidget {
  /// The initial byte value
  final int initialValue;

  /// The minimum allowed value (must be between 0 and 255)
  final int minValue;

  /// The maximum allowed value (must be between 0 and 255)
  final int maxValue;

  /// The step size for increment/decrement
  final int stepSize;

  /// The display format for the byte value
  final ByteDisplayFormat displayFormat;

  /// The input mode for the byte value
  final ByteInputMode inputMode;

  /// Whether to show the value in uppercase for hexadecimal format
  final bool uppercaseHex;

  /// Whether to show the '0x' prefix for hexadecimal format
  final bool showHexPrefix;

  /// Whether to pad the binary format with leading zeros to 8 digits
  final bool padBinary;

  /// Whether to show the byte value as a percentage (0-100%)
  final bool showAsPercentage;

  /// Whether the widget is read-only
  final bool readOnly;

  /// Whether the widget is disabled
  final bool disabled;

  /// The text color
  final Color textColor;

  /// The background color
  final Color backgroundColor;

  /// The border color
  final Color borderColor;

  /// The border width
  final double borderWidth;

  /// The border radius
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// The font size
  final double fontSize;

  /// The font weight
  final FontWeight fontWeight;

  /// Whether to use a compact layout
  final bool isCompact;

  /// The text alignment
  final TextAlign textAlign;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// Whether to use dark theme colors
  final bool isDarkTheme;

  /// Whether to show a label
  final bool showLabel;

  /// The label text
  final String? label;

  /// Whether to show a prefix
  final bool showPrefix;

  /// The prefix text
  final String? prefix;

  /// Whether to show a suffix
  final bool showSuffix;

  /// The suffix text
  final String? suffix;

  /// The prefix icon
  final IconData? prefixIcon;

  /// The suffix icon
  final IconData? suffixIcon;

  /// The color of the prefix icon
  final Color? prefixIconColor;

  /// The color of the suffix icon
  final Color? suffixIconColor;

  /// Whether to show increment/decrement buttons
  final bool showButtons;

  /// The size of the increment/decrement buttons
  final double buttonSize;

  /// The color of the increment/decrement buttons
  final Color? buttonColor;

  /// The color of the icons in the increment/decrement buttons
  final Color? buttonIconColor;

  /// The icon for the increment button
  final IconData incrementIcon;

  /// The icon for the decrement button
  final IconData decrementIcon;

  /// Whether to show a slider
  final bool showSlider;

  /// The color of the slider
  final Color? sliderColor;

  /// The color of the slider track
  final Color? sliderTrackColor;

  /// Whether to show tick marks on the slider
  final bool showSliderTicks;

  /// The number of divisions for the slider
  final int? sliderDivisions;

  /// Whether to show a clear button
  final bool showClearButton;

  /// Whether to show a tooltip
  final bool showTooltip;

  /// The tooltip text
  final String? tooltipText;

  /// Whether to show a helper text
  final bool showHelperText;

  /// The helper text
  final String? helperText;

  /// The color of the helper text
  final Color? helperTextColor;

  /// Whether to show an error text
  final bool showErrorText;

  /// The error text
  final String? errorText;

  /// The color of the error text
  final Color errorTextColor;

  /// Whether to show the value as a progress indicator
  final bool showAsProgress;

  /// The color of the progress indicator
  final Color? progressColor;

  /// The background color of the progress indicator
  final Color? progressBackgroundColor;

  /// The height of the progress indicator
  final double progressHeight;

  /// Whether to animate value changes
  final bool animateChanges;

  /// The duration of the animation
  final Duration animationDuration;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// The padding inside the widget
  final EdgeInsetsGeometry padding;

  /// The margin around the widget
  final EdgeInsetsGeometry margin;

  /// Callback when the value changes
  final void Function(int)? onChanged;

  /// Callback when the value is submitted
  final void Function(int)? onSubmitted;

  /// Creates a byte widget.
  const ByteWidget({
    super.key,
    this.initialValue = 0,
    this.minValue = 0,
    this.maxValue = 255,
    this.stepSize = 1,
    this.displayFormat = ByteDisplayFormat.decimal,
    this.inputMode = ByteInputMode.combined,
    this.uppercaseHex = true,
    this.showHexPrefix = true,
    this.padBinary = true,
    this.showAsPercentage = false,
    this.readOnly = false,
    this.disabled = false,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.textAlign = TextAlign.center,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.showLabel = false,
    this.label,
    this.showPrefix = false,
    this.prefix,
    this.showSuffix = false,
    this.suffix,
    this.prefixIcon,
    this.suffixIcon,
    this.prefixIconColor,
    this.suffixIconColor,
    this.showButtons = true,
    this.buttonSize = 36.0,
    this.buttonColor,
    this.buttonIconColor,
    this.incrementIcon = Icons.add,
    this.decrementIcon = Icons.remove,
    this.showSlider = false,
    this.sliderColor,
    this.sliderTrackColor,
    this.showSliderTicks = false,
    this.sliderDivisions,
    this.showClearButton = false,
    this.showTooltip = false,
    this.tooltipText,
    this.showHelperText = false,
    this.helperText,
    this.helperTextColor,
    this.showErrorText = false,
    this.errorText,
    this.errorTextColor = Colors.red,
    this.showAsProgress = false,
    this.progressColor,
    this.progressBackgroundColor,
    this.progressHeight = 4.0,
    this.animateChanges = false,
    this.animationDuration = const Duration(milliseconds: 200),
    this.width,
    this.height,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = EdgeInsets.zero,
    this.onChanged,
    this.onSubmitted,
  }) : assert(initialValue >= 0 && initialValue <= 255),
       assert(minValue >= 0 && minValue <= 255),
       assert(maxValue >= 0 && maxValue <= 255),
       assert(minValue <= maxValue);

  @override
  State<ByteWidget> createState() => _ByteWidgetState();
}

class _ByteWidgetState extends State<ByteWidget> with SingleTickerProviderStateMixin {
  late int _value;
  late TextEditingController _controller;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    // Initialize the value
    _value = widget.initialValue.clamp(widget.minValue, widget.maxValue);

    // Initialize the text controller
    _controller = TextEditingController(text: _formatValue(_value));

    // Initialize the animation controller
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    // Initialize the animation
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(ByteWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update the value if the initial value changed
    if (oldWidget.initialValue != widget.initialValue) {
      _setValue(widget.initialValue);
    }

    // Update the text controller if the display format changed
    if (oldWidget.displayFormat != widget.displayFormat ||
        oldWidget.uppercaseHex != widget.uppercaseHex ||
        oldWidget.showHexPrefix != widget.showHexPrefix ||
        oldWidget.padBinary != widget.padBinary ||
        oldWidget.showAsPercentage != widget.showAsPercentage) {
      _controller.text = _formatValue(_value);
    }
  }

  /// Sets the value and updates the text controller
  void _setValue(int newValue) {
    // Clamp the value to the allowed range
    final clampedValue = newValue.clamp(widget.minValue, widget.maxValue);

    // Only update if the value changed
    if (_value != clampedValue) {
      setState(() {
        _value = clampedValue;
        _controller.text = _formatValue(_value);
      });

      // Trigger the animation if enabled
      if (widget.animateChanges) {
        _animationController.reset();
        _animationController.forward();
      }

      // Call the onChanged callback
      if (widget.onChanged != null) {
        widget.onChanged!(_value);
      }
    }
  }

  /// Increments the value by the step size
  void _increment() {
    if (widget.readOnly || widget.disabled) return;
    _setValue(_value + widget.stepSize);
  }

  /// Decrements the value by the step size
  void _decrement() {
    if (widget.readOnly || widget.disabled) return;
    _setValue(_value - widget.stepSize);
  }

  /// Clears the value (sets it to the minimum value)
  void _clearValue() {
    if (widget.readOnly || widget.disabled) return;
    _setValue(widget.minValue);
  }

  /// Formats the value according to the display format
  String _formatValue(int value) {
    if (widget.showAsPercentage) {
      // Calculate the percentage
      final percentage = ((value - widget.minValue) / (widget.maxValue - widget.minValue) * 100).round();
      return '$percentage%';
    }

    switch (widget.displayFormat) {
      case ByteDisplayFormat.decimal:
        return value.toString();

      case ByteDisplayFormat.hexadecimal:
        final hex = value.toRadixString(16);
        final paddedHex = hex.padLeft(2, '0');
        final formattedHex = widget.uppercaseHex ? paddedHex.toUpperCase() : paddedHex;
        return widget.showHexPrefix ? '0x$formattedHex' : formattedHex;

      case ByteDisplayFormat.binary:
        final binary = value.toRadixString(2);
        return widget.padBinary ? binary.padLeft(8, '0') : binary;
    }
  }

  /// Parses the text input and returns the corresponding value
  int _parseInput(String text) {
    // Handle percentage input
    if (widget.showAsPercentage && text.endsWith('%')) {
      final percentText = text.substring(0, text.length - 1);
      final percent = int.tryParse(percentText) ?? 0;
      final range = widget.maxValue - widget.minValue;
      return (widget.minValue + (percent / 100 * range)).round().clamp(widget.minValue, widget.maxValue);
    }

    // Handle different formats
    switch (widget.displayFormat) {
      case ByteDisplayFormat.decimal:
        return int.tryParse(text) ?? widget.minValue;

      case ByteDisplayFormat.hexadecimal:
        // Remove the '0x' prefix if present
        final hexText = text.startsWith('0x') ? text.substring(2) : text;
        return int.tryParse(hexText, radix: 16) ?? widget.minValue;

      case ByteDisplayFormat.binary:
        return int.tryParse(text, radix: 2) ?? widget.minValue;
    }
  }

  /// Builds the text field for input
  Widget _buildTextField() {
    return TextField(
      controller: _controller,
      readOnly: widget.readOnly || widget.disabled || widget.inputMode == ByteInputMode.slider,
      enabled: !widget.disabled,
      style: TextStyle(
        color: widget.disabled ? Colors.grey : widget.textColor,
        fontSize: widget.fontSize,
        fontWeight: widget.fontWeight,
      ),
      textAlign: widget.textAlign,
      decoration: InputDecoration(
        filled: true,
        fillColor: widget.backgroundColor,
        contentPadding: widget.isCompact
            ? const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0)
            : widget.padding,
        border: widget.hasBorder
            ? OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(
                  color: widget.borderColor,
                  width: widget.borderWidth,
                ),
              )
            : InputBorder.none,
        enabledBorder: widget.hasBorder
            ? OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(
                  color: widget.borderColor,
                  width: widget.borderWidth,
                ),
              )
            : null,
        disabledBorder: widget.hasBorder
            ? OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(
                  color: Colors.grey,
                  width: widget.borderWidth,
                ),
              )
            : null,
        focusedBorder: widget.hasBorder
            ? OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(
                  color: widget.isDarkTheme ? Colors.white : Colors.blue,
                  width: widget.borderWidth,
                ),
              )
            : null,
        labelText: widget.showLabel ? widget.label : null,
        prefixText: widget.showPrefix ? widget.prefix : null,
        suffixText: widget.showSuffix ? widget.suffix : null,
        prefixIcon: widget.prefixIcon != null
            ? Icon(
                widget.prefixIcon,
                color: widget.prefixIconColor ?? (widget.disabled ? Colors.grey : null),
              )
            : null,
        suffixIcon: widget.showClearButton && !widget.readOnly && !widget.disabled
            ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: _clearValue,
                iconSize: 18.0,
              )
            : widget.suffixIcon != null
                ? Icon(
                    widget.suffixIcon,
                    color: widget.suffixIconColor ?? (widget.disabled ? Colors.grey : null),
                  )
                : null,
      ),
      keyboardType: TextInputType.text,
      inputFormatters: [
        // Use different input formatters based on the display format
        if (widget.displayFormat == ByteDisplayFormat.decimal)
          FilteringTextInputFormatter.digitsOnly,
        if (widget.displayFormat == ByteDisplayFormat.hexadecimal)
          FilteringTextInputFormatter.allow(RegExp(r'[0-9a-fA-F]|0x')),
        if (widget.displayFormat == ByteDisplayFormat.binary)
          FilteringTextInputFormatter.allow(RegExp(r'[01]')),
        if (widget.showAsPercentage)
          FilteringTextInputFormatter.allow(RegExp(r'[0-9]|%')),
      ],
      onChanged: (text) {
        if (widget.readOnly || widget.disabled) return;

        // Parse the input and update the value
        final newValue = _parseInput(text);
        if (newValue >= widget.minValue && newValue <= widget.maxValue) {
          setState(() {
            _value = newValue;
          });

          // Call the onChanged callback
          if (widget.onChanged != null) {
            widget.onChanged!(_value);
          }
        }
      },
      onSubmitted: (text) {
        if (widget.readOnly || widget.disabled) return;

        // Parse the input and update the value
        final newValue = _parseInput(text);
        _setValue(newValue);

        // Update the text field with the formatted value
        _controller.text = _formatValue(_value);

        // Call the onSubmitted callback
        if (widget.onSubmitted != null) {
          widget.onSubmitted!(_value);
        }
      },
    );
  }

  /// Builds the slider for input
  Widget _buildSlider() {
    return Slider(
      value: _value.toDouble(),
      min: widget.minValue.toDouble(),
      max: widget.maxValue.toDouble(),
      divisions: widget.sliderDivisions ?? (widget.maxValue - widget.minValue),
      activeColor: widget.sliderColor ?? (widget.isDarkTheme ? Colors.white : Colors.blue),
      inactiveColor: widget.sliderTrackColor ?? (widget.isDarkTheme ? Colors.grey : Colors.grey.shade300),
      label: _formatValue(_value),
      onChanged: widget.readOnly || widget.disabled
          ? null
          : (double value) {
              _setValue(value.round());
            },
    );
  }

  /// Builds the increment/decrement buttons
  Widget _buildButtons() {
    final effectiveButtonColor = widget.buttonColor ?? (widget.isDarkTheme ? Colors.white : Colors.blue);
    final effectiveIconColor = widget.buttonIconColor ?? (widget.isDarkTheme ? Colors.black : Colors.white);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Decrement button
        SizedBox(
          width: widget.buttonSize,
          height: widget.buttonSize,
          child: IconButton(
            icon: Icon(widget.decrementIcon),
            color: effectiveIconColor,
            padding: EdgeInsets.zero,
            style: IconButton.styleFrom(
              backgroundColor: effectiveButtonColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
              ),
            ),
            onPressed: widget.readOnly || widget.disabled || _value <= widget.minValue
                ? null
                : _decrement,
          ),
        ),

        // Spacer
        const SizedBox(width: 8),

        // Increment button
        SizedBox(
          width: widget.buttonSize,
          height: widget.buttonSize,
          child: IconButton(
            icon: Icon(widget.incrementIcon),
            color: effectiveIconColor,
            padding: EdgeInsets.zero,
            style: IconButton.styleFrom(
              backgroundColor: effectiveButtonColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
              ),
            ),
            onPressed: widget.readOnly || widget.disabled || _value >= widget.maxValue
                ? null
                : _increment,
          ),
        ),
      ],
    );
  }

  /// Builds the progress indicator
  Widget _buildProgressIndicator() {
    final percentage = (_value - widget.minValue) / (widget.maxValue - widget.minValue);

    return Container(
      height: widget.progressHeight,
      decoration: BoxDecoration(
        color: widget.progressBackgroundColor ?? Colors.grey.shade300,
        borderRadius: BorderRadius.circular(widget.progressHeight / 2),
      ),
      child: FractionallySizedBox(
        widthFactor: percentage,
        alignment: Alignment.centerLeft,
        child: Container(
          decoration: BoxDecoration(
            color: widget.progressColor ?? (widget.isDarkTheme ? Colors.white : Colors.blue),
            borderRadius: BorderRadius.circular(widget.progressHeight / 2),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Build the main content based on the input mode
    Widget content;

    switch (widget.inputMode) {
      case ByteInputMode.textField:
        content = _buildTextField();
        break;

      case ByteInputMode.slider:
        content = Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Display the current value
            Text(
              _formatValue(_value),
              style: TextStyle(
                color: widget.disabled ? Colors.grey : widget.textColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
            ),
            const SizedBox(height: 8),
            // Slider for input
            _buildSlider(),
          ],
        );
        break;

      case ByteInputMode.buttons:
        content = Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Decrement button
            SizedBox(
              width: widget.buttonSize,
              height: widget.buttonSize,
              child: IconButton(
                icon: Icon(widget.decrementIcon),
                color: widget.buttonIconColor,
                padding: EdgeInsets.zero,
                style: IconButton.styleFrom(
                  backgroundColor: widget.buttonColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                  ),
                ),
                onPressed: widget.readOnly || widget.disabled || _value <= widget.minValue
                    ? null
                    : _decrement,
              ),
            ),

            // Display the current value
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                _formatValue(_value),
                style: TextStyle(
                  color: widget.disabled ? Colors.grey : widget.textColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
            ),

            // Increment button
            SizedBox(
              width: widget.buttonSize,
              height: widget.buttonSize,
              child: IconButton(
                icon: Icon(widget.incrementIcon),
                color: widget.buttonIconColor,
                padding: EdgeInsets.zero,
                style: IconButton.styleFrom(
                  backgroundColor: widget.buttonColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                  ),
                ),
                onPressed: widget.readOnly || widget.disabled || _value >= widget.maxValue
                    ? null
                    : _increment,
              ),
            ),
          ],
        );
        break;

      case ByteInputMode.combined:
        content = Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Text field for input
            _buildTextField(),

            // Add slider if enabled
            if (widget.showSlider) ...[
              const SizedBox(height: 8),
              _buildSlider(),
            ],

            // Add buttons if enabled
            if (widget.showButtons) ...[
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildButtons(),
                ],
              ),
            ],
          ],
        );
        break;
    }

    // Add progress indicator if enabled
    if (widget.showAsProgress) {
      content = Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          content,
          const SizedBox(height: 8),
          _buildProgressIndicator(),
        ],
      );
    }

    // Apply animation if enabled
    if (widget.animateChanges) {
      content = FadeTransition(
        opacity: _animation,
        child: content,
      );
    }

    // Apply container styling
    content = Container(
      width: widget.width,
      height: widget.height,
      margin: widget.margin,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: content,
    );

    // Apply tooltip if enabled
    if (widget.showTooltip) {
      content = Tooltip(
        message: widget.tooltipText ?? 'Byte value: ${_formatValue(_value)}',
        child: content,
      );
    }

    // Build the final widget with helper/error text
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        content,

        if (widget.showHelperText && widget.helperText != null && !widget.showErrorText) ...[
          const SizedBox(height: 4),
          Text(
            widget.helperText!,
            style: TextStyle(
              color: widget.helperTextColor ?? Colors.grey,
              fontSize: widget.fontSize * 0.8,
            ),
          ),
        ],

        if (widget.showErrorText && widget.errorText != null) ...[
          const SizedBox(height: 4),
          Text(
            widget.errorText!,
            style: TextStyle(
              color: widget.errorTextColor,
              fontSize: widget.fontSize * 0.8,
            ),
          ),
        ],
      ],
    );
  }
}