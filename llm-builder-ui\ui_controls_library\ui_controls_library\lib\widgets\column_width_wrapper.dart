import 'package:flutter/material.dart';

/// A wrapper widget that applies column width specifications to child widgets.
///
/// This widget is used to ensure that UI components respect the column width
/// specifications defined in the design system. It wraps any widget and applies
/// the appropriate width constraints based on the specified column width.
class ColumnWidthWrapper extends StatelessWidget {
  /// The widget to be wrapped
  final Widget child;

  /// The number of columns this widget should span (1-4)
  final int columnWidth;

  /// The total number of columns in the grid
  final int totalColumns;

  /// The spacing between columns
  final double columnSpacing;

  /// Whether to apply a fixed aspect ratio
  final bool applyAspectRatio;

  /// The aspect ratio to apply (width/height)
  final double aspectRatio;

  /// Additional padding to apply
  final EdgeInsetsGeometry padding;

  /// Creates a column width wrapper.
  ///
  /// The [columnWidth] parameter specifies how many columns the widget should span.
  /// The [totalColumns] parameter specifies the total number of columns in the grid.
  /// The default grid has 4 columns, matching the design specification.
  const ColumnWidthWrapper({
    super.key,
    required this.child,
    required this.columnWidth,
    this.totalColumns = 4,
    this.columnSpacing = 12.0,
    this.applyAspectRatio = false,
    this.aspectRatio = 1.2,
    this.padding = EdgeInsets.zero,
  }) : assert(columnWidth > 0 && columnWidth <= totalColumns,
            'columnWidth must be between 1 and totalColumns');

  @override
  Widget build(BuildContext context) {
    // Calculate the width based on the available width and column specifications
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate the width of a single column
        final availableWidth = constraints.maxWidth;
        final totalSpacing = columnSpacing * (totalColumns - 1);
        final singleColumnWidth = (availableWidth - totalSpacing) / totalColumns;
        
        // Calculate the width for this widget based on columnWidth
        final widgetWidth = (singleColumnWidth * columnWidth) + 
                           (columnSpacing * (columnWidth - 1));
        
        // Apply the calculated width
        Widget wrappedWidget = Container(
          width: widgetWidth,
          padding: padding,
          child: child,
        );
        
        // Apply aspect ratio if requested
        if (applyAspectRatio) {
          wrappedWidget = AspectRatio(
            aspectRatio: aspectRatio,
            child: wrappedWidget,
          );
        }
        
        return wrappedWidget;
      },
    );
  }
}
