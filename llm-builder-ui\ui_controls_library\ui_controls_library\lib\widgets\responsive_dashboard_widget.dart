import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:ui';

/// Enum for dashboard section border styles
enum DashboardBorderStyle {
  /// Solid border
  solid,

  /// Dashed border
  dashed,

  /// Dotted border
  dotted,

  /// No border
  none,
}

/// Enum for dashboard header positions
enum DashboardHeaderPosition {
  /// Header at the top
  top,

  /// Header at the bottom
  bottom,

  /// Header on the left side
  left,

  /// Header on the right side
  right,
}

/// Enum for dashboard scroll physics
enum DashboardScrollBehavior {
  /// Default scroll physics
  normal,

  /// Bouncing scroll physics
  bouncing,

  /// Clamping scroll physics
  clamping,

  /// Never scrollable
  never,

  /// Always scrollable
  always,
}

/// Enum for dashboard overflow behavior
enum DashboardOverflowBehavior {
  /// Clip overflow
  clip,

  /// Show overflow
  visible,

  /// Fade overflow
  fade,

  /// Ellipsis for text overflow
  ellipsis,
}

/// Enum for dashboard layout direction
enum DashboardDirection {
  /// Left to right
  ltr,

  /// Right to left
  rtl,
}

/// Enum for dashboard section entry animation
enum SectionEntryAnimation {
  /// Fade in
  fade,

  /// Slide in from bottom
  slideUp,

  /// Slide in from top
  slideDown,

  /// Slide in from left
  slideLeft,

  /// Slide in from right
  slideRight,

  /// Scale up
  scale,

  /// No animation
  none,
}

/// Enum for swipe directions
enum SwipeDirection {
  /// Swipe left
  left,

  /// Swipe right
  right,

  /// Swipe up
  up,

  /// Swipe down
  down,
}

/// A responsive dashboard widget that provides a multi-section layout with responsive behavior.
///
/// This widget adapts its layout based on the available screen size, showing different
/// arrangements of sections for different screen widths.
class ResponsiveDashboardWidget extends StatefulWidget {
  //
  // 1. Dashboard Layout and Structure
  //

  /// The title of the dashboard.
  final String title;

  /// List of sections to display in the dashboard.
  final List<DashboardSection> sections;

  /// Whether to show a grid layout or a flowing layout.
  final bool useGridLayout;

  /// Number of columns to show in grid layout on large screens.
  final int gridColumns;

  /// Custom breakpoint for mobile layout (default: 600)
  final double mobileBreakpoint;

  /// Custom breakpoint for tablet layout (default: 900)
  final double tabletBreakpoint;

  /// Spacing between dashboard sections.
  final double sectionSpacing;

  /// Padding inside the entire dashboard.
  final EdgeInsets padding;

  /// Aspect ratio for grid items (default: 1.5)
  final double sectionAspectRatio;

  /// Padding inside each section.
  final EdgeInsets sectionPadding;

  /// Padding around content within sections.
  final EdgeInsets sectionContentPadding;

  /// Layout direction (LTR or RTL)
  final DashboardDirection layoutDirection;

  /// Scroll physics for the dashboard
  final DashboardScrollBehavior scrollBehavior;

  /// How to handle content that overflows sections
  final DashboardOverflowBehavior overflowBehavior;

  /// Custom scroll controller for the dashboard
  final ScrollController? scrollController;

  /// Whether to use a staggered grid layout
  final bool useStaggeredGrid;

  /// Whether to respect section sizes in the layout
  final bool respectSectionSizes;

  /// Fixed sections that don't scroll with the rest
  final List<int>? fixedSectionIndices;

  /// Section visibility map (by index)
  final Map<int, bool>? sectionVisibility;

  /// Section weights for relative sizing
  final Map<int, double>? sectionWeights;

  //
  // 2. Visual Styling
  //

  /// The background color of the dashboard.
  final Color backgroundColor;

  /// The color of the section cards.
  final Color sectionColor;

  /// The text color for the dashboard content.
  final Color textColor;

  /// The accent color for highlights and interactive elements.
  final Color accentColor;

  /// Background color for the dashboard header
  final Color? headerBackgroundColor;

  /// Background color for section headers
  final Color? sectionHeaderColor;

  /// Color for dividers between section header and content
  final Color? dividerColor;

  /// Colors for icons (refresh, settings, section actions)
  final Color? iconColor;

  /// Colors for chart elements
  final List<Color>? chartColors;

  /// Whether to use gradient backgrounds
  final bool useGradients;

  /// Gradient for dashboard background
  final Gradient? backgroundGradient;

  /// Gradient for section backgrounds
  final Gradient? sectionGradient;

  /// Whether to show section borders.
  final bool showSectionBorders;

  /// Border color for sections when [showSectionBorders] is true.
  final Color borderColor;

  /// Border width for sections when [showSectionBorders] is true.
  final double borderWidth;

  /// Border radius for sections.
  final double borderRadius;

  /// Border style for sections
  final DashboardBorderStyle borderStyle;

  /// Whether to show shadows for sections.
  final bool showShadows;

  /// Elevation for shadows when [showShadows] is true.
  final double elevation;

  /// Shadow color for sections
  final Color? shadowColor;

  /// Shadow offset for sections
  final Offset? shadowOffset;

  /// Shadow blur radius
  final double? shadowBlurRadius;

  /// Shadow spread radius
  final double? shadowSpreadRadius;

  /// Whether to show the dashboard title.
  final bool showTitle;

  /// Title style for the dashboard.
  final TextStyle? titleStyle;

  /// Section title style
  final TextStyle? sectionTitleStyle;

  /// Content text style
  final TextStyle? contentTextStyle;

  /// Font family for all text
  final String? fontFamily;

  /// Text alignment for dashboard text
  final TextAlign? textAlignment;

  /// Text overflow handling
  final TextOverflow? textOverflow;

  //
  // 3. Interactive Features
  //

  /// Whether sections can be reordered by the user.
  final bool allowReordering;

  /// Whether sections can be resized by the user.
  final bool allowResizing;

  /// Whether to show a refresh button for the dashboard.
  final bool showRefreshButton;

  /// Callback when the refresh button is pressed.
  final VoidCallback? onRefresh;

  /// Whether to show a settings button for the dashboard.
  final bool showSettingsButton;

  /// Callback when the settings button is pressed.
  final VoidCallback? onSettings;

  /// Custom action buttons for the dashboard header
  final List<Widget>? customHeaderActions;

  /// Custom widget to replace the entire header
  final Widget? customHeader;

  /// Position of the header
  final DashboardHeaderPosition headerPosition;

  /// Whether to make the header sticky when scrolling
  final bool stickyHeader;

  /// Whether sections can be expanded/collapsed
  final bool allowSectionCollapse;

  /// Whether sections can be closed/removed
  final bool allowSectionClose;

  /// Whether sections have individual refresh buttons
  final bool showSectionRefreshButtons;

  /// Whether sections have individual settings buttons
  final bool showSectionSettingsButtons;

  /// Custom drag handle widget for reordering
  final Widget? dragHandleWidget;

  /// Callback for double-tap on sections
  final Function(int sectionIndex)? onSectionDoubleTap;

  /// Callback for long-press on sections
  final Function(int sectionIndex)? onSectionLongPress;

  /// Actions for swipe gestures on sections
  final Map<SwipeDirection, Function(int sectionIndex)>? swipeActions;

  /// Callback when a section is clicked
  final Function(int sectionIndex)? onSectionTap;

  /// Whether to show hover effects
  final bool showHoverEffects;

  /// Color for hover effects
  final Color? hoverColor;

  /// Whether to enable keyboard focus navigation
  final bool enableKeyboardNavigation;

  //
  // 4. Animation and Transitions
  //

  /// Whether to animate transitions when the layout changes.
  final bool animateLayoutChanges;

  /// Duration for animations when [animateLayoutChanges] is true.
  final Duration animationDuration;

  /// Animation curve for transitions
  final Curve animationCurve;

  /// Animation for section entry
  final SectionEntryAnimation sectionEntryAnimation;

  /// Animation for section exit
  final SectionEntryAnimation sectionExitAnimation;

  /// Animation for section reordering
  final bool animateReordering;

  /// Animation for section resizing
  final bool animateResizing;

  /// Animation during data refresh
  final bool showRefreshAnimation;

  /// Widget to show during loading
  final Widget? loadingWidget;

  /// Transition effect between dashboard states
  final AnimatedSwitcherTransitionBuilder? transitionBuilder;

  //
  // 5. Data and Content
  //

  /// Default content for empty sections
  final Widget? defaultSectionContent;

  /// Widget to show when a section has no data
  final Widget? emptySectionWidget;

  /// Widget to show while section data is loading
  final Widget? loadingSectionWidget;

  /// Widget to show when section data fails to load
  final Widget? errorSectionWidget;

  /// Whether to allow individual section scrolling
  final bool allowSectionScrolling;

  /// Auto-refresh interval for data
  final Duration? refreshInterval;

  /// Whether to show real-time updates
  final bool enableRealTimeUpdates;

  //
  // 6. Accessibility and Internationalization
  //

  /// Semantic label for the dashboard
  final String? semanticLabel;

  /// Whether to enable screen reader announcements
  final bool enableScreenReaderAnnouncements;

  /// Whether to support high contrast mode
  final bool supportHighContrast;

  /// High contrast colors
  final Map<String, Color>? highContrastColors;

  /// Whether to support text scaling
  final bool supportTextScaling;

  /// Whether to support right-to-left languages
  final bool supportRtl;

  /// Localized strings
  final Map<String, String>? localizedStrings;

  //
  // 7. Advanced Features
  //

  /// Whether to save and restore dashboard state
  final bool persistState;

  /// Key for state persistence
  final String? persistenceKey;

  /// Whether to support light/dark themes
  final bool supportTheming;

  /// Whether to support responsive printing
  final bool supportPrinting;

  /// Export options
  final List<String>? exportOptions;

  /// Whether to support fullscreen mode
  final bool supportFullscreen;

  /// Callback when entering fullscreen
  final VoidCallback? onEnterFullscreen;

  /// Callback when exiting fullscreen
  final VoidCallback? onExitFullscreen;

  /// Predefined dashboard configurations
  final Map<String, dynamic>? dashboardPresets;

  /// Whether to enable dashboard history (undo/redo)
  final bool enableHistory;

  /// Maximum history steps
  final int? maxHistorySteps;

  /// Whether to enable performance optimizations
  final bool enablePerformanceOptimizations;

  /// Whether to track dashboard usage analytics
  final bool trackAnalytics;

  /// Analytics callback
  final Function(String event, Map<String, dynamic> properties)? onAnalyticsEvent;

  /// Creates a responsive dashboard widget.
  const ResponsiveDashboardWidget({
    super.key,
    required this.title,
    required this.sections,
    // 1. Dashboard Layout and Structure
    this.useGridLayout = true,
    this.gridColumns = 3,
    this.mobileBreakpoint = 600,
    this.tabletBreakpoint = 900,
    this.sectionSpacing = 16.0,
    this.padding = const EdgeInsets.all(16.0),
    this.sectionAspectRatio = 1.5,
    this.sectionPadding = const EdgeInsets.all(16.0),
    this.sectionContentPadding = const EdgeInsets.all(8.0),
    this.layoutDirection = DashboardDirection.ltr,
    this.scrollBehavior = DashboardScrollBehavior.normal,
    this.overflowBehavior = DashboardOverflowBehavior.clip,
    this.scrollController,
    this.useStaggeredGrid = false,
    this.respectSectionSizes = false,
    this.fixedSectionIndices,
    this.sectionVisibility,
    this.sectionWeights,

    // 2. Visual Styling
    this.backgroundColor = Colors.white,
    this.sectionColor = Colors.white,
    this.textColor = Colors.black87,
    this.accentColor = Colors.blue,
    this.headerBackgroundColor,
    this.sectionHeaderColor,
    this.dividerColor,
    this.iconColor,
    this.chartColors,
    this.useGradients = false,
    this.backgroundGradient,
    this.sectionGradient,
    this.showSectionBorders = true,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.borderStyle = DashboardBorderStyle.solid,
    this.showShadows = true,
    this.elevation = 2.0,
    this.shadowColor,
    this.shadowOffset,
    this.shadowBlurRadius,
    this.shadowSpreadRadius,
    this.showTitle = true,
    this.titleStyle,
    this.sectionTitleStyle,
    this.contentTextStyle,
    this.fontFamily,
    this.textAlignment,
    this.textOverflow,

    // 3. Interactive Features
    this.allowReordering = false,
    this.allowResizing = false,
    this.showRefreshButton = true,
    this.onRefresh,
    this.showSettingsButton = true,
    this.onSettings,
    this.customHeaderActions,
    this.customHeader,
    this.headerPosition = DashboardHeaderPosition.top,
    this.stickyHeader = false,
    this.allowSectionCollapse = false,
    this.allowSectionClose = false,
    this.showSectionRefreshButtons = false,
    this.showSectionSettingsButtons = false,
    this.dragHandleWidget,
    this.onSectionDoubleTap,
    this.onSectionLongPress,
    this.swipeActions,
    this.onSectionTap,
    this.showHoverEffects = false,
    this.hoverColor,
    this.enableKeyboardNavigation = false,

    // 4. Animation and Transitions
    this.animateLayoutChanges = true,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    this.sectionEntryAnimation = SectionEntryAnimation.fade,
    this.sectionExitAnimation = SectionEntryAnimation.fade,
    this.animateReordering = true,
    this.animateResizing = true,
    this.showRefreshAnimation = true,
    this.loadingWidget,
    this.transitionBuilder,

    // 5. Data and Content
    this.defaultSectionContent,
    this.emptySectionWidget,
    this.loadingSectionWidget,
    this.errorSectionWidget,
    this.allowSectionScrolling = true,
    this.refreshInterval,
    this.enableRealTimeUpdates = false,

    // 6. Accessibility and Internationalization
    this.semanticLabel,
    this.enableScreenReaderAnnouncements = false,
    this.supportHighContrast = false,
    this.highContrastColors,
    this.supportTextScaling = true,
    this.supportRtl = false,
    this.localizedStrings,

    // 7. Advanced Features
    this.persistState = false,
    this.persistenceKey,
    this.supportTheming = false,
    this.supportPrinting = false,
    this.exportOptions,
    this.supportFullscreen = false,
    this.onEnterFullscreen,
    this.onExitFullscreen,
    this.dashboardPresets,
    this.enableHistory = false,
    this.maxHistorySteps,
    this.enablePerformanceOptimizations = false,
    this.trackAnalytics = false,
    this.onAnalyticsEvent,
  });

  @override
  State<ResponsiveDashboardWidget> createState() => _ResponsiveDashboardWidgetState();
}

class _ResponsiveDashboardWidgetState extends State<ResponsiveDashboardWidget> {
  late List<DashboardSection> _sections;
  bool _isFullscreen = false;
  List<List<DashboardSection>> _history = [];
  int _historyIndex = -1;
  Map<int, bool> _collapsedSections = {};
  int? _draggedSectionIndex;

  @override
  void initState() {
    super.initState();
    _sections = List.from(widget.sections);

    // Initialize collapsed state
    for (int i = 0; i < _sections.length; i++) {
      _collapsedSections[i] = _sections[i].isInitiallyCollapsed;
    }

    // Load saved state if persistence is enabled
    if (widget.persistState) {
      _loadSavedState();
    }

    // Add initial state to history if history is enabled
    if (widget.enableHistory) {
      _addToHistory();
    }

    // Set up auto-refresh if enabled
    if (widget.refreshInterval != null) {
      _setupAutoRefresh();
    }
  }

  @override
  void didUpdateWidget(ResponsiveDashboardWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.sections != widget.sections) {
      _sections = List.from(widget.sections);

      // Reset collapsed state for new sections
      _collapsedSections.clear();
      for (int i = 0; i < _sections.length; i++) {
        _collapsedSections[i] = _sections[i].isInitiallyCollapsed;
      }

      // Add to history if enabled
      if (widget.enableHistory) {
        _addToHistory();
      }
    }

    // Handle refresh interval changes
    if (oldWidget.refreshInterval != widget.refreshInterval) {
      _setupAutoRefresh();
    }
  }

  // Set up auto-refresh timer
  void _setupAutoRefresh() {
    // This would be implemented with a Timer
    // For now, we'll just leave this as a placeholder
  }

  // Save current state to shared preferences
  Future<void> _saveState() async {
    if (!widget.persistState || widget.persistenceKey == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();

      // Create a serializable representation of the dashboard state
      final state = {
        'sectionOrder': List.generate(_sections.length, (index) => index),
        'collapsedSections': _collapsedSections.map((key, value) => MapEntry(key.toString(), value)),
        'visibility': widget.sectionVisibility?.map((key, value) => MapEntry(key.toString(), value)) ?? {},
      };

      // Save as JSON string
      await prefs.setString(widget.persistenceKey!, jsonEncode(state));
    } catch (e) {
      debugPrint('Error saving dashboard state: $e');
    }
  }

  // Load saved state from shared preferences
  Future<void> _loadSavedState() async {
    if (!widget.persistState || widget.persistenceKey == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final savedState = prefs.getString(widget.persistenceKey!);

      if (savedState != null) {
        final state = jsonDecode(savedState) as Map<String, dynamic>;

        // Restore collapsed state
        if (state.containsKey('collapsedSections')) {
          final collapsedMap = state['collapsedSections'] as Map<String, dynamic>;
          _collapsedSections = collapsedMap.map((key, value) =>
            MapEntry(int.parse(key), value as bool));
        }

        // Reorder sections if needed
        if (state.containsKey('sectionOrder')) {
          final order = (state['sectionOrder'] as List).cast<int>();
          if (order.length == _sections.length) {
            final reorderedSections = <DashboardSection>[];
            for (final index in order) {
              if (index < _sections.length) {
                reorderedSections.add(_sections[index]);
              }
            }
            if (reorderedSections.length == _sections.length) {
              _sections = reorderedSections;
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading dashboard state: $e');
    }
  }

  // Add current state to history
  void _addToHistory() {
    if (!widget.enableHistory) return;

    // Remove any forward history if we're not at the end
    if (_historyIndex < _history.length - 1) {
      _history = _history.sublist(0, _historyIndex + 1);
    }

    // Add current state to history
    _history.add(List.from(_sections));
    _historyIndex = _history.length - 1;

    // Limit history size if needed
    if (widget.maxHistorySteps != null && _history.length > widget.maxHistorySteps!) {
      _history = _history.sublist(_history.length - widget.maxHistorySteps!);
      _historyIndex = _history.length - 1;
    }
  }

  // Undo last action
  void _undo() {
    if (!widget.enableHistory || _historyIndex <= 0) return;

    _historyIndex--;
    _sections = List.from(_history[_historyIndex]);
    setState(() {});
  }

  // Redo last undone action
  void _redo() {
    if (!widget.enableHistory || _historyIndex >= _history.length - 1) return;

    _historyIndex++;
    _sections = List.from(_history[_historyIndex]);
    setState(() {});
  }

  // Toggle fullscreen mode
  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
      if (_isFullscreen) {
        if (widget.onEnterFullscreen != null) {
          widget.onEnterFullscreen!();
        }
      } else {
        if (widget.onExitFullscreen != null) {
          widget.onExitFullscreen!();
        }
      }
    });
  }

  // Toggle section collapsed state
  void _toggleSectionCollapsed(int index) {
    if (!widget.allowSectionCollapse) return;

    setState(() {
      _collapsedSections[index] = !(_collapsedSections[index] ?? false);
    });

    // Add to history if enabled
    if (widget.enableHistory) {
      _addToHistory();
    }

    // Save state if persistence is enabled
    if (widget.persistState) {
      _saveState();
    }
  }

  // Close a section
  void _closeSection(int index) {
    if (!widget.allowSectionClose) return;

    setState(() {
      // Instead of removing, we'll just mark it as not visible
      // This allows it to be restored later if needed
      if (widget.sectionVisibility != null) {
        widget.sectionVisibility![index] = false;
      } else {
        // If no visibility map is provided, we'll actually remove the section
        _sections.removeAt(index);

        // Update collapsed sections map
        final newCollapsedSections = <int, bool>{};
        _collapsedSections.forEach((key, value) {
          if (key < index) {
            newCollapsedSections[key] = value;
          } else if (key > index) {
            newCollapsedSections[key - 1] = value;
          }
        });
        _collapsedSections = newCollapsedSections;
      }
    });

    // Add to history if enabled
    if (widget.enableHistory) {
      _addToHistory();
    }

    // Save state if persistence is enabled
    if (widget.persistState) {
      _saveState();
    }
  }

  // Reorder sections
  void _reorderSections(int oldIndex, int newIndex) {
    if (!widget.allowReordering) return;

    setState(() {
      if (oldIndex < newIndex) {
        newIndex -= 1;
      }
      final item = _sections.removeAt(oldIndex);
      _sections.insert(newIndex, item);

      // Update collapsed sections map
      final oldCollapsed = _collapsedSections[oldIndex] ?? false;
      final newCollapsedSections = <int, bool>{};

      for (int i = 0; i < _sections.length; i++) {
        if (i == newIndex) {
          newCollapsedSections[i] = oldCollapsed;
        } else if (i < oldIndex && i < newIndex || i > oldIndex && i > newIndex) {
          // Indices that don't change
          newCollapsedSections[i] = _collapsedSections[i] ?? false;
        } else if (oldIndex < newIndex && i >= oldIndex && i < newIndex) {
          // Indices that shift down
          newCollapsedSections[i] = _collapsedSections[i + 1] ?? false;
        } else if (oldIndex > newIndex && i > newIndex && i <= oldIndex) {
          // Indices that shift up
          newCollapsedSections[i] = _collapsedSections[i - 1] ?? false;
        }
      }

      _collapsedSections = newCollapsedSections;
    });

    // Add to history if enabled
    if (widget.enableHistory) {
      _addToHistory();
    }

    // Save state if persistence is enabled
    if (widget.persistState) {
      _saveState();
    }
  }

  // Determine the number of columns based on screen width
  int _getColumnCount(double width) {
    if (width < widget.mobileBreakpoint) {
      return 1; // Mobile
    } else if (width < widget.tabletBreakpoint) {
      return 2; // Tablet
    } else {
      return widget.gridColumns; // Desktop
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final columnCount = _getColumnCount(width);

        // Apply RTL directionality if needed
        Widget dashboardContent = Container(
          // Use constraints instead of fixed height for better responsiveness
          constraints: BoxConstraints(
            minHeight: 500, // Minimum height to ensure the dashboard has a reasonable size
            maxHeight: MediaQuery.of(context).size.height * 0.9, // Maximum height based on screen size
          ),
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            gradient: widget.useGradients ? widget.backgroundGradient : null,
          ),
          padding: widget.padding,
          child: _buildDashboardContent(columnCount),
        );

        // Apply semantic labels for accessibility
        if (widget.semanticLabel != null) {
          dashboardContent = Semantics(
            label: widget.semanticLabel,
            child: dashboardContent,
          );
        }

        // Apply RTL directionality if needed
        if (widget.layoutDirection == DashboardDirection.rtl ||
            (widget.supportRtl && Directionality.of(context) == TextDirection.rtl)) {
          dashboardContent = Directionality(
            textDirection: TextDirection.rtl,
            child: dashboardContent,
          );
        }

        // Apply high contrast if needed
        if (widget.supportHighContrast && MediaQuery.of(context).highContrast) {
          // Use high contrast colors if provided
          // This would be implemented with a theme wrapper
        }

        return dashboardContent;
      },
    );
  }

  Widget _buildDashboardContent(int columnCount) {
    // Build the dashboard content based on header position
    switch (widget.headerPosition) {
      case DashboardHeaderPosition.top:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.showTitle)
              widget.stickyHeader
                ? _buildStickyHeader(_buildHeader())
                : _buildHeader(),
            const SizedBox(height: 16),
            Expanded(
              child: _buildDashboardBody(columnCount),
            ),
          ],
        );

      case DashboardHeaderPosition.bottom:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: _buildDashboardBody(columnCount),
            ),
            const SizedBox(height: 16),
            if (widget.showTitle) _buildHeader(),
          ],
        );

      case DashboardHeaderPosition.left:
        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.showTitle)
              SizedBox(
                width: 200, // Fixed width for side header
                child: _buildVerticalHeader(),
              ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDashboardBody(columnCount),
            ),
          ],
        );

      case DashboardHeaderPosition.right:
        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: _buildDashboardBody(columnCount),
            ),
            const SizedBox(width: 16),
            if (widget.showTitle)
              SizedBox(
                width: 200, // Fixed width for side header
                child: _buildVerticalHeader(),
              ),
          ],
        );
    }
  }

  Widget _buildDashboardBody(int columnCount) {
    // Apply scroll physics based on settings
    ScrollPhysics? physics;
    switch (widget.scrollBehavior) {
      case DashboardScrollBehavior.bouncing:
        physics = const BouncingScrollPhysics();
        break;
      case DashboardScrollBehavior.clamping:
        physics = const ClampingScrollPhysics();
        break;
      case DashboardScrollBehavior.never:
        physics = const NeverScrollableScrollPhysics();
        break;
      case DashboardScrollBehavior.always:
        physics = const AlwaysScrollableScrollPhysics();
        break;
      case DashboardScrollBehavior.normal:
        physics = null;
        break;
    }

    // Build the main content with appropriate layout
    Widget content = widget.useGridLayout
        ? _buildGridLayout(columnCount)
        : _buildFlowLayout();

    // Apply scroll controller if provided
    if (widget.scrollController != null || physics != null) {
      content = ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(physics: physics),
        child: content,
      );
    }

    // Show loading widget if provided and needed
    if (widget.showRefreshAnimation && widget.loadingWidget != null) {
      // This would be implemented with a loading state
    }

    // Use Expanded to fill available space rather than fixed height
    return content;
  }

  // Helper method to create a sticky header
  Widget _buildStickyHeader(Widget header) {
    return Container(
      color: widget.headerBackgroundColor ?? widget.backgroundColor,
      child: header,
    );
  }

  // Build a vertical header for left/right positions
  Widget _buildVerticalHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.title,
          style: widget.titleStyle ??
              TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: widget.textColor,
              ),
        ),
        const SizedBox(height: 16),
        if (widget.showRefreshButton)
          IconButton(
            icon: Icon(Icons.refresh, color: widget.accentColor),
            onPressed: widget.onRefresh ?? () {},
            tooltip: 'Refresh dashboard',
          ),
        if (widget.showSettingsButton)
          IconButton(
            icon: Icon(Icons.settings, color: widget.accentColor),
            onPressed: widget.onSettings ?? () {},
            tooltip: 'Dashboard settings',
          ),
        if (widget.customHeaderActions != null)
          ...widget.customHeaderActions!,
      ],
    );
  }

  Widget _buildHeader() {
    // Use custom header if provided
    if (widget.customHeader != null) {
      return widget.customHeader!;
    }

    return Container(
      color: widget.headerBackgroundColor,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            widget.title,
            style: widget.titleStyle ??
                TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: widget.textColor,
                  fontFamily: widget.fontFamily,
                ),
          ),
          Row(
            children: [
              // Undo button
              if (widget.enableHistory)
                IconButton(
                  icon: Icon(
                    Icons.undo,
                    color: _historyIndex > 0
                        ? (widget.iconColor ?? widget.accentColor)
                        : (widget.iconColor ?? widget.accentColor).withAlpha(77), // 0.3 * 255 = 77
                  ),
                  onPressed: _historyIndex > 0 ? _undo : null,
                  tooltip: widget.localizedStrings?['undo_tooltip'] ?? 'Undo',
                ),

              // Redo button
              if (widget.enableHistory)
                IconButton(
                  icon: Icon(
                    Icons.redo,
                    color: _historyIndex < _history.length - 1
                        ? (widget.iconColor ?? widget.accentColor)
                        : (widget.iconColor ?? widget.accentColor).withAlpha(77), // 0.3 * 255 = 77
                  ),
                  onPressed: _historyIndex < _history.length - 1 ? _redo : null,
                  tooltip: widget.localizedStrings?['redo_tooltip'] ?? 'Redo',
                ),

              // Refresh button
              if (widget.showRefreshButton)
                IconButton(
                  icon: Icon(
                    Icons.refresh,
                    color: widget.iconColor ?? widget.accentColor,
                  ),
                  onPressed: widget.onRefresh ?? () {
                    if (widget.trackAnalytics && widget.onAnalyticsEvent != null) {
                      widget.onAnalyticsEvent!('dashboard_refresh', {'title': widget.title});
                    }
                  },
                  tooltip: widget.localizedStrings?['refresh_tooltip'] ?? 'Refresh dashboard',
                ),

              // Settings button
              if (widget.showSettingsButton)
                IconButton(
                  icon: Icon(
                    Icons.settings,
                    color: widget.iconColor ?? widget.accentColor,
                  ),
                  onPressed: widget.onSettings ?? () {
                    if (widget.trackAnalytics && widget.onAnalyticsEvent != null) {
                      widget.onAnalyticsEvent!('dashboard_settings', {'title': widget.title});
                    }
                  },
                  tooltip: widget.localizedStrings?['settings_tooltip'] ?? 'Dashboard settings',
                ),

              // Fullscreen button
              if (widget.supportFullscreen)
                IconButton(
                  icon: Icon(
                    _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
                    color: widget.iconColor ?? widget.accentColor,
                  ),
                  onPressed: _toggleFullscreen,
                  tooltip: _isFullscreen
                      ? (widget.localizedStrings?['exit_fullscreen_tooltip'] ?? 'Exit fullscreen')
                      : (widget.localizedStrings?['fullscreen_tooltip'] ?? 'Enter fullscreen'),
                ),

              // Custom header actions
              if (widget.customHeaderActions != null)
                ...widget.customHeaderActions!,
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGridLayout(int columnCount) {
    Widget grid = _buildGrid(columnCount);

    // Apply animations if enabled
    if (widget.animateLayoutChanges) {
      return AnimatedSwitcher(
        duration: widget.animationDuration,
        switchInCurve: widget.animationCurve,
        switchOutCurve: widget.animationCurve,
        transitionBuilder: widget.transitionBuilder ?? AnimatedSwitcher.defaultTransitionBuilder,
        child: grid,
      );
    }

    return grid;
  }

  Widget _buildGrid(int columnCount) {
    // Filter sections based on visibility
    final visibleSections = _getVisibleSections();

    // Use staggered grid if enabled
    if (widget.useStaggeredGrid && widget.respectSectionSizes) {
      // Implement a custom staggered grid that respects section sizes
      return CustomScrollView(
        controller: widget.scrollController,
        shrinkWrap: true,
        slivers: [
          SliverGrid(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: columnCount,
              crossAxisSpacing: widget.sectionSpacing,
              mainAxisSpacing: widget.sectionSpacing,
              // We'll adjust the child aspect ratio based on section size
              childAspectRatio: widget.sectionAspectRatio,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final section = visibleSections[index];

                // Determine the span based on section size
                // Apply different styling based on section size
                Widget sectionWidget = _buildSectionCard(section, index);

                // Apply constraints based on section size
                if (section.size == DashboardSectionSize.small) {
                  // Small sections get default size
                  sectionWidget = ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: section.minHeight ?? 150,
                      maxHeight: section.maxHeight ?? double.infinity,
                    ),
                    child: sectionWidget,
                  );
                } else if (section.size == DashboardSectionSize.medium) {
                  // Medium sections are taller
                  sectionWidget = ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: section.minHeight ?? 250,
                      maxHeight: section.maxHeight ?? double.infinity,
                    ),
                    child: sectionWidget,
                  );
                } else if (section.size == DashboardSectionSize.large) {
                  // Large sections are even taller
                  sectionWidget = ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: section.minHeight ?? 350,
                      maxHeight: section.maxHeight ?? double.infinity,
                    ),
                    child: sectionWidget,
                  );
                } else if (section.size == DashboardSectionSize.fullWidth) {
                  // Full width sections span the entire width
                  sectionWidget = ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: section.minHeight ?? 200,
                      maxHeight: section.maxHeight ?? double.infinity,
                    ),
                    child: sectionWidget,
                  );
                }

                // Apply section entry animation if needed
                if (widget.sectionEntryAnimation != SectionEntryAnimation.none) {
                  return _applySectionEntryAnimation(sectionWidget, index);
                }

                return sectionWidget;
              },
              childCount: visibleSections.length,
            ),
          ),
        ],
      );
    }

    // Regular grid layout
    return GridView.builder(
      key: ValueKey<int>(columnCount), // For AnimatedSwitcher
      controller: widget.scrollController,
      shrinkWrap: true, // Use available space
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columnCount,
        crossAxisSpacing: widget.sectionSpacing,
        mainAxisSpacing: widget.sectionSpacing,
        childAspectRatio: widget.sectionAspectRatio,
      ),
      itemCount: visibleSections.length,
      itemBuilder: (context, index) {
        final section = visibleSections[index];

        final sectionWidget = _buildSectionCard(section, index);

        // Apply section entry animation if needed
        if (widget.sectionEntryAnimation != SectionEntryAnimation.none) {
          return _applySectionEntryAnimation(sectionWidget, index);
        }

        return sectionWidget;
      },
    );
  }

  Widget _buildFlowLayout() {
    // Filter sections based on visibility
    final visibleSections = _getVisibleSections();

    return ListView.separated(
        controller: widget.scrollController,
        shrinkWrap: true, // Use available space
        itemCount: visibleSections.length,
        separatorBuilder: (context, index) => SizedBox(height: widget.sectionSpacing),
        itemBuilder: (context, index) {
          final section = visibleSections[index];

          // Build the section card
          Widget sectionWidget = _buildSectionCard(section, index);

          // Apply constraints based on section size if respectSectionSizes is true
          if (widget.respectSectionSizes) {
            if (section.size == DashboardSectionSize.small) {
              // Small sections get default size
              sectionWidget = ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: section.minHeight ?? 150,
                  maxHeight: section.maxHeight ?? double.infinity,
                ),
                child: sectionWidget,
              );
            } else if (section.size == DashboardSectionSize.medium) {
              // Medium sections are taller
              sectionWidget = ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: section.minHeight ?? 250,
                  maxHeight: section.maxHeight ?? double.infinity,
                ),
                child: sectionWidget,
              );
            } else if (section.size == DashboardSectionSize.large) {
              // Large sections are even taller
              sectionWidget = ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: section.minHeight ?? 350,
                  maxHeight: section.maxHeight ?? double.infinity,
                ),
                child: sectionWidget,
              );
            } else if (section.size == DashboardSectionSize.fullWidth) {
              // Full width sections span the entire width
              sectionWidget = ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: section.minHeight ?? 200,
                  maxHeight: section.maxHeight ?? double.infinity,
                ),
                child: sectionWidget,
              );
            }
          }

          // Apply section entry animation if needed
          if (widget.sectionEntryAnimation != SectionEntryAnimation.none) {
            return _applySectionEntryAnimation(sectionWidget, index);
          }

          return sectionWidget;
        },
      );
  }

  // Helper method to get visible sections
  List<DashboardSection> _getVisibleSections() {
    if (widget.sectionVisibility == null) {
      return _sections.where((section) => section.isVisible).toList();
    }

    return _sections.asMap().entries
      .where((entry) =>
        widget.sectionVisibility?[entry.key] ?? entry.value.isVisible)
      .map((entry) => entry.value)
      .toList();
  }

  // Apply section entry animation based on settings
  Widget _applySectionEntryAnimation(Widget child, int index) {
    // Apply different animations based on the setting
    switch (widget.sectionEntryAnimation) {
      case SectionEntryAnimation.fade:
        return AnimatedOpacity(
          opacity: 1.0,
          duration: widget.animationDuration,
          curve: widget.animationCurve,
          child: child,
        );

      case SectionEntryAnimation.slideUp:
        // Start from below and slide up
        return TweenAnimationBuilder<Offset>(
          tween: Tween<Offset>(
            begin: const Offset(0, 1), // Start from below
            end: Offset.zero,
          ),
          duration: widget.animationDuration,
          curve: widget.animationCurve,
          builder: (context, offset, child) {
            return FractionalTranslation(
              translation: offset,
              child: child,
            );
          },
          child: child,
        );

      case SectionEntryAnimation.slideDown:
        // Start from above and slide down
        return TweenAnimationBuilder<Offset>(
          tween: Tween<Offset>(
            begin: const Offset(0, -1), // Start from above
            end: Offset.zero,
          ),
          duration: widget.animationDuration,
          curve: widget.animationCurve,
          builder: (context, offset, child) {
            return FractionalTranslation(
              translation: offset,
              child: child,
            );
          },
          child: child,
        );

      case SectionEntryAnimation.slideLeft:
        // Start from right and slide left
        return TweenAnimationBuilder<Offset>(
          tween: Tween<Offset>(
            begin: const Offset(1, 0), // Start from right
            end: Offset.zero,
          ),
          duration: widget.animationDuration,
          curve: widget.animationCurve,
          builder: (context, offset, child) {
            return FractionalTranslation(
              translation: offset,
              child: child,
            );
          },
          child: child,
        );

      case SectionEntryAnimation.slideRight:
        // Start from left and slide right
        return TweenAnimationBuilder<Offset>(
          tween: Tween<Offset>(
            begin: const Offset(-1, 0), // Start from left
            end: Offset.zero,
          ),
          duration: widget.animationDuration,
          curve: widget.animationCurve,
          builder: (context, offset, child) {
            return FractionalTranslation(
              translation: offset,
              child: child,
            );
          },
          child: child,
        );

      case SectionEntryAnimation.scale:
        // Start small and scale up
        return TweenAnimationBuilder<double>(
          tween: Tween<double>(
            begin: 0.5, // Start at 50% size
            end: 1.0,   // End at 100% size
          ),
          duration: widget.animationDuration,
          curve: widget.animationCurve,
          builder: (context, scale, child) {
            return Transform.scale(
              scale: scale,
              alignment: Alignment.center,
              child: child,
            );
          },
          child: child,
        );

      case SectionEntryAnimation.none:
        return child;
    }
  }

  Widget _buildSectionCard(DashboardSection section, [int? index]) {
    // Handle section visibility
    if (!section.isVisible) {
      return const SizedBox.shrink();
    }

    // Handle section loading state
    if (section.isLoading && section.loadingWidget != null) {
      return section.loadingWidget!;
    }

    // Handle section error state
    if (section.hasError && section.errorWidget != null) {
      return section.errorWidget!;
    }

    // Handle empty state
    if (section.isEmpty && section.emptyWidget != null) {
      return section.emptyWidget!;
    }

    // Determine section colors (section-specific or dashboard default)
    final backgroundColor = section.backgroundColor ?? widget.sectionColor;
    final borderColor = section.borderColor ?? widget.borderColor;
    final textColor = section.textColor ?? widget.textColor;
    final accentColor = section.accentColor ?? widget.accentColor;

    // Determine border style
    BorderSide borderSide = BorderSide.none;
    if (widget.showSectionBorders) {
      switch (widget.borderStyle) {
        case DashboardBorderStyle.solid:
          borderSide = BorderSide(
            color: borderColor,
            width: widget.borderWidth,
          );
          break;
        case DashboardBorderStyle.dashed:
          // Implement dashed border
          borderSide = BorderSide(
            color: borderColor,
            width: widget.borderWidth,
          );
          // We'll use a custom border in the shape below
          break;
        case DashboardBorderStyle.dotted:
          // Implement dotted border
          borderSide = BorderSide(
            color: borderColor,
            width: widget.borderWidth,
          );
          // We'll use a custom border in the shape below
          break;
        case DashboardBorderStyle.none:
          borderSide = BorderSide.none;
          break;
      }
    }

    // Determine section padding
    final padding = section.padding ?? widget.sectionPadding;

    // Determine section border radius
    final borderRadius = section.borderRadius ?? widget.borderRadius;

    // Determine shadow properties
    final showShadow = section.showShadow ?? widget.showShadows;
    final elevation = section.elevation ?? widget.elevation;

    // Check if section is collapsed
    final isCollapsed = index != null && (_collapsedSections[index] ?? false);

    // Create the shape based on border style
    ShapeBorder shape;
    if (widget.borderStyle == DashboardBorderStyle.dashed) {
      shape = DashedBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        dashPattern: const [6, 3],
        color: borderColor,
        strokeWidth: widget.borderWidth,
      );
    } else if (widget.borderStyle == DashboardBorderStyle.dotted) {
      shape = DashedBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        dashPattern: const [2, 2],
        color: borderColor,
        strokeWidth: widget.borderWidth,
      );
    } else {
      shape = RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        side: borderSide,
      );
    }

    // Build the section card
    Widget sectionCard = Card(
      color: backgroundColor,
      elevation: showShadow ? elevation : 0,
      shadowColor: widget.shadowColor,
      shape: shape,
      child: Padding(
        padding: padding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Build section header
            _buildSectionHeader(section, textColor, accentColor, index),

            // Show divider if needed
            if (section.showDivider && !isCollapsed)
              Divider(color: widget.dividerColor),

            // Section content - only show if not collapsed
            if (!isCollapsed)
              Expanded(
                child: section.allowScrolling
                  ? SingleChildScrollView(
                      child: Padding(
                        padding: widget.sectionContentPadding,
                        child: section.content,
                      ),
                    )
                  : Padding(
                      padding: widget.sectionContentPadding,
                      child: section.content,
                    ),
              ),

            // Section footer if provided and not collapsed
            if (section.footer != null && !isCollapsed) section.footer!,
          ],
        ),
      ),
    );

    // Apply interactive features
    if (widget.allowReordering && section.isDraggable && index != null) {
      // Implement draggable section
      sectionCard = LongPressDraggable<int>(
        data: index,
        feedback: SizedBox(
          width: 200,
          height: 100,
          child: Opacity(
            opacity: 0.7,
            child: Card(
              color: backgroundColor,
              elevation: elevation,
              child: Center(
                child: Text(
                  section.title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: textColor,
                  ),
                ),
              ),
            ),
          ),
        ),
        childWhenDragging: Opacity(
          opacity: 0.3,
          child: sectionCard,
        ),
        onDragStarted: () {
          setState(() {
            _draggedSectionIndex = index;
          });
        },
        onDragEnd: (_) {
          setState(() {
            _draggedSectionIndex = null;
          });
        },
        child: DragTarget<int>(
          builder: (context, candidateData, rejectedData) {
            return sectionCard;
          },
          onAcceptWithDetails: (details) {
            final oldIndex = details.data;
            _reorderSections(oldIndex, index);
          },
        ),
      );
    }

    if (widget.allowResizing && section.isResizable) {
      // Implement resizable section
      sectionCard = Stack(
        children: [
          sectionCard,
          Positioned(
            right: 0,
            bottom: 0,
            child: GestureDetector(
              onPanUpdate: (details) {
                // This is a simplified implementation
                // In a real app, we would update the section size
                // based on the drag distance
                if (index != null) {
                  // Update section size in the state
                  setState(() {
                    // For now, we'll just log the resize action
                    debugPrint('Resizing section $index');
                  });
                }
              },
              child: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: accentColor.withAlpha(100),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10),
                  ),
                ),
                child: Icon(
                  Icons.drag_handle,
                  size: 14,
                  color: textColor,
                ),
              ),
            ),
          ),
        ],
      );
    }

    // Apply hover effects if enabled
    if (widget.showHoverEffects) {
      sectionCard = MouseRegion(
        cursor: SystemMouseCursors.click,
        onEnter: (_) {
          setState(() {
            // In a real implementation, we would store hover state
            // For now, we'll just use the built-in hover effect of Card
          });
        },
        onExit: (_) {
          setState(() {
            // In a real implementation, we would clear hover state
          });
        },
        child: sectionCard,
      );
    }

    // Apply gesture detectors
    Widget gestureWrapper = sectionCard;

    // Apply tap callback if provided
    if (widget.onSectionTap != null && index != null) {
      gestureWrapper = GestureDetector(
        onTap: () => widget.onSectionTap!(index),
        child: gestureWrapper,
      );
    }

    // Apply double tap callback if provided
    if (widget.onSectionDoubleTap != null && index != null) {
      gestureWrapper = GestureDetector(
        onDoubleTap: () => widget.onSectionDoubleTap!(index),
        child: gestureWrapper,
      );
    }

    // Apply long press callback if provided
    if (widget.onSectionLongPress != null && index != null) {
      gestureWrapper = GestureDetector(
        onLongPress: () => widget.onSectionLongPress!(index),
        child: gestureWrapper,
      );
    }

    // Apply swipe actions if provided
    if (widget.swipeActions != null && index != null) {
      gestureWrapper = GestureDetector(
        onHorizontalDragEnd: (details) {
          if (details.primaryVelocity == null) return;

          if (details.primaryVelocity! > 0) {
            // Swiped right
            if (widget.swipeActions!.containsKey(SwipeDirection.right)) {
              widget.swipeActions![SwipeDirection.right]!(index);
            }
          } else {
            // Swiped left
            if (widget.swipeActions!.containsKey(SwipeDirection.left)) {
              widget.swipeActions![SwipeDirection.left]!(index);
            }
          }
        },
        onVerticalDragEnd: (details) {
          if (details.primaryVelocity == null) return;

          if (details.primaryVelocity! > 0) {
            // Swiped down
            if (widget.swipeActions!.containsKey(SwipeDirection.down)) {
              widget.swipeActions![SwipeDirection.down]!(index);
            }
          } else {
            // Swiped up
            if (widget.swipeActions!.containsKey(SwipeDirection.up)) {
              widget.swipeActions![SwipeDirection.up]!(index);
            }
          }
        },
        child: gestureWrapper,
      );
    }

    // Apply semantic labels for accessibility
    if (widget.enableScreenReaderAnnouncements) {
      gestureWrapper = Semantics(
        label: section.title,
        enabled: section.isEnabled,
        child: gestureWrapper,
      );
    }

    return gestureWrapper;
  }

  // Helper method to build section header
  Widget _buildSectionHeader(DashboardSection section, Color textColor, Color accentColor, [int? index]) {
    // Use custom header if provided
    if (section.customHeader != null) {
      return section.customHeader!;
    }

    // Check if section is collapsed
    final isCollapsed = index != null && (_collapsedSections[index] ?? false);

    return Container(
      color: widget.sectionHeaderColor,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Title with optional icon
          Expanded(
            child: Row(
              children: [
                if (section.icon != null)
                  Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: Icon(section.icon, color: accentColor, size: 20),
                  ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        section.title,
                        style: widget.sectionTitleStyle ?? TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: textColor,
                          fontFamily: widget.fontFamily,
                        ),
                        overflow: widget.textOverflow ?? TextOverflow.ellipsis,
                      ),
                      if (section.subtitle != null)
                        Text(
                          section.subtitle!,
                          style: TextStyle(
                            fontSize: 14,
                            color: textColor.withAlpha(179), // 0.7 * 255 = 179
                            fontFamily: widget.fontFamily,
                          ),
                          overflow: widget.textOverflow ?? TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Section actions
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Section refresh button
              if (widget.showSectionRefreshButtons && section.hasRefreshButton)
                IconButton(
                  icon: Icon(Icons.refresh, color: accentColor, size: 18),
                  onPressed: section.onRefresh ?? () {},
                  tooltip: widget.localizedStrings?['section_refresh_tooltip'] ?? 'Refresh section',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  iconSize: 18,
                ),

              // Section settings button
              if (widget.showSectionSettingsButtons && section.hasSettingsButton)
                IconButton(
                  icon: Icon(Icons.settings, color: accentColor, size: 18),
                  onPressed: section.onSettings ?? () {},
                  tooltip: widget.localizedStrings?['section_settings_tooltip'] ?? 'Section settings',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  iconSize: 18,
                ),

              // Collapse/expand button
              if (widget.allowSectionCollapse && section.isCollapsible && index != null)
                IconButton(
                  icon: Icon(
                    isCollapsed ? Icons.expand_more : Icons.expand_less,
                    color: accentColor,
                    size: 18,
                  ),
                  onPressed: () => _toggleSectionCollapsed(index),
                  tooltip: isCollapsed
                      ? (widget.localizedStrings?['section_expand_tooltip'] ?? 'Expand section')
                      : (widget.localizedStrings?['section_collapse_tooltip'] ?? 'Collapse section'),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  iconSize: 18,
                ),

              // Close button
              if (widget.allowSectionClose && section.isClosable && index != null)
                IconButton(
                  icon: Icon(Icons.close, color: accentColor, size: 18),
                  onPressed: () => _closeSection(index),
                  tooltip: widget.localizedStrings?['section_close_tooltip'] ?? 'Close section',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  iconSize: 18,
                ),

              // Custom actions
              if (section.actions != null) section.actions!,
            ],
          ),
        ],
      ),
    );
  }
}

/// Represents a section in the dashboard.
class DashboardSection {
  /// The title of the section.
  final String title;

  /// The content widget of the section.
  final Widget content;

  /// Optional actions widget to display in the section header.
  final Widget? actions;

  /// The size of the section (used for layout calculations).
  final DashboardSectionSize size;

  /// Subtitle for the section
  final String? subtitle;

  /// Icon for the section
  final IconData? icon;

  /// Custom background color for this section
  final Color? backgroundColor;

  /// Custom border color for this section
  final Color? borderColor;

  /// Custom text color for this section
  final Color? textColor;

  /// Custom accent color for this section
  final Color? accentColor;

  /// Whether this section is collapsible
  final bool isCollapsible;

  /// Whether this section is initially collapsed
  final bool isInitiallyCollapsed;

  /// Whether this section is closable
  final bool isClosable;

  /// Whether this section is draggable
  final bool isDraggable;

  /// Whether this section is resizable
  final bool isResizable;

  /// Whether this section has a refresh button
  final bool hasRefreshButton;

  /// Callback when the refresh button is pressed
  final VoidCallback? onRefresh;

  /// Whether this section has a settings button
  final bool hasSettingsButton;

  /// Callback when the settings button is pressed
  final VoidCallback? onSettings;

  /// Custom header widget for this section
  final Widget? customHeader;

  /// Custom footer widget for this section
  final Widget? footer;

  /// Whether to show a divider between header and content
  final bool showDivider;

  /// Whether this section allows scrolling
  final bool allowScrolling;

  /// Padding for this section
  final EdgeInsets? padding;

  /// Border radius for this section
  final double? borderRadius;

  /// Whether to show a shadow for this section
  final bool? showShadow;

  /// Elevation for this section's shadow
  final double? elevation;

  /// Minimum height for this section
  final double? minHeight;

  /// Maximum height for this section
  final double? maxHeight;

  /// Minimum width for this section
  final double? minWidth;

  /// Maximum width for this section
  final double? maxWidth;

  /// Whether this section is enabled
  final bool isEnabled;

  /// Whether this section is visible
  final bool isVisible;

  /// Whether this section is fixed (non-scrollable)
  final bool isFixed;

  /// Whether this section spans the full width
  final bool isFullWidth;

  /// Whether this section is in loading state
  final bool isLoading;

  /// Whether this section is in error state
  final bool hasError;

  /// Error message when [hasError] is true
  final String? errorMessage;

  /// Widget to show when in loading state
  final Widget? loadingWidget;

  /// Widget to show when in error state
  final Widget? errorWidget;

  /// Widget to show when empty
  final Widget? emptyWidget;

  /// Whether this section is empty
  final bool isEmpty;

  /// Auto-refresh interval for this section
  final Duration? refreshInterval;

  /// Data source for this section
  final String? dataSource;

  /// Data filter for this section
  final Map<String, dynamic>? dataFilter;

  /// Data sort options for this section
  final Map<String, bool>? dataSort;

  /// Creates a dashboard section.
  const DashboardSection({
    required this.title,
    required this.content,
    this.actions,
    this.size = DashboardSectionSize.medium,
    this.subtitle,
    this.icon,
    this.backgroundColor,
    this.borderColor,
    this.textColor,
    this.accentColor,
    this.isCollapsible = false,
    this.isInitiallyCollapsed = false,
    this.isClosable = false,
    this.isDraggable = true,
    this.isResizable = false,
    this.hasRefreshButton = false,
    this.onRefresh,
    this.hasSettingsButton = false,
    this.onSettings,
    this.customHeader,
    this.footer,
    this.showDivider = true,
    this.allowScrolling = true,
    this.padding,
    this.borderRadius,
    this.showShadow,
    this.elevation,
    this.minHeight,
    this.maxHeight,
    this.minWidth,
    this.maxWidth,
    this.isEnabled = true,
    this.isVisible = true,
    this.isFixed = false,
    this.isFullWidth = false,
    this.isLoading = false,
    this.hasError = false,
    this.errorMessage,
    this.loadingWidget,
    this.errorWidget,
    this.emptyWidget,
    this.isEmpty = false,
    this.refreshInterval,
    this.dataSource,
    this.dataFilter,
    this.dataSort,
  });
}

/// Size options for dashboard sections.
enum DashboardSectionSize {
  /// Small section (1x1 in grid).
  small,

  /// Medium section (1x2 in grid).
  medium,

  /// Large section (2x2 in grid).
  large,

  /// Full width section (spans all columns).
  fullWidth,
}

/// Custom border that supports dashed and dotted styles
class DashedBorder extends ShapeBorder {
  final BorderRadius borderRadius;
  final List<double> dashPattern;
  final Color color;
  final double strokeWidth;

  const DashedBorder({
    required this.borderRadius,
    required this.dashPattern,
    required this.color,
    this.strokeWidth = 1.0,
  });

  @override
  EdgeInsetsGeometry get dimensions => EdgeInsets.all(strokeWidth);

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return Path()
      ..addRRect(borderRadius.toRRect(rect).deflate(strokeWidth));
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    return Path()
      ..addRRect(borderRadius.toRRect(rect));
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    final Paint paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final Path path = getOuterPath(rect, textDirection: textDirection);
    canvas.drawPath(
      dashPath(path, dashPattern: dashPattern),
      paint,
    );
  }

  @override
  ShapeBorder scale(double t) {
    return DashedBorder(
      borderRadius: borderRadius * t,
      dashPattern: dashPattern.map((pattern) => pattern * t).toList(),
      color: color,
      strokeWidth: strokeWidth * t,
    );
  }

  /// Creates a dashed path based on the original path and dash pattern
  Path dashPath(
    Path originalPath, {
    required List<double> dashPattern,
  }) {
    final Path dashPath = Path();
    final double dashLength = dashPattern[0];
    final double dashGapLength = dashPattern.length == 1 ? dashLength : dashPattern[1];

    bool isDash = true;

    for (final PathMetric metric in originalPath.computeMetrics()) {
      double startDistance = 0.0;

      while (startDistance < metric.length) {
        final double currentDistance = isDash ? dashLength : dashGapLength;
        if (startDistance + currentDistance > metric.length) {
          // We've reached the end of the path
          dashPath.addPath(
            metric.extractPath(startDistance, metric.length),
            Offset.zero,
          );
          break;
        } else {
          if (isDash) {
            dashPath.addPath(
              metric.extractPath(startDistance, startDistance + currentDistance),
              Offset.zero,
            );
          }
          startDistance += currentDistance;
          isDash = !isDash;
        }
      }
    }

    return dashPath;
  }
}
