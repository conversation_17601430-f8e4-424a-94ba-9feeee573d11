// To parse this JSON data, do
//
//     final userModel = userModelFromJson(jsonString);

import 'dart:convert';

UserModel userModelFromJson(String str) => UserModel.fromJson(json.decode(str));

String userModelToJson(UserModel data) => json.encode(data.toJson());

class UserModel {
    String? accessToken;
    String? refreshToken;
    String? tokenType;
    int? expiresAt;
    User? user;

    UserModel({
        this.accessToken,
        this.refreshToken,
        this.tokenType,
        this.expiresAt,
        this.user,
    });

    factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
        accessToken: json["access_token"],
        refreshToken: json["refresh_token"],
        tokenType: json["token_type"],
        expiresAt: json["expires_at"],
        user: json["user"] == null ? null : User.from<PERSON><PERSON>(json["user"]),
    );

    Map<String, dynamic> toJson() => {
        "access_token": accessToken,
        "refresh_token": refreshToken,
        "token_type": tokenType,
        "expires_at": expiresAt,
        "user": user?.toJson(),
    };
}

class User {
    String? userId;
    String? username;
    String? email;
    String? firstName;
    String? lastName;
    String? status;
    List<String>? roles;
    String? tenantId;
    bool? disabled;
    DateTime? createdAt;

    User({
        this.userId,
        this.username,
        this.email,
        this.firstName,
        this.lastName,
        this.status,
        this.roles,
        this.tenantId,
        this.disabled,
        this.createdAt,
    });

    factory User.fromJson(Map<String, dynamic> json) => User(
        userId: json["user_id"],
        username: json["username"],
        email: json["email"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        status: json["status"],
        roles: json["roles"] == null ? [] : List<String>.from(json["roles"]!.map((x) => x)),
        tenantId: json["tenant_id"],
        disabled: json["disabled"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    );

    Map<String, dynamic> toJson() => {
        "user_id": userId,
        "username": username,
        "email": email,
        "first_name": firstName,
        "last_name": lastName,
        "status": status,
        "roles": roles == null ? [] : List<dynamic>.from(roles!.map((x) => x)),
        "tenant_id": tenantId,
        "disabled": disabled,
        "created_at": createdAt?.toIso8601String(),
    };
}
