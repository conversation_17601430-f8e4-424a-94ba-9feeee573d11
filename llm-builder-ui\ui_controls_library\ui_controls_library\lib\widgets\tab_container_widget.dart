import 'package:flutter/material.dart';

/// A highly configurable tab-based container widget that can display multiple views.
///
/// This widget wraps Flutter's TabBar and TabBarView components and provides
/// additional customization options for creating tab-based interfaces.
class TabContainerWidget extends StatefulWidget {
  /// The list of tab contents to display.
  final List<Widget> tabContents;

  /// The list of tab labels.
  final List<String> tabLabels;

  /// Optional list of icons to display in the tabs.
  final List<IconData>? tabIcons;

  /// Whether to show icons in the tabs.
  final bool showIcons;

  /// Whether to show labels in the tabs.
  final bool showLabels;

  /// Whether icons should be displayed before labels.
  final bool iconBeforeLabel;

  /// The position of the tab bar.
  final TabPosition tabPosition;

  /// The alignment of the tabs within the tab bar.
  final TabAlignment tabAlignment;

  /// The style of the tabs.
  final TabStyle tabStyle;

  /// Optional list of individual styles for each tab.
  /// If provided, must have the same length as tabLabels.
  final List<TabStyle>? individualTabStyles;

  /// Optional list of individual background colors for each tab.
  /// If provided, must have the same length as tabLabels.
  final List<Color>? individualTabColors;

  /// Optional custom builder for tab content.
  /// This is useful when deserializing from JSON to create custom content.
  final Widget Function(String label, int index)? contentBuilder;

  /// The initial tab index to select.
  final int initialIndex;

  /// Whether tabs are scrollable.
  final bool isScrollable;

  /// Background color of the tab container.
  final Color backgroundColor;

  /// Background color of the tab bar.
  final Color tabBarColor;

  /// Color of the selected tab indicator.
  final Color indicatorColor;

  /// Color of the selected tab label and icon.
  final Color selectedLabelColor;

  /// Color of the unselected tab labels and icons.
  final Color unselectedLabelColor;

  /// Width of the tab indicator.
  final double indicatorWeight;

  /// Whether to show a divider between the tab bar and content.
  final bool showDivider;

  /// Color of the divider.
  final Color dividerColor;

  /// Width of the divider.
  final double dividerThickness;

  /// Padding inside the tab container.
  final EdgeInsetsGeometry padding;

  /// Margin around the tab container.
  final EdgeInsetsGeometry margin;

  /// Width of the tab container.
  final double? width;

  /// Height of the tab container.
  final double? height;

  /// Whether to show a border around the tab container.
  final bool hasBorder;

  /// Color for the tab container's border.
  final Color borderColor;

  /// Width of the tab container's border.
  final double borderWidth;

  /// Radius of the tab container's corners.
  final double borderRadius;

  /// Whether to show a shadow under the tab container.
  final bool hasShadow;

  /// Elevation of the tab container (affects shadow).
  final double elevation;

  /// Font size for the tab labels.
  final double labelFontSize;

  /// Font weight for the tab labels.
  final FontWeight labelFontWeight;

  /// Size of the tab icons.
  final double iconSize;

  /// Whether to use a dark theme.
  final bool isDarkTheme;

  /// Whether the tab container is disabled.
  final bool isDisabled;

  /// Callback when a tab is selected.
  final void Function(int)? onTabSelected;

  /// Whether to animate tab transitions.
  final bool animateTransitions;

  /// Duration of the tab transition animation.
  final Duration animationDuration;

  /// Whether tabs can be closed.
  final bool closableTabs;

  /// Callback when a tab is closed.
  final void Function(int)? onTabClosed;

  /// Creates a tab container widget with the specified properties.
  const TabContainerWidget({
    super.key,
    required this.tabContents,
    required this.tabLabels,
    this.tabIcons,
    this.showIcons = true,
    this.showLabels = true,
    this.iconBeforeLabel = true,
    this.tabPosition = TabPosition.top,
    this.tabAlignment = TabAlignment.start,
    this.tabStyle = TabStyle.fixed,
    this.individualTabStyles,
    this.individualTabColors,
    this.contentBuilder,
    this.initialIndex = 0,
    this.isScrollable = false,
    this.backgroundColor = Colors.white,
    this.tabBarColor = Colors.white,
    this.indicatorColor = Colors.blue,
    this.selectedLabelColor = Colors.blue,
    this.unselectedLabelColor = Colors.grey,
    this.indicatorWeight = 2.0,
    this.showDivider = true,
    this.dividerColor = Colors.grey,
    this.dividerThickness = 1.0,
    this.padding = const EdgeInsets.all(16.0),
    this.margin = const EdgeInsets.all(8.0),
    this.width,
    this.height,
    this.hasBorder = false,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.hasShadow = true,
    this.elevation = 2.0,
    this.labelFontSize = 14.0,
    this.labelFontWeight = FontWeight.normal,
    this.iconSize = 20.0,
    this.isDarkTheme = false,
    this.isDisabled = false,
    this.onTabSelected,
    this.animateTransitions = true,
    this.animationDuration = const Duration(milliseconds: 300),
    this.closableTabs = false,
    this.onTabClosed,
  }) : assert(tabContents.length == tabLabels.length,
            'The number of tab contents must match the number of tab labels'),
       assert(tabIcons == null || tabIcons.length == tabLabels.length,
            'If provided, the number of tab icons must match the number of tab labels'),
       assert(individualTabStyles == null || individualTabStyles.length == tabLabels.length,
            'If provided, the number of individual tab styles must match the number of tab labels'),
       assert(individualTabColors == null || individualTabColors.length == tabLabels.length,
            'If provided, the number of individual tab colors must match the number of tab labels');

  @override
  State<TabContainerWidget> createState() => _TabContainerWidgetState();

  /// Creates a TabContainerWidget from a JSON map.
  ///
  /// This factory constructor allows creating a TabContainerWidget from a JSON object,
  /// making it easy to configure the widget dynamically.
  factory TabContainerWidget.fromJson(Map<String, dynamic> json) {
    // Helper function to get a color from a string or int
    Color getColorFromValue(dynamic value) {
      if (value is String) {
        if (value.startsWith('#')) {
          // Handle hex color
          final hex = value.replaceFirst('#', '');
          final hexValue = int.tryParse(hex, radix: 16);
          if (hexValue != null) {
            return Color(hexValue | 0xFF000000); // Add alpha if needed
          }
        }

        // Handle named colors
        switch (value.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'transparent': return Colors.transparent;
          default: return Colors.blue; // Default color
        }
      } else if (value is int) {
        return Color(value);
      }
      return Colors.blue; // Default color
    }

    // Helper function to get a font weight from a string or int
    FontWeight getFontWeightFromValue(dynamic value) {
      if (value is String) {
        switch (value.toLowerCase()) {
          case 'bold': return FontWeight.bold;
          case 'normal': return FontWeight.normal;
          case 'light': return FontWeight.w300;
          case 'medium': return FontWeight.w500;
          case 'semibold': return FontWeight.w600;
          case 'extrabold': return FontWeight.w800;
          case 'black': return FontWeight.w900;
          default: return FontWeight.normal;
        }
      } else if (value is int) {
        return FontWeight.values.firstWhere(
          (weight) => weight.index == value,
          orElse: () => FontWeight.normal,
        );
      }
      return FontWeight.normal;
    }

    // Helper function to get an icon from a string
    IconData? getIconFromValue(dynamic value) {
      if (value is String) {
        switch (value.toLowerCase()) {
          case 'home': return Icons.home;
          case 'settings': return Icons.settings;
          case 'person': return Icons.person;
          case 'info': return Icons.info;
          case 'help': return Icons.help;
          case 'about': return Icons.info_outline;
          case 'contact': return Icons.contact_mail;
          case 'email': return Icons.email;
          case 'phone': return Icons.phone;
          case 'message': return Icons.message;
          case 'chat': return Icons.chat;
          case 'notification': return Icons.notifications;
          case 'search': return Icons.search;
          case 'favorite': return Icons.favorite;
          case 'star': return Icons.star;
          case 'bookmark': return Icons.bookmark;
          case 'list': return Icons.list;
          case 'grid': return Icons.grid_view;
          case 'dashboard': return Icons.dashboard;
          case 'menu': return Icons.menu;
          case 'more': return Icons.more_horiz;
          case 'add': return Icons.add;
          case 'remove': return Icons.remove;
          case 'edit': return Icons.edit;
          case 'delete': return Icons.delete;
          case 'save': return Icons.save;
          case 'upload': return Icons.upload;
          case 'download': return Icons.download;
          case 'share': return Icons.share;
          case 'link': return Icons.link;
          case 'attach': return Icons.attach_file;
          case 'camera': return Icons.camera_alt;
          case 'photo': return Icons.photo;
          case 'image': return Icons.image;
          case 'video': return Icons.videocam;
          case 'music': return Icons.music_note;
          case 'audio': return Icons.audiotrack;
          case 'file': return Icons.insert_drive_file;
          case 'folder': return Icons.folder;
          case 'cloud': return Icons.cloud;
          case 'wifi': return Icons.wifi;
          case 'bluetooth': return Icons.bluetooth;
          case 'location': return Icons.location_on;
          case 'map': return Icons.map;
          case 'navigation': return Icons.navigation;
          case 'calendar': return Icons.calendar_today;
          case 'event': return Icons.event;
          case 'time': return Icons.access_time;
          case 'alarm': return Icons.alarm;
          case 'timer': return Icons.timer;
          case 'watch': return Icons.watch;
          case 'lock': return Icons.lock;
          case 'unlock': return Icons.lock_open;
          case 'security': return Icons.security;
          case 'key': return Icons.vpn_key;
          case 'warning': return Icons.warning;
          case 'error': return Icons.error;
          case 'success': return Icons.check_circle;
          case 'check': return Icons.check;
          case 'close': return Icons.close;
          case 'cancel': return Icons.cancel;
          case 'done': return Icons.done;
          case 'undo': return Icons.undo;
          case 'redo': return Icons.redo;
          case 'refresh': return Icons.refresh;
          case 'sync': return Icons.sync;
          case 'update': return Icons.update;
          case 'history': return Icons.history;
          case 'visibility': return Icons.visibility;
          case 'visibility_off': return Icons.visibility_off;
          case 'mic': return Icons.mic;
          case 'mic_off': return Icons.mic_off;
          case 'volume': return Icons.volume_up;
          case 'volume_off': return Icons.volume_off;
          case 'play': return Icons.play_arrow;
          case 'pause': return Icons.pause;
          case 'stop': return Icons.stop;
          case 'skip_next': return Icons.skip_next;
          case 'skip_previous': return Icons.skip_previous;
          case 'fast_forward': return Icons.fast_forward;
          case 'fast_rewind': return Icons.fast_rewind;
          case 'repeat': return Icons.repeat;
          case 'shuffle': return Icons.shuffle;
          case 'playlist': return Icons.playlist_play;
          default: return null;
        }
      }
      return null;
    }

    // Helper function to get EdgeInsets from value
    EdgeInsetsGeometry getEdgeInsetsFromValue(dynamic value) {
      if (value is String) {
        // Parse formats like "16.0" or "16.0,8.0,16.0,8.0" (top,right,bottom,left)
        final parts = value.split(',');
        if (parts.length == 1) {
          final all = double.tryParse(parts[0]) ?? 16.0;
          return EdgeInsets.all(all);
        } else if (parts.length == 2) {
          final vertical = double.tryParse(parts[0]) ?? 16.0;
          final horizontal = double.tryParse(parts[1]) ?? 16.0;
          return EdgeInsets.symmetric(vertical: vertical, horizontal: horizontal);
        } else if (parts.length == 4) {
          final top = double.tryParse(parts[0]) ?? 16.0;
          final right = double.tryParse(parts[1]) ?? 16.0;
          final bottom = double.tryParse(parts[2]) ?? 16.0;
          final left = double.tryParse(parts[3]) ?? 16.0;
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (value is double) {
        return EdgeInsets.all(value);
      } else if (value is int) {
        return EdgeInsets.all(value.toDouble());
      } else if (value is Map) {
        final top = (value['top'] as num?)?.toDouble() ?? 0.0;
        final right = (value['right'] as num?)?.toDouble() ?? 0.0;
        final bottom = (value['bottom'] as num?)?.toDouble() ?? 0.0;
        final left = (value['left'] as num?)?.toDouble() ?? 0.0;

        if (value.containsKey('all')) {
          final all = (value['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (value.containsKey('horizontal') || value.containsKey('vertical')) {
          final horizontal = (value['horizontal'] as num?)?.toDouble() ?? 0.0;
          final vertical = (value['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(horizontal: horizontal, vertical: vertical);
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      }
      return const EdgeInsets.all(16.0);
    }

    // Helper function to get TabPosition from string
    TabPosition getTabPositionFromValue(dynamic value) {
      if (value is String) {
        switch (value.toLowerCase()) {
          case 'top': return TabPosition.top;
          case 'bottom': return TabPosition.bottom;
          case 'left': return TabPosition.left;
          case 'right': return TabPosition.right;
          default: return TabPosition.top;
        }
      }
      return TabPosition.top;
    }

    // Helper function to get TabAlignment from string
    TabAlignment getTabAlignmentFromValue(dynamic value) {
      if (value is String) {
        switch (value.toLowerCase()) {
          case 'start': return TabAlignment.start;
          case 'center': return TabAlignment.center;
          case 'end': return TabAlignment.end;
          case 'fill': return TabAlignment.fill;
          default: return TabAlignment.start;
        }
      }
      return TabAlignment.start;
    }

    // Helper function to get TabStyle from string
    TabStyle getTabStyleFromValue(dynamic value) {
      if (value is String) {
        switch (value.toLowerCase()) {
          case 'fixed': return TabStyle.fixed;
          case 'scrollable': return TabStyle.scrollable;
          case 'button': return TabStyle.button;
          case 'pill': return TabStyle.pill;
          case 'underlined': return TabStyle.underlined;
          default: return TabStyle.fixed;
        }
      }
      return TabStyle.fixed;
    }

    // Extract tab labels and contents
    List<String> tabLabels = [];
    List<Widget> tabContents = [];
    List<IconData>? tabIcons;

    // Parse tab labels
    if (json.containsKey('tabLabels')) {
      if (json['tabLabels'] is List) {
        tabLabels = (json['tabLabels'] as List).map((e) => e.toString()).toList();
      } else if (json['tabLabels'] is String) {
        tabLabels = (json['tabLabels'] as String).split(',').map((e) => e.trim()).toList();
      }
    }

    // If no labels provided, use default ones
    if (tabLabels.isEmpty) {
      tabLabels = ['Tab 1', 'Tab 2', 'Tab 3'];
    }

    // Create tab contents
    if (json.containsKey('tabContents') && json['tabContents'] is List) {
      // If specific content is provided, use it
      final contentList = json['tabContents'] as List;

      // Check if a custom content builder is provided
      final contentBuilder = json['contentBuilder'] as Widget Function(String, int)?;

      if (contentBuilder != null) {
        // Use the custom content builder
        tabContents = List.generate(tabLabels.length, (index) {
          return contentBuilder(tabLabels[index], index);
        });
      } else {
        // Use the default content creation
        tabContents = contentList.map((content) {
          if (content is String) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  content,
                  style: const TextStyle(fontSize: 18.0),
                ),
              ),
            );
          } else {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Content for Tab',
                  style: const TextStyle(fontSize: 18.0),
                ),
              ),
            );
          }
        }).toList();
      }
    } else {
      // Create default content based on labels
      tabContents = tabLabels.map((label) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Content for $label',
              style: const TextStyle(fontSize: 18.0),
            ),
          ),
        );
      }).toList();
    }

    // Parse individual tab styles
    List<TabStyle>? individualTabStyles;
    if (json.containsKey('individualTabStyles') && json['individualTabStyles'] is List) {
      final stylesList = json['individualTabStyles'] as List;
      individualTabStyles = stylesList.map((style) => getTabStyleFromValue(style)).toList();

      // Make sure the number of styles matches the number of tabs
      if (individualTabStyles.length != tabLabels.length) {
        if (individualTabStyles.length < tabLabels.length) {
          // Add default styles to match the number of tabs
          final defaultStyle = json.containsKey('tabStyle')
              ? getTabStyleFromValue(json['tabStyle'])
              : TabStyle.fixed;
          individualTabStyles = [
            ...individualTabStyles,
            ...List<TabStyle>.filled(tabLabels.length - individualTabStyles.length, defaultStyle)
          ];
        } else {
          // Truncate the list to match the number of tabs
          individualTabStyles = individualTabStyles.sublist(0, tabLabels.length);
        }
      }
    }

    // Parse individual tab colors
    List<Color>? individualTabColors;
    if (json.containsKey('individualTabColors') && json['individualTabColors'] is List) {
      final colorsList = json['individualTabColors'] as List;
      individualTabColors = colorsList.map((color) => getColorFromValue(color)).toList();

      // Make sure the number of colors matches the number of tabs
      if (individualTabColors.length != tabLabels.length) {
        if (individualTabColors.length < tabLabels.length) {
          // Add default colors to match the number of tabs
          final defaultColor = json.containsKey('tabBarColor')
              ? getColorFromValue(json['tabBarColor'])
              : Colors.white;
          individualTabColors = [
            ...individualTabColors,
            ...List<Color>.filled(tabLabels.length - individualTabColors.length, defaultColor)
          ];
        } else {
          // Truncate the list to match the number of tabs
          individualTabColors = individualTabColors.sublist(0, tabLabels.length);
        }
      }
    }

    // Parse tab icons
    if (json.containsKey('tabIcons') && json['tabIcons'] is List) {
      final iconList = json['tabIcons'] as List;
      tabIcons = iconList.map((icon) => getIconFromValue(icon)).whereType<IconData>().toList();

      // Make sure the number of icons matches the number of tabs
      if (tabIcons.isNotEmpty && tabIcons.length != tabLabels.length) {
        if (tabIcons.length < tabLabels.length) {
          // Add null icons to match the number of tabs
          tabIcons = [...tabIcons, ...List<IconData?>.filled(tabLabels.length - tabIcons.length, null)]
              .whereType<IconData>().toList();
        } else {
          // Truncate the list to match the number of tabs
          tabIcons = tabIcons.sublist(0, tabLabels.length);
        }
      }
    }

    // Return the TabContainerWidget with properties from JSON
    return TabContainerWidget(
      tabContents: tabContents,
      tabLabels: tabLabels,
      tabIcons: tabIcons,
      showIcons: json['showIcons'] as bool? ?? true,
      showLabels: json['showLabels'] as bool? ?? true,
      iconBeforeLabel: json['iconBeforeLabel'] as bool? ?? true,
      tabPosition: json.containsKey('tabPosition')
          ? getTabPositionFromValue(json['tabPosition'])
          : TabPosition.top,
      tabAlignment: json.containsKey('tabAlignment')
          ? getTabAlignmentFromValue(json['tabAlignment'])
          : TabAlignment.start,
      tabStyle: json.containsKey('tabStyle')
          ? getTabStyleFromValue(json['tabStyle'])
          : TabStyle.fixed,
      individualTabStyles: individualTabStyles,
      individualTabColors: individualTabColors,
      contentBuilder: json['contentBuilder'] as Widget Function(String, int)?,
      initialIndex: (json['initialIndex'] as int?) ?? 0,
      isScrollable: json['isScrollable'] as bool? ?? false,
      backgroundColor: json.containsKey('backgroundColor')
          ? getColorFromValue(json['backgroundColor'])
          : Colors.white,
      tabBarColor: json.containsKey('tabBarColor')
          ? getColorFromValue(json['tabBarColor'])
          : Colors.white,
      indicatorColor: json.containsKey('indicatorColor')
          ? getColorFromValue(json['indicatorColor'])
          : Colors.blue,
      selectedLabelColor: json.containsKey('selectedLabelColor')
          ? getColorFromValue(json['selectedLabelColor'])
          : Colors.blue,
      unselectedLabelColor: json.containsKey('unselectedLabelColor')
          ? getColorFromValue(json['unselectedLabelColor'])
          : Colors.grey,
      indicatorWeight: (json['indicatorWeight'] as num?)?.toDouble() ?? 2.0,
      showDivider: json['showDivider'] as bool? ?? true,
      dividerColor: json.containsKey('dividerColor')
          ? getColorFromValue(json['dividerColor'])
          : Colors.grey,
      dividerThickness: (json['dividerThickness'] as num?)?.toDouble() ?? 1.0,
      padding: json.containsKey('padding')
          ? getEdgeInsetsFromValue(json['padding'])
          : const EdgeInsets.all(16.0),
      margin: json.containsKey('margin')
          ? getEdgeInsetsFromValue(json['margin'])
          : const EdgeInsets.all(8.0),
      width: (json['width'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      hasBorder: json['hasBorder'] as bool? ?? false,
      borderColor: json.containsKey('borderColor')
          ? getColorFromValue(json['borderColor'])
          : Colors.grey,
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 8.0,
      hasShadow: json['hasShadow'] as bool? ?? true,
      elevation: (json['elevation'] as num?)?.toDouble() ?? 2.0,
      labelFontSize: (json['labelFontSize'] as num?)?.toDouble() ?? 14.0,
      labelFontWeight: json.containsKey('labelFontWeight')
          ? getFontWeightFromValue(json['labelFontWeight'])
          : FontWeight.normal,
      iconSize: (json['iconSize'] as num?)?.toDouble() ?? 20.0,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      animateTransitions: json['animateTransitions'] as bool? ?? true,
      animationDuration: json.containsKey('animationDuration')
          ? Duration(milliseconds: (json['animationDuration'] as int?) ?? 300)
          : const Duration(milliseconds: 300),
      closableTabs: json['closableTabs'] as bool? ?? false,
      onTabSelected: null, // Callbacks can't be serialized
      onTabClosed: null, // Callbacks can't be serialized
    );
  }

  /// Converts the TabContainerWidget to a JSON map.
  ///
  /// This method allows serializing the widget's configuration to JSON,
  /// making it easy to save and restore widget states.
  Map<String, dynamic> toJson() {
    // Helper function to convert Color to hex string
    String colorToHex(Color color) {
      return '#${color.toString().split('(0x')[1].split(')')[0].substring(2)}';
    }

    // Helper function to convert TabPosition to string
    String tabPositionToString(TabPosition position) {
      switch (position) {
        case TabPosition.top: return 'top';
        case TabPosition.bottom: return 'bottom';
        case TabPosition.left: return 'left';
        case TabPosition.right: return 'right';
      }
    }

    // Helper function to convert TabAlignment to string
    String tabAlignmentToString(TabAlignment alignment) {
      switch (alignment) {
        case TabAlignment.start: return 'start';
        case TabAlignment.center: return 'center';
        case TabAlignment.end: return 'end';
        case TabAlignment.fill: return 'fill';
      }
    }

    // Helper function to convert TabStyle to string
    String tabStyleToString(TabStyle style) {
      switch (style) {
        case TabStyle.fixed: return 'fixed';
        case TabStyle.scrollable: return 'scrollable';
        case TabStyle.button: return 'button';
        case TabStyle.pill: return 'pill';
        case TabStyle.underlined: return 'underlined';
      }
    }

    // Helper function to convert a list of TabStyles to strings
    List<String>? tabStylesToStrings(List<TabStyle>? styles) {
      if (styles == null) return null;
      return styles.map((style) => tabStyleToString(style)).toList();
    }

    // Helper function to convert a list of Colors to hex strings
    List<String>? colorsToHexStrings(List<Color>? colors) {
      if (colors == null) return null;
      return colors.map((color) => colorToHex(color)).toList();
    }

    return {
      'tabLabels': tabLabels,
      // We can't serialize the actual widget content, so we'll just indicate the number of tabs
      'tabContentsCount': tabContents.length,
      'showIcons': showIcons,
      'showLabels': showLabels,
      'iconBeforeLabel': iconBeforeLabel,
      'tabPosition': tabPositionToString(tabPosition),
      'tabAlignment': tabAlignmentToString(tabAlignment),
      'tabStyle': tabStyleToString(tabStyle),
      'individualTabStyles': tabStylesToStrings(individualTabStyles),
      'individualTabColors': colorsToHexStrings(individualTabColors),
      // We can't serialize the contentBuilder function
      'initialIndex': initialIndex,
      'isScrollable': isScrollable,
      'backgroundColor': colorToHex(backgroundColor),
      'tabBarColor': colorToHex(tabBarColor),
      'indicatorColor': colorToHex(indicatorColor),
      'selectedLabelColor': colorToHex(selectedLabelColor),
      'unselectedLabelColor': colorToHex(unselectedLabelColor),
      'indicatorWeight': indicatorWeight,
      'showDivider': showDivider,
      'dividerColor': colorToHex(dividerColor),
      'dividerThickness': dividerThickness,
      'padding': {
        'all': padding is EdgeInsets ? (padding as EdgeInsets).top : null,
      },
      'margin': {
        'all': margin is EdgeInsets ? (margin as EdgeInsets).top : null,
      },
      'width': width,
      'height': height,
      'hasBorder': hasBorder,
      'borderColor': colorToHex(borderColor),
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'labelFontSize': labelFontSize,
      'labelFontWeight': labelFontWeight.toString(),
      'iconSize': iconSize,
      'isDarkTheme': isDarkTheme,
      'isDisabled': isDisabled,
      'animateTransitions': animateTransitions,
      'animationDuration': animationDuration.inMilliseconds,
      'closableTabs': closableTabs,
    };
  }
}

/// The position of the tab bar relative to the content.
enum TabPosition {
  /// Tab bar is positioned at the top of the content.
  top,

  /// Tab bar is positioned at the bottom of the content.
  bottom,

  /// Tab bar is positioned to the left of the content.
  left,

  /// Tab bar is positioned to the right of the content.
  right,
}

/// The alignment of the tabs within the tab bar.
enum TabAlignment {
  /// Tabs are aligned to the start of the tab bar.
  start,

  /// Tabs are aligned to the center of the tab bar.
  center,

  /// Tabs are aligned to the end of the tab bar.
  end,

  /// Tabs are stretched to fill the tab bar.
  fill,
}

/// The style of the tabs.
enum TabStyle {
  /// Fixed tabs with equal width.
  fixed,

  /// Scrollable tabs with variable width.
  scrollable,

  /// Tabs with a button-like appearance.
  button,

  /// Tabs with a pill-shaped appearance.
  pill,

  /// Tabs with an underlined appearance.
  underlined,
}

class _TabContainerWidgetState extends State<TabContainerWidget> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late List<String> _tabLabels;
  late List<Widget> _tabContents;
  late List<IconData>? _tabIcons;
  List<TabStyle>? _individualTabStyles;
  List<Color>? _individualTabColors;

  @override
  void initState() {
    super.initState();
    _tabLabels = List.from(widget.tabLabels);
    _tabContents = List.from(widget.tabContents);
    _tabIcons = widget.tabIcons != null ? List.from(widget.tabIcons!) : null;
    _individualTabStyles = widget.individualTabStyles != null ? List.from(widget.individualTabStyles!) : null;
    _individualTabColors = widget.individualTabColors != null ? List.from(widget.individualTabColors!) : null;
    _tabController = TabController(
      length: _tabLabels.length,
      vsync: this,
      initialIndex: widget.initialIndex < _tabLabels.length ? widget.initialIndex : 0,
    );
    _tabController.addListener(_handleTabSelection);
  }

  @override
  void didUpdateWidget(TabContainerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.tabLabels != widget.tabLabels ||
        oldWidget.tabContents != widget.tabContents ||
        oldWidget.tabIcons != widget.tabIcons ||
        oldWidget.individualTabStyles != widget.individualTabStyles ||
        oldWidget.individualTabColors != widget.individualTabColors) {
      _tabLabels = List.from(widget.tabLabels);
      _tabContents = List.from(widget.tabContents);
      _tabIcons = widget.tabIcons != null ? List.from(widget.tabIcons!) : null;
      _individualTabStyles = widget.individualTabStyles != null ? List.from(widget.individualTabStyles!) : null;
      _individualTabColors = widget.individualTabColors != null ? List.from(widget.individualTabColors!) : null;

      // Recreate the tab controller if the number of tabs has changed
      if (oldWidget.tabLabels.length != widget.tabLabels.length) {
        _tabController.dispose();
        _tabController = TabController(
          length: _tabLabels.length,
          vsync: this,
          initialIndex: widget.initialIndex < _tabLabels.length ? widget.initialIndex : 0,
        );
        _tabController.addListener(_handleTabSelection);
      }
    }

    // Update the selected tab if the initial index has changed
    if (oldWidget.initialIndex != widget.initialIndex &&
        widget.initialIndex < _tabLabels.length &&
        _tabController.index != widget.initialIndex) {
      _tabController.animateTo(widget.initialIndex);
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabSelection);
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabSelection() {
    if (_tabController.indexIsChanging) {
      if (widget.onTabSelected != null) {
        widget.onTabSelected!(_tabController.index);
      }
    }
  }

  void _handleTabClose(int index) {
    if (widget.onTabClosed != null) {
      widget.onTabClosed!(index);
    }

    // Remove the tab if there's more than one
    if (_tabLabels.length > 1) {
      setState(() {
        _tabLabels.removeAt(index);
        _tabContents.removeAt(index);
        if (_tabIcons != null) {
          _tabIcons!.removeAt(index);
        }
        if (_individualTabStyles != null) {
          _individualTabStyles!.removeAt(index);
        }
        if (_individualTabColors != null) {
          _individualTabColors!.removeAt(index);
        }

        // Recreate the tab controller
        final newIndex = index > 0 ? index - 1 : 0;
        _tabController.dispose();
        _tabController = TabController(
          length: _tabLabels.length,
          vsync: this,
          initialIndex: newIndex,
        );
        _tabController.addListener(_handleTabSelection);
      });
    }
  }

  /// Adds a new tab with the specified label, content, and optional icon.
  ///
  /// This method allows dynamically adding tabs to the TabContainer after it has been created.
  void addTab(String label, Widget content, {IconData? icon, TabStyle? style, Color? color}) {
    setState(() {
      _tabLabels.add(label);
      _tabContents.add(content);

      // Add icon if provided and if we're using icons
      if (_tabIcons != null) {
        _tabIcons!.add(icon ?? Icons.circle);
      }

      // Add style if using individual styles
      if (_individualTabStyles != null) {
        _individualTabStyles!.add(style ?? widget.tabStyle);
      }

      // Add color if using individual colors
      if (_individualTabColors != null) {
        _individualTabColors!.add(color ?? widget.tabBarColor);
      }

      // Recreate the tab controller with the new length
      final currentIndex = _tabController.index;
      _tabController.dispose();
      _tabController = TabController(
        length: _tabLabels.length,
        vsync: this,
        initialIndex: currentIndex < _tabLabels.length - 1 ? currentIndex : _tabLabels.length - 1,
      );
      _tabController.addListener(_handleTabSelection);
    });
  }

  /// Removes the tab at the specified index.
  ///
  /// This method allows dynamically removing tabs from the TabContainer after it has been created.
  void removeTab(int index) {
    if (index < 0 || index >= _tabLabels.length) {
      throw ArgumentError('Index out of range: $index');
    }

    if (_tabLabels.length <= 1) {
      throw StateError('Cannot remove the last tab');
    }

    setState(() {
      _tabLabels.removeAt(index);
      _tabContents.removeAt(index);

      if (_tabIcons != null) {
        _tabIcons!.removeAt(index);
      }

      if (_individualTabStyles != null) {
        _individualTabStyles!.removeAt(index);
      }

      if (_individualTabColors != null) {
        _individualTabColors!.removeAt(index);
      }

      // Recreate the tab controller with the new length
      final currentIndex = _tabController.index;
      _tabController.dispose();
      _tabController = TabController(
        length: _tabLabels.length,
        vsync: this,
        initialIndex: currentIndex >= index ?
            (currentIndex > 0 ? currentIndex - 1 : 0) :
            currentIndex,
      );
      _tabController.addListener(_handleTabSelection);

      // Call the onTabClosed callback if provided
      if (widget.onTabClosed != null) {
        widget.onTabClosed!(index);
      }
    });
  }

  /// Updates the tab at the specified index with new label, content, and optional icon.
  ///
  /// This method allows dynamically updating tabs in the TabContainer after it has been created.
  void updateTab(int index, {String? label, Widget? content, IconData? icon, TabStyle? style, Color? color}) {
    if (index < 0 || index >= _tabLabels.length) {
      throw ArgumentError('Index out of range: $index');
    }

    setState(() {
      if (label != null) {
        _tabLabels[index] = label;
      }

      if (content != null) {
        _tabContents[index] = content;
      }

      if (icon != null && _tabIcons != null) {
        _tabIcons![index] = icon;
      }

      if (style != null && _individualTabStyles != null) {
        _individualTabStyles![index] = style;
      }

      if (color != null && _individualTabColors != null) {
        _individualTabColors![index] = color;
      }
    });
  }

  Widget _buildTab(int index) {
    final hasIcon = widget.showIcons && _tabIcons != null && index < _tabIcons!.length;
    final hasLabel = widget.showLabels && index < _tabLabels.length;

    // Get individual tab style if available
    final tabStyle = _individualTabStyles != null && index < _individualTabStyles!.length
        ? _individualTabStyles![index]
        : widget.tabStyle;

    // Get individual tab color if available
    final tabColor = _individualTabColors != null && index < _individualTabColors!.length
        ? _individualTabColors![index]
        : widget.tabBarColor;

    // Create the tab content
    List<Widget> tabContent = [];

    // Add icon if needed
    if (hasIcon) {
      tabContent.add(
        Icon(
          _tabIcons![index],
          size: widget.iconSize,
          color: _tabController.index == index
              ? widget.selectedLabelColor
              : widget.unselectedLabelColor,
        ),
      );

      // Add spacing if both icon and label are shown
      if (hasLabel) {
        tabContent.add(const SizedBox(width: 8));
      }
    }

    // Add label if needed
    if (hasLabel) {
      tabContent.add(
        Text(
          _tabLabels[index],
          style: TextStyle(
            fontSize: widget.labelFontSize,
            fontWeight: _tabController.index == index
                ? widget.labelFontWeight
                : FontWeight.normal,
            color: _tabController.index == index
                ? widget.selectedLabelColor
                : widget.unselectedLabelColor,
          ),
        ),
      );
    }

    // Create the tab with individual styling
    Widget tab = Tab(
      child: Container(
        decoration: _tabController.index == index &&
                  (tabStyle == TabStyle.pill || tabStyle == TabStyle.button)
            ? BoxDecoration(
                borderRadius: BorderRadius.circular(
                  tabStyle == TabStyle.pill ? 30 : 4
                ),
                color: widget.indicatorColor,
              )
            : BoxDecoration(
                color: _tabController.index == index ? null : tabColor,
              ),
        padding: EdgeInsets.symmetric(
          horizontal: tabStyle == TabStyle.pill ? 16 : 8,
          vertical: tabStyle == TabStyle.pill ? 8 : 4,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: widget.iconBeforeLabel ? tabContent : tabContent.reversed.toList(),
        ),
      ),
    );

    // Add close button if tabs are closable
    if (widget.closableTabs) {
      tab = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          tab,
          const SizedBox(width: 4),
          InkWell(
            onTap: () => _handleTabClose(index),
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.black12,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.close,
                size: 12,
                color: _tabController.index == index
                    ? widget.selectedLabelColor
                    : widget.unselectedLabelColor,
              ),
            ),
          ),
        ],
      );
    }

    return tab;
  }

  Widget _buildTabBar() {
    // Determine if we need scrollable tabs based on individual styles
    bool needsScrollable = widget.isScrollable;
    if (_individualTabStyles != null) {
      needsScrollable = needsScrollable ||
                        _individualTabStyles!.any((style) => style == TabStyle.scrollable);
    } else {
      needsScrollable = needsScrollable || widget.tabStyle == TabStyle.scrollable;
    }

    // Determine the indicator based on tab styles
    Decoration? indicator;
    if (_individualTabStyles == null) {
      // Use global tab style for indicator
      if (widget.tabStyle == TabStyle.pill) {
        indicator = BoxDecoration(
          borderRadius: BorderRadius.circular(30),
          color: widget.indicatorColor,
        );
      } else if (widget.tabStyle == TabStyle.button) {
        indicator = BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: widget.indicatorColor,
        );
      }
    } else {
      // For individual styles, we handle the indicator in _buildTab
      // and set this to null to avoid conflicts
      indicator = null;
    }

    // Create the tab bar
    final tabBar = TabBar(
      controller: _tabController,
      tabs: List.generate(_tabLabels.length, (index) => _buildTab(index)),
      isScrollable: needsScrollable,
      labelColor: widget.selectedLabelColor,
      unselectedLabelColor: widget.unselectedLabelColor,
      indicatorColor: widget.indicatorColor,
      indicatorWeight: widget.indicatorWeight,
      labelStyle: TextStyle(
        fontSize: widget.labelFontSize,
        fontWeight: widget.labelFontWeight,
      ),
      unselectedLabelStyle: TextStyle(
        fontSize: widget.labelFontSize,
        fontWeight: FontWeight.normal,
      ),
      labelPadding: const EdgeInsets.symmetric(horizontal: 16.0),
      indicator: indicator,
    );

    // Apply tab alignment
    Widget alignedTabBar;
    switch (widget.tabAlignment) {
      case TabAlignment.start:
        alignedTabBar = Align(
          alignment: Alignment.centerLeft,
          child: tabBar,
        );
        break;
      case TabAlignment.center:
        alignedTabBar = Align(
          alignment: Alignment.center,
          child: tabBar,
        );
        break;
      case TabAlignment.end:
        alignedTabBar = Align(
          alignment: Alignment.centerRight,
          child: tabBar,
        );
        break;
      case TabAlignment.fill:
        alignedTabBar = tabBar;
        break;
    }

    // Apply tab bar styling
    return Container(
      color: widget.tabBarColor,
      child: alignedTabBar,
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      physics: widget.isDisabled
          ? const NeverScrollableScrollPhysics()
          : const AlwaysScrollableScrollPhysics(),
      children: _tabContents,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Determine the effective background color based on theme
    final effectiveBackgroundColor = widget.isDarkTheme
        ? Colors.grey.shade800
        : widget.backgroundColor;

    // Create the tab container content based on tab position
    Widget tabContainerContent;
    switch (widget.tabPosition) {
      case TabPosition.top:
        tabContainerContent = Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildTabBar(),
            if (widget.showDivider)
              Divider(
                height: widget.dividerThickness,
                thickness: widget.dividerThickness,
                color: widget.dividerColor,
              ),
            Expanded(child: _buildTabContent()),
          ],
        );
        break;
      case TabPosition.bottom:
        tabContainerContent = Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(child: _buildTabContent()),
            if (widget.showDivider)
              Divider(
                height: widget.dividerThickness,
                thickness: widget.dividerThickness,
                color: widget.dividerColor,
              ),
            _buildTabBar(),
          ],
        );
        break;
      case TabPosition.left:
        tabContainerContent = Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            SizedBox(
              width: 150, // Fixed width for the tab bar
              child: _buildTabBar(),
            ),
            if (widget.showDivider)
              VerticalDivider(
                width: widget.dividerThickness,
                thickness: widget.dividerThickness,
                color: widget.dividerColor,
              ),
            Expanded(child: _buildTabContent()),
          ],
        );
        break;
      case TabPosition.right:
        tabContainerContent = Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(child: _buildTabContent()),
            if (widget.showDivider)
              VerticalDivider(
                width: widget.dividerThickness,
                thickness: widget.dividerThickness,
                color: widget.dividerColor,
              ),
            SizedBox(
              width: 150, // Fixed width for the tab bar
              child: _buildTabBar(),
            ),
          ],
        );
        break;
    }

    // Create the container with border and shadow
    final container = Container(
      width: widget.width,
      height: widget.height,
      padding: widget.padding,
      margin: widget.margin,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(26), // 0.1 opacity
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(
          widget.hasBorder
              ? widget.borderRadius - widget.borderWidth
              : widget.borderRadius,
        ),
        child: tabContainerContent,
      ),
    );

    // Apply disabled state if needed
    if (widget.isDisabled) {
      return Opacity(
        opacity: 0.5,
        child: container,
      );
    }

    return container;
  }
}
