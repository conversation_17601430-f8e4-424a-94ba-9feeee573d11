
import 'package:flutter/material.dart';
import 'package:pie_chart/pie_chart.dart';


class JsonWidgetScreen extends StatefulWidget {
  final Map<String, dynamic> jsonSource; // Can be a String or a Map

  const JsonWidgetScreen({super.key, required this.jsonSource});

  @override
  State<JsonWidgetScreen> createState() => _JsonWidgetScreenState();
}

class _JsonWidgetScreenState extends State<JsonWidgetScreen> {
  Color _parseColor(String hexColor) {
    hexColor = hexColor.toUpperCase().replaceAll("#", "");
    if (hexColor.length == 6) hexColor = "FF$hexColor";
    return Color(int.parse(hexColor, radix: 16));
  }

  MainAxisAlignment _parseMainAxisAlignment(String? value) {
    switch (value) {
      case 'MainAxisAlignment.center':
        return MainAxisAlignment.center;
      case 'MainAxisAlignment.end':
        return MainAxisAlignment.end;
      case 'MainAxisAlignment.spaceBetween':
        return MainAxisAlignment.spaceBetween;
      case 'MainAxisAlignment.spaceAround':
        return MainAxisAlignment.spaceAround;
      case 'MainAxisAlignment.spaceEvenly':
        return MainAxisAlignment.spaceEvenly;
      case 'MainAxisAlignment.start':
      default:
        return MainAxisAlignment.start;
    }
  }

  CrossAxisAlignment _parseCrossAxisAlignment(String? value) {
    switch (value) {
      case 'CrossAxisAlignment.center':
        return CrossAxisAlignment.center;
      case 'CrossAxisAlignment.end':
        return CrossAxisAlignment.end;
      case 'CrossAxisAlignment.stretch':
        return CrossAxisAlignment.stretch;
      case 'CrossAxisAlignment.baseline':
        return CrossAxisAlignment.baseline;
      case 'CrossAxisAlignment.start':
      default:
        return CrossAxisAlignment.start;
    }
  }

  Widget _buildFromJson(Map<String, dynamic> json) {
    switch (json['widget_type']) {
      case 'Column':
        return Column(
          mainAxisAlignment: _parseMainAxisAlignment(json['mainAxisAlignment']),
          crossAxisAlignment: _parseCrossAxisAlignment(json['crossAxisAlignment']),
          children: (json['children'] as List)
              .map<Widget>((c) => _buildFromJson(Map<String, dynamic>.from(c)))
              .toList(),
        );
      case 'Row':
        return Row(
          mainAxisAlignment: _parseMainAxisAlignment(json['mainAxisAlignment']),
          crossAxisAlignment: _parseCrossAxisAlignment(json['crossAxisAlignment']),
          children: (json['children'] as List)
              .map<Widget>((c) => _buildFromJson(Map<String, dynamic>.from(c)))
              .toList(),
        );
      case 'Expanded':
        return Expanded(
          flex: json['flex'] ?? 1,
          child: _buildFromJson(Map<String, dynamic>.from(json['child'])),
        );
      case 'SizedBox':
        return SizedBox(
          width: (json['width'] as num?)?.toDouble(),
          height: (json['height'] as num?)?.toDouble(),
        );
      case 'Text':
        return Text(
          json['text'],
          textAlign: _parseTextAlign(json['textAlign']),
          style: TextStyle(
            fontSize: (json['fontSize'] as num?)?.toDouble(),
            fontWeight: json['fontWeight'] == 'FontWeight.bold'
                ? FontWeight.bold
                : (json['fontWeight'] == 'FontWeight.w500'
                    ? FontWeight.w500
                    : (json['fontWeight'] == 'FontWeight.w600'
                        ? FontWeight.w600
                        : (json['fontWeight'] == 'FontWeight.w700'
                            ? FontWeight.w700
                            : FontWeight.normal))),
            color: json['color'] != null ? _parseColor(json['color']) : null,
          ),
        );
      case 'Container':
        return Container(
          width: (json['width'] as num?)?.toDouble(),
          height: (json['height'] as num?)?.toDouble(),
          color: json['color'] != null ? _parseColor(json['color']) : null,
          child: json['child'] != null ? _buildFromJson(Map<String, dynamic>.from(json['child'])) : null,
        );
      case 'Card':
        return Card(
          elevation: (json['elevation'] as num?)?.toDouble() ?? 1.0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular((json['borderRadius'] as num?)?.toDouble() ?? 8.0),
            side: BorderSide(
      color: Colors.grey,   // Border color
      width: (json['borderWidth'] as num?)?.toDouble() ??0,           // Border width
    ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildFromJson(Map<String, dynamic>.from(json['child'])),
          ),
        );
      case 'PieChart':
        if (json['dataMap'] == null || json['colorList'] == null) {
          return const Text('PieChart data missing');
        }
        final rawMap = Map<String, dynamic>.from(json['dataMap']);
        final dataMap = rawMap.map((k, v) => MapEntry(k, (v as num).toDouble()));
        final colorList = (json['colorList'] as List).map((c) => _parseColor(c)).toList();
        return PieChart(
          dataMap: dataMap,
          colorList: colorList,
          // chartType: ChartType.ring,
          chartRadius: (json['chartRadius'] as num).toDouble(),
          ringStrokeWidth: (json['ringStrokeWidth'] as num).toDouble(),
          centerWidget: Text(
            json['centerText'] ?? '',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: ((json['chartRadius'] as num).toDouble()) / 8,
            ),
          ),
          legendOptions: const LegendOptions(showLegends: false),
          chartValuesOptions: const ChartValuesOptions(showChartValues: false),
        );
      case 'Legend':
        // Just build the pre-composed layout
        final Map<String, dynamic> composition = Map<String, dynamic>.from(json['composition']);
        return _buildFromJson(composition);
      default:
        return const SizedBox();
    }
  }

  TextAlign? _parseTextAlign(String? value) {
    switch (value) {
      case 'TextAlign.center':
        return TextAlign.center;
      case 'TextAlign.right':
        return TextAlign.right;
      case 'TextAlign.left':
        return TextAlign.left;
      case 'TextAlign.justify':
        return TextAlign.justify;
      default:
        return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final Map<String, dynamic> jsonMap =  widget.jsonSource;

    return Scaffold(
      appBar: AppBar(title: const Text('UI from JSON')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: _buildFromJson(jsonMap),
      ),
    );
  }
}

